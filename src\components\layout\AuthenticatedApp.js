import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Box } from '@mui/material';
import { useFund } from '../../contexts/FundContext';

// Components
import Sidebar from './Sidebar';
import Header from './Header';
import WelcomePage from './WelcomePage';
import HomePage from '../home/<USER>';
import DashboardPage from '../dashboard/DashboardPage';
import MembersManagement from '../members/MembersManagement';
import MemberDetails from '../members/MemberDetails';
import IncomeManagement from '../income/IncomeManagement';
import ExpenseManagement from '../expenses/ExpenseManagement';
import FamiliesManagement from '../families/FamiliesManagement';
import ReportsPage from '../reports/ReportsPage';
import NewSettingsPage from '../settings/NewSettingsPage';
import FundManagementPage from '../funds/FundManagementPage';

// Helper functions for safe JSON operations
const safelyParseJSON = (key, defaultValue) => {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error(`Error parsing JSON for key ${key}:`, error);
    return defaultValue;
  }
};

const safelySaveJSON = (key, data) => {
  try {
    localStorage.setItem(key, JSON.stringify(data));
    return true;
  } catch (error) {
    console.error(`Error saving data for key ${key}:`, error);
    return false;
  }
};

const AuthenticatedApp = () => {
  // Get current fund information from context
  const { currentFundId, currentFund } = useFund();
  
  // Create fund-specific storage keys
  const MEMBERS_STORAGE_KEY_WITH_PREFIX = `savings_fund_members_${currentFundId || 'default'}`;
  const INCOME_STORAGE_KEY_WITH_PREFIX = `savings_fund_income_${currentFundId || 'default'}`;
  const EXPENSES_STORAGE_KEY_WITH_PREFIX = `savings_fund_expenses_${currentFundId || 'default'}`;
  const FAMILIES_STORAGE_KEY_WITH_PREFIX = `savings_fund_families_${currentFundId || 'default'}`;
  const WITHDRAWALS_STORAGE_KEY_WITH_PREFIX = `savings_fund_withdrawals_${currentFundId || 'default'}`;
  const LOANS_STORAGE_KEY_WITH_PREFIX = `savings_fund_loans_${currentFundId || 'default'}`;
  const FUND_SETTINGS_KEY_WITH_PREFIX = `savings_fund_settings_${currentFundId || 'default'}`;

  // Initialize state with data from localStorage using fund-specific keys
  const [members, setMembers] = useState(() => safelyParseJSON(MEMBERS_STORAGE_KEY_WITH_PREFIX, []));
  const [income, setIncome] = useState(() => safelyParseJSON(INCOME_STORAGE_KEY_WITH_PREFIX, []));
  const [expenses, setExpenses] = useState(() => safelyParseJSON(EXPENSES_STORAGE_KEY_WITH_PREFIX, []));
  const [families, setFamilies] = useState(() => safelyParseJSON(FAMILIES_STORAGE_KEY_WITH_PREFIX, []));
  const [withdrawals, setWithdrawals] = useState(() => safelyParseJSON(WITHDRAWALS_STORAGE_KEY_WITH_PREFIX, []));
  const [loans, setLoans] = useState(() => safelyParseJSON(LOANS_STORAGE_KEY_WITH_PREFIX, []));
  const [dateType, setDateType] = useState('gregorian'); // 'gregorian' or 'hijri'
  const [fundSettings, setFundSettings] = useState(() => {
    // Try to get fund settings from localStorage
    const storedSettings = safelyParseJSON(FUND_SETTINGS_KEY_WITH_PREFIX, null);
    
    // If settings exist, use them; otherwise, use current fund data as settings
    return storedSettings || {
      fundName: currentFund?.fundName || 'صندوق التوفير',
      fundFamily: currentFund?.fundFamily || '',
      generalManager: currentFund?.generalManager || '',
      treasurer: currentFund?.treasurer || '',
      collector: currentFund?.collector || ''
    };
  });

  // Save data to localStorage when it changes, using fund-specific keys
  useEffect(() => {
    safelySaveJSON(MEMBERS_STORAGE_KEY_WITH_PREFIX, members);
  }, [members, MEMBERS_STORAGE_KEY_WITH_PREFIX]);

  useEffect(() => {
    safelySaveJSON(INCOME_STORAGE_KEY_WITH_PREFIX, income);
  }, [income, INCOME_STORAGE_KEY_WITH_PREFIX]);

  useEffect(() => {
    safelySaveJSON(EXPENSES_STORAGE_KEY_WITH_PREFIX, expenses);
  }, [expenses, EXPENSES_STORAGE_KEY_WITH_PREFIX]);
  
  useEffect(() => {
    safelySaveJSON(FAMILIES_STORAGE_KEY_WITH_PREFIX, families);
  }, [families, FAMILIES_STORAGE_KEY_WITH_PREFIX]);

  useEffect(() => {
    safelySaveJSON(WITHDRAWALS_STORAGE_KEY_WITH_PREFIX, withdrawals);
  }, [withdrawals, WITHDRAWALS_STORAGE_KEY_WITH_PREFIX]);

  useEffect(() => {
    safelySaveJSON(LOANS_STORAGE_KEY_WITH_PREFIX, loans);
  }, [loans, LOANS_STORAGE_KEY_WITH_PREFIX]);

  useEffect(() => {
    safelySaveJSON(FUND_SETTINGS_KEY_WITH_PREFIX, fundSettings);
  }, [fundSettings, FUND_SETTINGS_KEY_WITH_PREFIX]);
  
  // Reload data when current fund changes
  useEffect(() => {
    if (currentFundId) {
      console.log('تحديث البيانات للصندوق:', currentFundId);
      
      // تحديث المفاتيح المرتبطة بالصندوق الحالي
      const membersKey = `savings_fund_members_${currentFundId}`;
      const incomeKey = `savings_fund_income_${currentFundId}`;
      const expensesKey = `savings_fund_expenses_${currentFundId}`;
      const familiesKey = `savings_fund_families_${currentFundId}`;
      const withdrawalsKey = `savings_fund_withdrawals_${currentFundId}`;
      const loansKey = `savings_fund_loans_${currentFundId}`;
      const settingsKey = `savings_fund_settings_${currentFundId}`;

      // تحميل البيانات من localStorage باستخدام المفاتيح المرتبطة بالصندوق الحالي
      setMembers(safelyParseJSON(membersKey, []));
      setIncome(safelyParseJSON(incomeKey, []));
      setExpenses(safelyParseJSON(expensesKey, []));
      setFamilies(safelyParseJSON(familiesKey, []));
      setWithdrawals(safelyParseJSON(withdrawalsKey, []));
      setLoans(safelyParseJSON(loansKey, []));
      setFundSettings(safelyParseJSON(settingsKey, {
        fundName: currentFund?.fundName || 'صندوق التوفير',
        fundFamily: currentFund?.fundFamily || '',
        generalManager: currentFund?.generalManager || '',
        treasurer: currentFund?.treasurer || '',
        collector: currentFund?.collector || ''
      }));
    }
  }, [currentFundId, currentFund]);

  // مراقبة تغييرات localStorage وتحديث البيانات
  useEffect(() => {
    const handleStorageChange = () => {
      if (currentFundId) {
        // تحديث جميع البيانات من localStorage
        const membersKey = `savings_fund_members_${currentFundId}`;
        const incomeKey = `savings_fund_income_${currentFundId}`;
        const expensesKey = `savings_fund_expenses_${currentFundId}`;
        const withdrawalsKey = `savings_fund_withdrawals_${currentFundId}`;
        const loansKey = `savings_fund_loans_${currentFundId}`;

        const updatedMembers = safelyParseJSON(membersKey, []);
        const updatedIncome = safelyParseJSON(incomeKey, []);
        const updatedExpenses = safelyParseJSON(expensesKey, []);
        const updatedWithdrawals = safelyParseJSON(withdrawalsKey, []);
        const updatedLoans = safelyParseJSON(loansKey, []);

        setMembers(updatedMembers);
        setIncome(updatedIncome);
        setExpenses(updatedExpenses);
        setWithdrawals(updatedWithdrawals);
        setLoans(updatedLoans);
      }
    };

    // تحديث دوري كل ثانيتين للتأكد من تحديث البيانات
    const interval = setInterval(handleStorageChange, 2000);

    return () => {
      clearInterval(interval);
    };
  }, [currentFundId]);

  // If no funds exist, show welcome page
  if (!currentFundId || !currentFund) {
    return (
      <Router>
        <Routes>
          <Route path="*" element={<WelcomePage />} />
        </Routes>
      </Router>
    );
  }

  // Toggle date type
  const toggleDateType = () => {
    setDateType(prevType => prevType === 'gregorian' ? 'hijri' : 'gregorian');
  };

  // Calculate totals
  const totalIncomeAmount = income.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0);
  const totalExpensesAmount = expenses.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0);
  const netBalance = totalIncomeAmount - totalExpensesAmount;

  // Add member function
  const addMember = (memberData) => {
    const newMember = {
      id: Date.now(),
      ...memberData,
      totalBalance: 0,
      createdAt: new Date().toISOString()
    };
    setMembers(prev => [...prev, newMember]);
  };

  // Update member function
  const updateMember = (memberId, memberData) => {
    setMembers(prev => prev.map(member => 
      member.id === memberId ? { ...member, ...memberData } : member
    ));
  };

  // Delete member function
  const deleteMember = (memberId) => {
    setMembers(prev => prev.filter(member => member.id !== memberId));
  };

  // Add income function
  const addIncome = (incomeData) => {
    const newIncome = {
      id: Date.now(),
      ...incomeData,
      createdAt: new Date().toISOString()
    };
    setIncome(prev => [...prev, newIncome]);
  };

  // Update income function
  const updateIncome = (incomeId, incomeData) => {
    setIncome(prev => prev.map(item => 
      item.id === incomeId ? { ...item, ...incomeData } : item
    ));
  };

  // Delete income function
  const deleteIncome = (incomeId) => {
    setIncome(prev => prev.filter(item => item.id !== incomeId));
  };

  // Add expense function
  const addExpense = (expenseData) => {
    const newExpense = {
      id: Date.now(),
      ...expenseData,
      createdAt: new Date().toISOString()
    };
    setExpenses(prev => [...prev, newExpense]);
  };

  // Update expense function
  const updateExpense = (expenseId, expenseData) => {
    setExpenses(prev => prev.map(item => 
      item.id === expenseId ? { ...item, ...expenseData } : item
    ));
  };

  // Delete expense function
  const deleteExpense = (expenseId) => {
    setExpenses(prev => prev.filter(item => item.id !== expenseId));
  };

  // Add family function
  const addFamily = (familyData) => {
    const newFamily = {
      id: Date.now(),
      ...familyData,
      createdAt: new Date().toISOString()
    };
    setFamilies(prev => [...prev, newFamily]);
  };

  // Update family function
  const updateFamily = (familyId, familyData) => {
    setFamilies(prev => prev.map(family => 
      family.id === familyId ? { ...family, ...familyData } : family
    ));
  };

  // Delete family function
  const deleteFamily = (familyId) => {
    setFamilies(prev => prev.filter(family => family.id !== familyId));
  };

  return (
    <Router>
      <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
        <Header 
          dateType={dateType}
          toggleDateType={toggleDateType}
          totalIncomeAmount={totalIncomeAmount}
          totalExpensesAmount={totalExpensesAmount}
          netBalance={netBalance}
          currentFund={currentFund}
        />
        <Box sx={{ display: 'flex', flex: 1 }}>
          <Sidebar />
          <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/dashboard" element={
                <DashboardPage
                  members={members}
                  income={income}
                  expenses={expenses}
                  families={families}
                  totalIncomeAmount={totalIncomeAmount}
                  totalExpensesAmount={totalExpensesAmount}
                  netBalance={netBalance}
                  dateType={dateType}
                />
              } />
              <Route path="/members" element={
                <MembersManagement
                  members={members}
                  families={families}
                  addMember={addMember}
                  updateMember={updateMember}
                  deleteMember={deleteMember}
                  dateType={dateType}
                />
              } />
              <Route path="/member/:id" element={
                <MemberDetails
                  members={members}
                  income={income}
                  expenses={expenses}
                  dateType={dateType}
                  addIncome={addIncome}
                  addExpense={addExpense}
                  updateIncome={updateIncome}
                  deleteIncome={deleteIncome}
                  updateExpense={updateExpense}
                  deleteExpense={deleteExpense}
                />
              } />
              <Route path="/income" element={
                <IncomeManagement 
                  income={income}
                  members={members}
                  addIncome={addIncome}
                  updateIncome={updateIncome}
                  deleteIncome={deleteIncome}
                  dateType={dateType}
                />
              } />
              <Route path="/expenses" element={
                <ExpenseManagement 
                  expenses={expenses}
                  members={members}
                  addExpense={addExpense}
                  updateExpense={updateExpense}
                  deleteExpense={deleteExpense}
                  dateType={dateType}
                />
              } />
              <Route path="/families" element={
                <FamiliesManagement
                  families={families}
                  members={members}
                  addFamily={addFamily}
                  updateFamily={updateFamily}
                  deleteFamily={deleteFamily}
                  addMember={addMember}
                  updateMember={updateMember}
                  deleteMember={deleteMember}
                  dateType={dateType}
                />
              } />
              <Route path="/reports" element={
                <ReportsPage 
                  members={members}
                  income={income}
                  expenses={expenses}
                  families={families}
                  withdrawals={withdrawals}
                  loans={loans}
                  dateType={dateType}
                />
              } />
              <Route path="/settings" element={
                <NewSettingsPage
                  fundSettings={fundSettings}
                  setFundSettings={setFundSettings}
                />
              } />
              <Route path="/funds" element={<FundManagementPage />} />
            </Routes>
          </Box>
        </Box>
      </Box>
    </Router>
  );
};

export default AuthenticatedApp;
