# سكريبت نسخ APK مع ترقيم
param([string]$Description = "update")

$DateTime = Get-Date -Format "yyyy-MM-dd_HH-mm"
$SourceAPK = "android\app\build\outputs\apk\debug\app-debug.apk"

# إنشاء مجلد الإصدارات إذا لم يكن موجود
if (!(Test-Path "releases")) {
    New-Item -ItemType Directory -Path "releases"
}

# تحديد رقم الإصدار
$ExistingAPKs = Get-ChildItem "releases" -Filter "savings-fund-v*.apk" | Measure-Object
$VersionNumber = $ExistingAPKs.Count + 1

$NewName = "savings-fund-v1.$VersionNumber.0-$DateTime-$Description.apk"
$TargetAPK = "releases\$NewName"

# نسخ الملف
Copy-Item $SourceAPK $TargetAPK

Write-Host "✅ تم إنشاء APK جديد!" -ForegroundColor Green
Write-Host "📱 الاسم: $NewName" -ForegroundColor Yellow
Write-Host "📍 المسار: $TargetAPK" -ForegroundColor Cyan
Write-Host "📊 الحجم: $([math]::Round((Get-Item $TargetAPK).Length / 1MB, 2)) MB" -ForegroundColor Magenta

Write-Host "`n📋 جميع الإصدارات:" -ForegroundColor Blue
Get-ChildItem "releases" -Filter "*.apk" | Sort-Object LastWriteTime -Descending | ForEach-Object {
    $size = [math]::Round($_.Length / 1MB, 2)
    Write-Host "   📱 $($_.Name) - $size MB - $($_.LastWriteTime)" -ForegroundColor White
}
