import React, { createContext, useContext, useState, useEffect } from 'react';

// Helper functions for safe JSON operations
const safelyParseJSON = (key, defaultValue) => {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error(`Error parsing JSON for key ${key}:`, error);
    return defaultValue;
  }
};

const safelySaveJSON = (key, data) => {
  try {
    localStorage.setItem(key, JSON.stringify(data));
    return true;
  } catch (error) {
    console.error(`Error saving data for key ${key}:`, error);
    return false;
  }
};

// User roles and permissions
export const USER_ROLES = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  TREASURER: 'treasurer',
  COLLECTOR: 'collector',
  VIEWER: 'viewer'
};

export const PERMISSIONS = {
  // Fund Management
  CREATE_FUND: 'create_fund',
  EDIT_FUND: 'edit_fund',
  DELETE_FUND: 'delete_fund',
  VIEW_FUND: 'view_fund',
  
  // Member Management
  ADD_MEMBER: 'add_member',
  EDIT_MEMBER: 'edit_member',
  DELETE_MEMBER: 'delete_member',
  VIEW_MEMBERS: 'view_members',
  
  // Financial Operations
  ADD_INCOME: 'add_income',
  EDIT_INCOME: 'edit_income',
  DELETE_INCOME: 'delete_income',
  VIEW_INCOME: 'view_income',
  
  ADD_EXPENSE: 'add_expense',
  EDIT_EXPENSE: 'edit_expense',
  DELETE_EXPENSE: 'delete_expense',
  VIEW_EXPENSES: 'view_expenses',
  
  // Family Management
  ADD_FAMILY: 'add_family',
  EDIT_FAMILY: 'edit_family',
  DELETE_FAMILY: 'delete_family',
  VIEW_FAMILIES: 'view_families',
  
  // Reports
  VIEW_REPORTS: 'view_reports',
  EXPORT_REPORTS: 'export_reports',
  
  // System Settings
  MANAGE_SETTINGS: 'manage_settings',
  MANAGE_USERS: 'manage_users',
  BACKUP_RESTORE: 'backup_restore'
};

// Role permissions mapping
export const ROLE_PERMISSIONS = {
  [USER_ROLES.ADMIN]: Object.values(PERMISSIONS),
  [USER_ROLES.MANAGER]: [
    PERMISSIONS.VIEW_FUND,
    PERMISSIONS.EDIT_FUND,
    PERMISSIONS.ADD_MEMBER,
    PERMISSIONS.EDIT_MEMBER,
    PERMISSIONS.DELETE_MEMBER,
    PERMISSIONS.VIEW_MEMBERS,
    PERMISSIONS.ADD_INCOME,
    PERMISSIONS.EDIT_INCOME,
    PERMISSIONS.DELETE_INCOME,
    PERMISSIONS.VIEW_INCOME,
    PERMISSIONS.ADD_EXPENSE,
    PERMISSIONS.EDIT_EXPENSE,
    PERMISSIONS.DELETE_EXPENSE,
    PERMISSIONS.VIEW_EXPENSES,
    PERMISSIONS.ADD_FAMILY,
    PERMISSIONS.EDIT_FAMILY,
    PERMISSIONS.DELETE_FAMILY,
    PERMISSIONS.VIEW_FAMILIES,
    PERMISSIONS.VIEW_REPORTS,
    PERMISSIONS.EXPORT_REPORTS,
    PERMISSIONS.MANAGE_SETTINGS
  ],
  [USER_ROLES.TREASURER]: [
    PERMISSIONS.VIEW_FUND,
    PERMISSIONS.VIEW_MEMBERS,
    PERMISSIONS.ADD_INCOME,
    PERMISSIONS.EDIT_INCOME,
    PERMISSIONS.VIEW_INCOME,
    PERMISSIONS.ADD_EXPENSE,
    PERMISSIONS.EDIT_EXPENSE,
    PERMISSIONS.VIEW_EXPENSES,
    PERMISSIONS.VIEW_FAMILIES,
    PERMISSIONS.VIEW_REPORTS,
    PERMISSIONS.EXPORT_REPORTS
  ],
  [USER_ROLES.COLLECTOR]: [
    PERMISSIONS.VIEW_FUND,
    PERMISSIONS.VIEW_MEMBERS,
    PERMISSIONS.ADD_INCOME,
    PERMISSIONS.VIEW_INCOME,
    PERMISSIONS.VIEW_FAMILIES,
    PERMISSIONS.VIEW_REPORTS
  ],
  [USER_ROLES.VIEWER]: [
    PERMISSIONS.VIEW_FUND,
    PERMISSIONS.VIEW_MEMBERS,
    PERMISSIONS.VIEW_INCOME,
    PERMISSIONS.VIEW_EXPENSES,
    PERMISSIONS.VIEW_FAMILIES,
    PERMISSIONS.VIEW_REPORTS
  ]
};

const UserContext = createContext();

export const UserProvider = ({ children }) => {
  const [users, setUsers] = useState(() => {
    const savedUsers = safelyParseJSON('savings_fund_users', []);
    return savedUsers; // لا ننشئ أي مستخدم افتراضي
  });

  const [currentUser, setCurrentUser] = useState(() => {
    const savedUser = safelyParseJSON('current_user', null);
    return savedUser; // لا نسجل دخول أي مستخدم تلقائياً
  });

  // Save users to localStorage when they change
  useEffect(() => {
    safelySaveJSON('savings_fund_users', users);
  }, [users]);

  // Save current user to localStorage when it changes
  useEffect(() => {
    safelySaveJSON('current_user', currentUser);
  }, [currentUser]);

  // لا نسجل دخول أي مستخدم تلقائياً

  // Login function
  const login = (username, password) => {
    const user = users.find(u => u.username === username && u.password === password && u.isActive);
    if (user) {
      const updatedUser = { ...user, lastLogin: new Date().toISOString() };
      setCurrentUser(updatedUser);
      
      // Update last login in users array
      setUsers(prev => prev.map(u => u.id === user.id ? updatedUser : u));
      
      return { success: true, user: updatedUser };
    }
    return { success: false, message: 'اسم المستخدم أو كلمة المرور غير صحيحة' };
  };

  // Logout function
  const logout = () => {
    setCurrentUser(null);
    localStorage.removeItem('current_user');
  };

  // Add user function
  const addUser = (userData) => {
    // Check if username already exists
    if (users.some(u => u.username === userData.username)) {
      return { success: false, message: 'اسم المستخدم موجود بالفعل' };
    }

    const newUser = {
      id: `user_${Date.now()}`,
      ...userData,
      isActive: true,
      createdAt: new Date().toISOString(),
      lastLogin: null
    };

    setUsers(prev => [...prev, newUser]);
    return { success: true, user: newUser };
  };

  // Update user function
  const updateUser = (userId, userData) => {
    setUsers(prev => prev.map(user => 
      user.id === userId ? { ...user, ...userData } : user
    ));
    
    // Update current user if it's the same user
    if (currentUser && currentUser.id === userId) {
      setCurrentUser(prev => ({ ...prev, ...userData }));
    }
    
    return { success: true };
  };

  // Delete user function
  const deleteUser = (userId) => {
    // Prevent deleting the last admin
    const admins = users.filter(u => u.role === USER_ROLES.ADMIN && u.id !== userId);
    if (admins.length === 0) {
      return { success: false, message: 'لا يمكن حذف آخر مدير في النظام' };
    }

    setUsers(prev => prev.filter(user => user.id !== userId));
    
    // Logout if current user is deleted
    if (currentUser && currentUser.id === userId) {
      logout();
    }
    
    return { success: true };
  };

  // Toggle user active status
  const toggleUserStatus = (userId) => {
    setUsers(prev => prev.map(user => 
      user.id === userId ? { ...user, isActive: !user.isActive } : user
    ));
    
    return { success: true };
  };

  // Check if user has permission
  const hasPermission = (permission) => {
    if (!currentUser) return false;
    const userPermissions = ROLE_PERMISSIONS[currentUser.role] || [];
    return userPermissions.includes(permission);
  };

  // Check if user has any of the given permissions
  const hasAnyPermission = (permissions) => {
    return permissions.some(permission => hasPermission(permission));
  };

  // Get user role display name
  const getRoleDisplayName = (role) => {
    const roleNames = {
      [USER_ROLES.ADMIN]: 'مدير النظام',
      [USER_ROLES.MANAGER]: 'مدير عام',
      [USER_ROLES.TREASURER]: 'أمين الصندوق',
      [USER_ROLES.COLLECTOR]: 'محصل',
      [USER_ROLES.VIEWER]: 'مشاهد'
    };
    return roleNames[role] || role;
  };

  const contextValue = {
    users,
    currentUser,
    login,
    logout,
    addUser,
    updateUser,
    deleteUser,
    toggleUserStatus,
    hasPermission,
    hasAnyPermission,
    getRoleDisplayName,
    USER_ROLES,
    PERMISSIONS,
    ROLE_PERMISSIONS
  };

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

// Custom hook to use the user context
export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

export default UserContext;
