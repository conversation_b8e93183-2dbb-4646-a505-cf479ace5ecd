#!/bin/bash

# تعيين الترميز للعربية
export LANG=en_US.UTF-8

echo ""
echo "========================================"
echo "    🚀 بناء تطبيق صندوق التوفير APK"
echo "========================================"
echo ""

show_menu() {
    echo "اختر طريقة البناء:"
    echo ""
    echo "1. PWA (سريع - 5 دقائق)"
    echo "2. APK أصلي (متقدم - 30 دقيقة)"
    echo "3. تشغيل التطبيق للاختبار"
    echo "4. تنظيف الملفات"
    echo "5. خروج"
    echo ""
    read -p "اختر رقم (1-5): " choice
}

build_pwa() {
    echo ""
    echo "📱 بناء PWA..."
    echo ""
    
    npm run build
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "✅ تم بناء PWA بنجاح!"
        echo ""
        echo "📋 الخطوات التالية:"
        echo "1. ارفع مجلد 'build' على Netlify.com"
        echo "2. احصل على الرابط"
        echo "3. افتح الرابط في Chrome على الجوال"
        echo "4. اضغط 'إضافة للشاشة الرئيسية'"
        echo ""
    else
        echo "❌ فشل في البناء!"
    fi
    
    read -p "اضغط Enter للمتابعة..."
}

build_apk() {
    echo ""
    echo "📱 بناء APK أصلي..."
    echo ""
    echo "⚠️  تأكد من تثبيت Android Studio أولاً!"
    echo ""
    read -p "هل تريد المتابعة؟ (y/n): " confirm
    
    if [[ $confirm != [yY] ]]; then
        return
    fi

    echo ""
    echo "🔨 بناء التطبيق..."
    npm run build
    
    if [ $? -ne 0 ]; then
        echo "❌ فشل في البناء!"
        read -p "اضغط Enter للمتابعة..."
        return
    fi

    echo ""
    echo "🔧 تهيئة Capacitor..."
    npm run cap:init
    
    if [ $? -ne 0 ]; then
        echo "⚠️  Capacitor مهيأ مسبقاً أو حدث خطأ"
    fi

    echo ""
    echo "📱 إضافة منصة Android..."
    npm run cap:add:android
    
    if [ $? -ne 0 ]; then
        echo "⚠️  منصة Android موجودة مسبقاً أو حدث خطأ"
    fi

    echo ""
    echo "📋 نسخ الملفات وفتح Android Studio..."
    npm run cap:copy
    npm run cap:open:android

    echo ""
    echo "✅ تم فتح Android Studio!"
    echo ""
    echo "📋 الخطوات التالية في Android Studio:"
    echo "1. انتظر حتى ينتهي التحميل والفهرسة"
    echo "2. Build → Build Bundle(s) / APK(s) → Build APK(s)"
    echo "3. انتظر حتى ينتهي البناء"
    echo "4. اضغط 'locate' لفتح مجلد APK"
    echo ""
    
    read -p "اضغط Enter للمتابعة..."
}

test_app() {
    echo ""
    echo "🧪 تشغيل التطبيق للاختبار..."
    echo ""
    echo "سيتم فتح التطبيق في المتصفح..."
    npm start
}

clean_files() {
    echo ""
    echo "🧹 تنظيف الملفات..."
    echo ""
    
    if [ -d "build" ]; then
        rm -rf build
        echo "✅ تم حذف مجلد build"
    fi
    
    if [ -d "android" ]; then
        rm -rf android
        echo "✅ تم حذف مجلد android"
    fi
    
    if [ -d "node_modules/.cache" ]; then
        rm -rf node_modules/.cache
        echo "✅ تم تنظيف الكاش"
    fi
    
    echo ""
    echo "✅ تم التنظيف بنجاح!"
    read -p "اضغط Enter للمتابعة..."
}

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js غير مثبت! يرجى تثبيته أولاً."
    exit 1
fi

# التحقق من وجود npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm غير مثبت! يرجى تثبيته أولاً."
    exit 1
fi

# التحقق من وجود package.json
if [ ! -f "package.json" ]; then
    echo "❌ ملف package.json غير موجود! تأكد من تشغيل الأداة في مجلد المشروع."
    exit 1
fi

# الحلقة الرئيسية
while true; do
    show_menu
    
    case $choice in
        1)
            build_pwa
            ;;
        2)
            build_apk
            ;;
        3)
            test_app
            ;;
        4)
            clean_files
            ;;
        5)
            echo ""
            echo "👋 شكراً لاستخدام أداة البناء!"
            echo ""
            exit 0
            ;;
        *)
            echo "❌ خيار غير صحيح! اختر رقم من 1 إلى 5."
            read -p "اضغط Enter للمتابعة..."
            ;;
    esac
done
