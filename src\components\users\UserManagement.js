import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Alert,
  Divider,
  Avatar,
  Grid,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Person as PersonIcon,
  AdminPanelSettings as AdminIcon,
  ManageAccounts as ManagerIcon,
  AccountBalance as TreasurerIcon,
  CollectionsBookmark as CollectorIcon,
  Visibility as ViewerIcon,
  Block as BlockIcon,
  CheckCircle as ActiveIcon
} from '@mui/icons-material';
import { useUser, USER_ROLES } from '../../contexts/UserContext';

const UserManagement = () => {
  const { 
    users, 
    currentUser, 
    addUser, 
    updateUser, 
    deleteUser, 
    toggleUserStatus, 
    getRoleDisplayName,
    hasPermission,
    PERMISSIONS 
  } = useUser();

  const [openDialog, setOpenDialog] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    fullName: '',
    email: '',
    role: USER_ROLES.VIEWER
  });
  const [errors, setErrors] = useState({});
  const [alert, setAlert] = useState({ open: false, message: '', severity: 'success' });

  // Check if user can manage users
  if (!hasPermission(PERMISSIONS.MANAGE_USERS)) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          ليس لديك صلاحية للوصول إلى إدارة المستخدمين
        </Alert>
      </Box>
    );
  }

  const handleOpenDialog = (user = null) => {
    if (user) {
      setEditingUser(user);
      setFormData({
        username: user.username,
        password: '', // Don't show password
        fullName: user.fullName,
        email: user.email,
        role: user.role
      });
    } else {
      setEditingUser(null);
      setFormData({
        username: '',
        password: '',
        fullName: '',
        email: '',
        role: USER_ROLES.VIEWER
      });
    }
    setErrors({});
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingUser(null);
    setFormData({
      username: '',
      password: '',
      fullName: '',
      email: '',
      role: USER_ROLES.VIEWER
    });
    setErrors({});
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.username.trim()) {
      newErrors.username = 'اسم المستخدم مطلوب';
    }
    
    if (!editingUser && !formData.password.trim()) {
      newErrors.password = 'كلمة المرور مطلوبة';
    }
    
    if (!formData.fullName.trim()) {
      newErrors.fullName = 'الاسم الكامل مطلوب';
    }
    
    if (!formData.email.trim()) {
      newErrors.email = 'البريد الإلكتروني مطلوب';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      let result;
      
      if (editingUser) {
        const updateData = { ...formData };
        if (!updateData.password) {
          delete updateData.password; // Don't update password if empty
        }
        result = updateUser(editingUser.id, updateData);
      } else {
        result = addUser(formData);
      }
      
      if (result.success) {
        setAlert({
          open: true,
          message: editingUser ? 'تم تحديث المستخدم بنجاح' : 'تم إضافة المستخدم بنجاح',
          severity: 'success'
        });
        handleCloseDialog();
      } else {
        setAlert({
          open: true,
          message: result.message,
          severity: 'error'
        });
      }
    }
  };

  const handleDeleteUser = (userId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
      const result = deleteUser(userId);
      if (result.success) {
        setAlert({
          open: true,
          message: 'تم حذف المستخدم بنجاح',
          severity: 'success'
        });
      } else {
        setAlert({
          open: true,
          message: result.message,
          severity: 'error'
        });
      }
    }
  };

  const handleToggleStatus = (userId) => {
    const result = toggleUserStatus(userId);
    if (result.success) {
      setAlert({
        open: true,
        message: 'تم تحديث حالة المستخدم بنجاح',
        severity: 'success'
      });
    }
  };

  const getRoleIcon = (role) => {
    const icons = {
      [USER_ROLES.ADMIN]: <AdminIcon />,
      [USER_ROLES.MANAGER]: <ManagerIcon />,
      [USER_ROLES.TREASURER]: <TreasurerIcon />,
      [USER_ROLES.COLLECTOR]: <CollectorIcon />,
      [USER_ROLES.VIEWER]: <ViewerIcon />
    };
    return icons[role] || <PersonIcon />;
  };

  const getRoleColor = (role) => {
    const colors = {
      [USER_ROLES.ADMIN]: 'error',
      [USER_ROLES.MANAGER]: 'primary',
      [USER_ROLES.TREASURER]: 'success',
      [USER_ROLES.COLLECTOR]: 'warning',
      [USER_ROLES.VIEWER]: 'default'
    };
    return colors[role] || 'default';
  };

  return (
    <Box sx={{
      p: { xs: 1, sm: 2, md: 3 },
      maxWidth: '100%',
      overflow: 'hidden'
    }}>
      {/* Header */}
      <Box sx={{
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        justifyContent: 'space-between',
        alignItems: { xs: 'stretch', sm: 'center' },
        mb: { xs: 2, sm: 3 },
        gap: { xs: 2, sm: 0 }
      }}>
        <Typography
          variant="h4"
          component="h1"
          fontWeight="bold"
          sx={{
            fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' },
            textAlign: { xs: 'center', sm: 'left' }
          }}
        >
          إدارة المستخدمين
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
          size={window.innerWidth < 600 ? "medium" : "large"}
          sx={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
            },
            minWidth: { xs: '100%', sm: 'auto' },
            py: { xs: 1.5, sm: 1 }
          }}
        >
          إضافة مستخدم جديد
        </Button>
      </Box>

      <Divider sx={{ mb: { xs: 2, sm: 3 } }} />

      {/* Statistics Cards */}
      <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mb: { xs: 2, sm: 3 } }}>
        <Grid item xs={6} sm={6} md={3}>
          <Card sx={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            minHeight: { xs: 100, sm: 120 }
          }}>
            <CardContent sx={{ p: { xs: 1.5, sm: 2 } }}>
              <Typography
                variant="h6"
                sx={{ fontSize: { xs: '0.875rem', sm: '1rem', md: '1.25rem' } }}
              >
                إجمالي المستخدمين
              </Typography>
              <Typography
                variant="h3"
                fontWeight="bold"
                sx={{ fontSize: { xs: '1.5rem', sm: '2rem', md: '3rem' } }}
              >
                {users.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} sm={6} md={3}>
          <Card sx={{
            background: 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)',
            color: 'white',
            minHeight: { xs: 100, sm: 120 }
          }}>
            <CardContent sx={{ p: { xs: 1.5, sm: 2 } }}>
              <Typography
                variant="h6"
                sx={{ fontSize: { xs: '0.875rem', sm: '1rem', md: '1.25rem' } }}
              >
                المستخدمين النشطين
              </Typography>
              <Typography
                variant="h3"
                fontWeight="bold"
                sx={{ fontSize: { xs: '1.5rem', sm: '2rem', md: '3rem' } }}
              >
                {users.filter(u => u.isActive).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} sm={6} md={3}>
          <Card sx={{
            background: 'linear-gradient(135deg, #FF9800 0%, #F57C00 100%)',
            color: 'white',
            minHeight: { xs: 100, sm: 120 }
          }}>
            <CardContent sx={{ p: { xs: 1.5, sm: 2 } }}>
              <Typography
                variant="h6"
                sx={{ fontSize: { xs: '0.875rem', sm: '1rem', md: '1.25rem' } }}
              >
                المديرين
              </Typography>
              <Typography
                variant="h3"
                fontWeight="bold"
                sx={{ fontSize: { xs: '1.5rem', sm: '2rem', md: '3rem' } }}
              >
                {users.filter(u => u.role === USER_ROLES.ADMIN).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} sm={6} md={3}>
          <Card sx={{
            background: 'linear-gradient(135deg, #2196F3 0%, #1976D2 100%)',
            color: 'white',
            minHeight: { xs: 100, sm: 120 }
          }}>
            <CardContent sx={{ p: { xs: 1.5, sm: 2 } }}>
              <Typography
                variant="h6"
                sx={{ fontSize: { xs: '0.875rem', sm: '1rem', md: '1.25rem' } }}
              >
                المديرين العامين
              </Typography>
              <Typography
                variant="h3"
                fontWeight="bold"
                sx={{ fontSize: { xs: '1.5rem', sm: '2rem', md: '3rem' } }}
              >
                {users.filter(u => u.role === USER_ROLES.MANAGER).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Users Table */}
      <Card sx={{ overflow: 'hidden' }}>
        <CardContent sx={{ p: { xs: 1, sm: 2 } }}>
          <TableContainer component={Paper} sx={{
            maxHeight: { xs: 400, sm: 600 },
            overflow: 'auto'
          }}>
            <Table stickyHeader size={window.innerWidth < 600 ? "small" : "medium"}>
              <TableHead>
                <TableRow>
                  <TableCell sx={{
                    fontSize: { xs: '0.75rem', sm: '0.875rem' },
                    fontWeight: 600,
                    minWidth: { xs: 120, sm: 150 }
                  }}>
                    المستخدم
                  </TableCell>
                  <TableCell sx={{
                    fontSize: { xs: '0.75rem', sm: '0.875rem' },
                    fontWeight: 600,
                    display: { xs: 'none', sm: 'table-cell' }
                  }}>
                    اسم المستخدم
                  </TableCell>
                  <TableCell sx={{
                    fontSize: { xs: '0.75rem', sm: '0.875rem' },
                    fontWeight: 600,
                    display: { xs: 'none', md: 'table-cell' }
                  }}>
                    البريد الإلكتروني
                  </TableCell>
                  <TableCell sx={{
                    fontSize: { xs: '0.75rem', sm: '0.875rem' },
                    fontWeight: 600
                  }}>
                    الدور
                  </TableCell>
                  <TableCell sx={{
                    fontSize: { xs: '0.75rem', sm: '0.875rem' },
                    fontWeight: 600,
                    display: { xs: 'none', sm: 'table-cell' }
                  }}>
                    الحالة
                  </TableCell>
                  <TableCell sx={{
                    fontSize: { xs: '0.75rem', sm: '0.875rem' },
                    fontWeight: 600,
                    display: { xs: 'none', lg: 'table-cell' }
                  }}>
                    آخر دخول
                  </TableCell>
                  <TableCell sx={{
                    fontSize: { xs: '0.75rem', sm: '0.875rem' },
                    fontWeight: 600
                  }}>
                    الإجراءات
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {users.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell sx={{ p: { xs: 1, sm: 2 } }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: { xs: 1, sm: 2 } }}>
                        <Avatar
                          sx={{
                            bgcolor: getRoleColor(user.role) + '.main',
                            width: { xs: 32, sm: 40 },
                            height: { xs: 32, sm: 40 }
                          }}
                        >
                          {getRoleIcon(user.role)}
                        </Avatar>
                        <Box>
                          <Typography
                            variant="body2"
                            fontWeight="bold"
                            sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
                          >
                            {user.fullName}
                          </Typography>
                          <Typography
                            variant="caption"
                            color="text.secondary"
                            sx={{
                              display: { xs: 'block', sm: 'none' },
                              fontSize: '0.7rem'
                            }}
                          >
                            {user.username}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell sx={{
                      display: { xs: 'none', sm: 'table-cell' },
                      p: { xs: 1, sm: 2 },
                      fontSize: { xs: '0.75rem', sm: '0.875rem' }
                    }}>
                      {user.username}
                    </TableCell>
                    <TableCell sx={{
                      display: { xs: 'none', md: 'table-cell' },
                      p: { xs: 1, sm: 2 },
                      fontSize: { xs: '0.75rem', sm: '0.875rem' }
                    }}>
                      {user.email}
                    </TableCell>
                    <TableCell sx={{ p: { xs: 1, sm: 2 } }}>
                      <Chip
                        icon={getRoleIcon(user.role)}
                        label={getRoleDisplayName(user.role)}
                        color={getRoleColor(user.role)}
                        size={window.innerWidth < 600 ? "small" : "medium"}
                        sx={{
                          fontSize: { xs: '0.7rem', sm: '0.8125rem' },
                          height: { xs: 24, sm: 32 }
                        }}
                      />
                    </TableCell>
                    <TableCell sx={{
                      display: { xs: 'none', sm: 'table-cell' },
                      p: { xs: 1, sm: 2 }
                    }}>
                      <Chip
                        icon={user.isActive ? <ActiveIcon /> : <BlockIcon />}
                        label={user.isActive ? 'نشط' : 'معطل'}
                        color={user.isActive ? 'success' : 'error'}
                        size={window.innerWidth < 600 ? "small" : "medium"}
                        sx={{
                          fontSize: { xs: '0.7rem', sm: '0.8125rem' },
                          height: { xs: 24, sm: 32 }
                        }}
                      />
                    </TableCell>
                    <TableCell sx={{
                      display: { xs: 'none', lg: 'table-cell' },
                      p: { xs: 1, sm: 2 },
                      fontSize: { xs: '0.75rem', sm: '0.875rem' }
                    }}>
                      {user.lastLogin
                        ? new Date(user.lastLogin).toLocaleDateString('ar-SA')
                        : 'لم يدخل بعد'
                      }
                    </TableCell>
                    <TableCell sx={{ p: { xs: 0.5, sm: 2 } }}>
                      <Box sx={{
                        display: 'flex',
                        gap: { xs: 0.5, sm: 1 },
                        flexDirection: { xs: 'column', sm: 'row' },
                        alignItems: 'center'
                      }}>
                        <Tooltip title="تعديل">
                          <IconButton
                            size="small"
                            onClick={() => handleOpenDialog(user)}
                            color="primary"
                            sx={{ minWidth: { xs: 32, sm: 'auto' } }}
                          >
                            <EditIcon sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }} />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={user.isActive ? 'تعطيل' : 'تفعيل'}>
                          <IconButton
                            size="small"
                            onClick={() => handleToggleStatus(user.id)}
                            color={user.isActive ? 'warning' : 'success'}
                            sx={{ minWidth: { xs: 32, sm: 'auto' } }}
                          >
                            {user.isActive ?
                              <BlockIcon sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }} /> :
                              <ActiveIcon sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }} />
                            }
                          </IconButton>
                        </Tooltip>
                        {user.id !== currentUser?.id && (
                          <Tooltip title="حذف">
                            <IconButton
                              size="small"
                              onClick={() => handleDeleteUser(user.id)}
                              color="error"
                              sx={{ minWidth: { xs: 32, sm: 'auto' } }}
                            >
                              <DeleteIcon sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }} />
                            </IconButton>
                          </Tooltip>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Add/Edit User Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingUser ? 'تعديل المستخدم' : 'إضافة مستخدم جديد'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="اسم المستخدم"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                error={!!errors.username}
                helperText={errors.username}
                disabled={!!editingUser}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={editingUser ? 'كلمة المرور الجديدة (اتركها فارغة للاحتفاظ بالحالية)' : 'كلمة المرور'}
                name="password"
                type="password"
                value={formData.password}
                onChange={handleInputChange}
                error={!!errors.password}
                helperText={errors.password}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="الاسم الكامل"
                name="fullName"
                value={formData.fullName}
                onChange={handleInputChange}
                error={!!errors.fullName}
                helperText={errors.fullName}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="البريد الإلكتروني"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                error={!!errors.email}
                helperText={errors.email}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>الدور</InputLabel>
                <Select
                  name="role"
                  value={formData.role}
                  onChange={handleInputChange}
                  label="الدور"
                >
                  {Object.values(USER_ROLES).map((role) => (
                    <MenuItem key={role} value={role}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {getRoleIcon(role)}
                        {getRoleDisplayName(role)}
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>إلغاء</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingUser ? 'تحديث' : 'إضافة'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Alert */}
      {alert.open && (
        <Alert 
          severity={alert.severity} 
          onClose={() => setAlert({ ...alert, open: false })}
          sx={{ mt: 2 }}
        >
          {alert.message}
        </Alert>
      )}
    </Box>
  );
};

export default UserManagement;
