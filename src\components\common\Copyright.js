import React from 'react';
import { Box, Typography, Divider } from '@mui/material';
import { Copyright as CopyrightIcon, Email as EmailIcon } from '@mui/icons-material';

const Copyright = ({ variant = 'default', showDivider = true }) => {
  const currentYear = new Date().getFullYear();

  const styles = {
    default: {
      container: {
        mt: 4,
        mb: 2,
        textAlign: 'center',
        '@media print': {
          mt: 2,
          mb: 1
        }
      },
      text: {
        color: 'text.secondary',
        fontSize: '0.875rem',
        fontWeight: 500,
        '@media print': {
          fontSize: '0.75rem',
          color: '#666 !important'
        }
      },
      email: {
        color: 'primary.main',
        fontSize: '0.8rem',
        fontWeight: 400,
        '@media print': {
          fontSize: '0.7rem',
          color: '#1976d2 !important'
        }
      }
    },
    footer: {
      container: {
        py: 2,
        px: 3,
        textAlign: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        borderRadius: '12px 12px 0 0',
        '@media print': {
          background: '#667eea !important',
          borderRadius: 0,
          py: 1,
          px: 1
        }
      },
      text: {
        color: 'white',
        fontSize: '0.875rem',
        fontWeight: 500,
        textShadow: '0 1px 2px rgba(0,0,0,0.3)',
        '@media print': {
          fontSize: '0.75rem',
          textShadow: 'none'
        }
      },
      email: {
        color: 'rgba(255,255,255,0.9)',
        fontSize: '0.8rem',
        fontWeight: 400,
        '@media print': {
          fontSize: '0.7rem'
        }
      }
    },
    compact: {
      container: {
        mt: 2,
        mb: 1,
        textAlign: 'center',
        '@media print': {
          mt: 1,
          mb: 0.5
        }
      },
      text: {
        color: 'text.secondary',
        fontSize: '0.75rem',
        fontWeight: 400,
        '@media print': {
          fontSize: '0.65rem',
          color: '#666 !important'
        }
      },
      email: {
        color: 'primary.main',
        fontSize: '0.7rem',
        fontWeight: 400,
        '@media print': {
          fontSize: '0.6rem',
          color: '#1976d2 !important'
        }
      }
    }
  };

  const currentStyle = styles[variant] || styles.default;

  return (
    <>
      {showDivider && variant === 'default' && (
        <Divider sx={{ 
          my: 3,
          '@media print': {
            my: 1,
            borderColor: '#ddd'
          }
        }} />
      )}
      
      <Box sx={currentStyle.container}>
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          gap: 1,
          mb: 0.5,
          '@media print': {
            mb: 0.25
          }
        }}>
          <CopyrightIcon sx={{ 
            fontSize: variant === 'compact' ? '1rem' : '1.2rem',
            '@media print': {
              fontSize: '0.9rem'
            }
          }} />
          <Typography sx={currentStyle.text}>
            {currentYear} جميع الحقوق محفوظة
          </Typography>
        </Box>
        
        <Typography sx={currentStyle.text}>
          الأستاذ / ناصر مسعود آل مستنير
        </Typography>
        
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          gap: 0.5,
          mt: 0.5,
          '@media print': {
            mt: 0.25
          }
        }}>
          <EmailIcon sx={{ 
            fontSize: variant === 'compact' ? '0.9rem' : '1rem',
            color: variant === 'footer' ? 'rgba(255,255,255,0.9)' : 'primary.main',
            '@media print': {
              fontSize: '0.8rem'
            }
          }} />
          <Typography sx={currentStyle.email}>
            <EMAIL>
          </Typography>
        </Box>
      </Box>
    </>
  );
};

export default Copyright;
