import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import reportWebVitals from './reportWebVitals';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import { prefixer } from 'stylis';
import { CacheProvider } from '@emotion/react';
import createCache from '@emotion/cache';

// مسح جميع البيانات المخزنة لبدء التطبيق من الصفر
const clearAllData = () => {
  // مسح localStorage بالكامل
  localStorage.clear();

  // مسح sessionStorage
  sessionStorage.clear();

  console.log('تم مسح جميع البيانات - التطبيق يبدأ من الصفر');
};

// تنفيذ مسح جميع البيانات
clearAllData();

// RTL setup for Arabic support
const cacheRtl = createCache({
  key: 'muirtl',
  stylisPlugins: [prefixer],
  prepend: true
});

// Create RTL theme with mobile optimizations
const theme = createTheme({
  direction: 'rtl',
  typography: {
    fontFamily: "'Tajawal', 'Roboto', sans-serif",
    // Mobile responsive typography
    h4: {
      '@media (max-width:768px)': {
        fontSize: '1.5rem',
      },
      '@media (max-width:480px)': {
        fontSize: '1.3rem',
      },
    },
    h5: {
      '@media (max-width:768px)': {
        fontSize: '1.25rem',
      },
      '@media (max-width:480px)': {
        fontSize: '1.1rem',
      },
    },
    h6: {
      '@media (max-width:768px)': {
        fontSize: '1.1rem',
      },
      '@media (max-width:480px)': {
        fontSize: '1rem',
      },
    },
  },
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#4caf50',
    },
  },
  components: {
    // Mobile optimized components
    MuiButton: {
      styleOverrides: {
        root: {
          '@media (max-width:768px)': {
            padding: '6px 12px',
            fontSize: '0.8rem',
            minHeight: '36px',
          },
          '@media (max-width:480px)': {
            padding: '4px 8px',
            fontSize: '0.75rem',
            minHeight: '32px',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          '@media (max-width:768px)': {
            margin: '4px',
          },
        },
      },
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          '@media (max-width:768px)': {
            padding: '12px',
          },
          '@media (max-width:480px)': {
            padding: '8px',
          },
        },
      },
    },
  },
});

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <CacheProvider value={cacheRtl}>
      <ThemeProvider theme={theme}>
        <App />
      </ThemeProvider>
    </CacheProvider>
  </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
