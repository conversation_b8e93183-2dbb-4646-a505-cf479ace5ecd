# 📱 دليل تحويل التطبيق إلى APK

## 🚀 الطريقة الأولى: Capacitor (الأفضل)

### المتطلبات:
- Node.js
- Android Studio
- Java JDK 8+

### الخطوات:

#### 1. تثبيت المكتبات المطلوبة:
```bash
npm install @capacitor/core @capacitor/cli @capacitor/android
```

#### 2. إعداد Capacitor:
```bash
npx cap init "صندوق التوفير" "com.savingsfund.app"
```

#### 3. بناء المشروع:
```bash
npm run build
```

#### 4. إضافة منصة Android:
```bash
npx cap add android
```

#### 5. نسخ الملفات:
```bash
npx cap copy
```

#### 6. فتح Android Studio:
```bash
npx cap open android
```

#### 7. بناء APK:
- في Android Studio: Build > Build Bundle(s) / APK(s) > Build APK(s)

---

## 🌐 الطريقة الثانية: PWA (الأسرع)

### الخطوات:
1. بناء المشروع: `npm run build`
2. رفع المجلد `build` على خادم HTTPS
3. فتح الموقع على الجوال
4. إضافة إلى الشاشة الرئيسية

---

## 📦 الطريقة الثالثة: Cordova

### الخطوات:
```bash
npm install -g cordova
cordova create SavingsFundApp com.savingsfund.app "صندوق التوفير"
# نسخ ملفات build إلى www
cordova platform add android
cordova build android
```

---

## 🛠️ Scripts المتاحة:

- `npm run build:android` - بناء وفتح Android Studio
- `npm run build:mobile` - بناء ومزامنة الملفات
- `npm run cap:sync` - مزامنة التغييرات

---

## 📋 ملاحظات مهمة:

1. **Android Studio**: مطلوب لبناء APK
2. **Java JDK**: يجب تثبيت الإصدار 8 أو أحدث
3. **Android SDK**: يتم تثبيته مع Android Studio
4. **الأذونات**: قد تحتاج لإضافة أذونات في AndroidManifest.xml

---

## 🔧 حل المشاكل الشائعة:

### مشكلة Java:
```bash
export JAVA_HOME=/path/to/java
```

### مشكلة Android SDK:
```bash
export ANDROID_HOME=/path/to/android-sdk
```

### تحديث Capacitor:
```bash
npx cap sync
```
