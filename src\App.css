.App {
  text-align: right;
  direction: rtl;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.app-container {
  display: flex;
  height: 100vh;
  direction: rtl;
  text-align: right;
}

.content-area {
  flex-grow: 1;
  padding: 16px;
  overflow-y: auto;
  margin-right: 240px; /* لإضافة مساحة للقائمة الجانبية */
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
    height: 100vh;
  }

  .content-area {
    margin-right: 0;
    padding: 8px;
    flex: 1;
    overflow-y: auto;
  }
}

@media (max-width: 480px) {
  .content-area {
    padding: 4px;
  }
}

.card {
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.icon-button {
  transition: transform 0.2s;
}

.icon-button:hover {
  transform: scale(1.1);
}

/* Animation for dashboard widgets */
.animate-card {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom styles for number inputs to ensure they display Arabic numbers */
input[type="number"] {
  font-family: 'Tajawal', sans-serif;
}

/* إضافة دعم RTL للعناصر الأخرى */
.MuiTableCell-head, .MuiTableCell-body {
  text-align: right !important;
}

.MuiFormControl-root {
  text-align: right !important;
}

/* تعديل لعناصر المادة يو آي بحيث تدعم RTL */
button.MuiButton-root {
  direction: rtl;
}

.MuiInputBase-root {
  text-align: right;
}

/* تعديل للقوائم المنسدلة */
.MuiSelect-select {
  padding-right: 14px !important;
  padding-left: 32px !important;
  text-align: right !important;
}

/* خصائص إضافية للاتجاه من اليمين لليسار */
.MuiMenuItem-root {
  justify-content: flex-start;
  text-align: right;
}
