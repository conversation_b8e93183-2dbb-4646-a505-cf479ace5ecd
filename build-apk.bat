@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🚀 بناء تطبيق صندوق التوفير APK
echo ========================================
echo.

:menu
echo اختر طريقة البناء:
echo.
echo 1. PWA (سريع - 5 دقائق)
echo 2. APK أصلي (متقدم - 30 دقيقة)
echo 3. تشغيل التطبيق للاختبار
echo 4. تنظيف الملفات
echo 5. خروج
echo.
set /p choice="اختر رقم (1-5): "

if "%choice%"=="1" goto pwa
if "%choice%"=="2" goto apk
if "%choice%"=="3" goto test
if "%choice%"=="4" goto clean
if "%choice%"=="5" goto exit
goto menu

:pwa
echo.
echo 📱 بناء PWA...
echo.
call npm run build
if %errorlevel% neq 0 (
    echo ❌ فشل في البناء!
    pause
    goto menu
)
echo.
echo ✅ تم بناء PWA بنجاح!
echo.
echo 📋 الخطوات التالية:
echo 1. ارفع مجلد 'build' على Netlify.com
echo 2. احصل على الرابط
echo 3. افتح الرابط في Chrome على الجوال
echo 4. اضغط "إضافة للشاشة الرئيسية"
echo.
pause
goto menu

:apk
echo.
echo 📱 بناء APK أصلي...
echo.
echo ⚠️  تأكد من تثبيت Android Studio أولاً!
echo.
set /p confirm="هل تريد المتابعة؟ (y/n): "
if /i not "%confirm%"=="y" goto menu

echo.
echo 🔨 بناء التطبيق...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ فشل في البناء!
    pause
    goto menu
)

echo.
echo 🔧 تهيئة Capacitor...
call npm run cap:init
if %errorlevel% neq 0 (
    echo ⚠️  Capacitor مهيأ مسبقاً أو حدث خطأ
)

echo.
echo 📱 إضافة منصة Android...
call npm run cap:add:android
if %errorlevel% neq 0 (
    echo ⚠️  منصة Android موجودة مسبقاً أو حدث خطأ
)

echo.
echo 📋 نسخ الملفات وفتح Android Studio...
call npm run cap:copy
call npm run cap:open:android

echo.
echo ✅ تم فتح Android Studio!
echo.
echo 📋 الخطوات التالية في Android Studio:
echo 1. انتظر حتى ينتهي التحميل والفهرسة
echo 2. Build → Build Bundle(s) / APK(s) → Build APK(s)
echo 3. انتظر حتى ينتهي البناء
echo 4. اضغط 'locate' لفتح مجلد APK
echo.
pause
goto menu

:test
echo.
echo 🧪 تشغيل التطبيق للاختبار...
echo.
echo سيتم فتح التطبيق في المتصفح...
call npm start
goto menu

:clean
echo.
echo 🧹 تنظيف الملفات...
echo.
if exist "build" (
    rmdir /s /q "build"
    echo ✅ تم حذف مجلد build
)
if exist "android" (
    rmdir /s /q "android"
    echo ✅ تم حذف مجلد android
)
if exist "node_modules\.cache" (
    rmdir /s /q "node_modules\.cache"
    echo ✅ تم تنظيف الكاش
)
echo.
echo ✅ تم التنظيف بنجاح!
pause
goto menu

:exit
echo.
echo 👋 شكراً لاستخدام أداة البناء!
echo.
pause
exit

:error
echo.
echo ❌ حدث خطأ! تأكد من:
echo 1. تثبيت Node.js
echo 2. تشغيل 'npm install' أولاً
echo 3. وجود اتصال بالإنترنت
echo.
pause
goto menu
