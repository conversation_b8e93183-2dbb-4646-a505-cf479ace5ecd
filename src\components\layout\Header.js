import React from 'react';
import {
  A<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Box,
  IconButton,
  Tooltip,
  Badge,
  Chip,
  Button,
  Paper,
  Switch,
  FormControlLabel,
  Menu,
  MenuItem,
  Divider,
  Avatar,
  Card,
  CardContent
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  CalendarToday as CalendarIcon,
  AccountBalance as BalanceIcon,
  TrendingUp as IncomeIcon,
  TrendingDown as ExpenseIcon,
  SwapHoriz as SwapIcon,
  DateRange as DateIcon,
  EventNote as HijriIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon,
  Add as AddIcon,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  Dashboard as DashboardIcon,
  Logout as LogoutIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import moment from 'moment';
import momentHijri from 'moment-hijri';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { formatCurrency } from '../../utils/formatters';

const StyledAppBar = styled(AppBar)(({ theme }) => ({
  background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%)',
  backdropFilter: 'blur(20px)',
  color: theme.palette.text.primary,
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  zIndex: 1300, // أعلى من الشريط الجانبي
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',
    pointerEvents: 'none',
  }
}));

const StyledToolbar = styled(Toolbar)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: theme.spacing(1, 4),
  minHeight: '80px',
  position: 'relative',
  zIndex: 2,
  [theme.breakpoints.down('md')]: {
    padding: theme.spacing(0.5, 1),
    minHeight: '60px',
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing(0.5),
  },
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(0.5),
    minHeight: '50px',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
}));

const GlassCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.1)',
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: '20px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  padding: theme.spacing(1, 2),
  margin: theme.spacing(0, 0.5),
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
  },
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(0.5, 1),
    margin: theme.spacing(0.25),
  },
}));

const UniformCard = styled(Paper)(({ theme, cardcolor }) => ({
  background: 'rgba(255, 255, 255, 0.15)',
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: '15px',
  padding: theme.spacing(1, 2),
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
  minHeight: '50px',
  minWidth: 'fit-content',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 6px 25px rgba(0, 0, 0, 0.15)',
  },
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(0.75, 1.5),
    minHeight: '45px',
  },
}));

const StatChip = styled(Chip)(({ theme, chipcolor }) => ({
  background: chipcolor ? `linear-gradient(135deg, ${chipcolor} 0%, ${chipcolor}dd 100%)` : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white',
  fontWeight: 'bold',
  borderRadius: '15px',
  boxShadow: chipcolor ? `0 4px 15px ${chipcolor}40` : '0 4px 15px rgba(102, 126, 234, 0.3)',
  height: '40px',
  minWidth: '120px',
  '& .MuiChip-icon': {
    color: 'white',
    fontSize: '1.2rem',
  },
  '& .MuiChip-label': {
    fontSize: '0.85rem',
    fontWeight: '600',
    padding: '0 12px',
  },
  [theme.breakpoints.down('sm')]: {
    height: '36px',
    minWidth: '100px',
    '& .MuiChip-label': {
      fontSize: '0.75rem',
      padding: '0 8px',
    },
  },
}));

const FundButton = styled(Button)(({ theme }) => ({
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white',
  fontWeight: 'bold',
  borderRadius: '15px',
  padding: theme.spacing(1, 2),
  boxShadow: '0 6px 20px rgba(102, 126, 234, 0.3)',
  textTransform: 'none',
  minHeight: '40px',
  minWidth: '100px',
  '&:hover': {
    background: 'linear-gradient(135deg, #764ba2 0%, #667eea 100%)',
    boxShadow: '0 8px 25px rgba(102, 126, 234, 0.4)',
    transform: 'translateY(-2px)',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '0.8rem',
    padding: theme.spacing(0.75, 1.5),
    minHeight: '36px',
    minWidth: '80px',
  },
}));

const SwitchCard = styled(Paper)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.15)',
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: '15px',
  padding: theme.spacing(0.5, 1.5),
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
  minHeight: '50px',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 6px 25px rgba(0, 0, 0, 0.15)',
  },
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(0.5, 1),
    minHeight: '45px',
  },
}));

function Header({
  dateType,
  toggleDateType,
  totalIncomeAmount,
  totalExpensesAmount,
  netBalance,
  currentFund
}) {
  const navigate = useNavigate();
  const { logout, currentUser } = useAuth();

  // Format for currency - استخدام الدالة المحسنة
  const formatCurrencyLocal = (amount) => {
    return formatCurrency(amount);
  };

  // Get current date in Gregorian format
  const getCurrentGregorianDate = () => {
    return moment().locale('ar').format('dddd، D MMMM YYYY');
  };

  // Get current date in Hijri format
  const getCurrentHijriDate = () => {
    return momentHijri().locale('ar').format('iYYYY/iMM/iDD');
  };

  // State for funds dropdown menu
  const [fundsMenuAnchorEl, setFundsMenuAnchorEl] = React.useState(null);
  const [funds, setFunds] = React.useState([]);
  
  // Load funds list
  React.useEffect(() => {
    try {
      const storedFunds = JSON.parse(localStorage.getItem('savings_funds_list') || '[]');
      setFunds(storedFunds);
    } catch (error) {
      console.error('Error loading funds list:', error);
      setFunds([]);
    }
  }, []);
  
  // Handle opening funds menu
  const handleOpenFundsMenu = (event) => {
    setFundsMenuAnchorEl(event.currentTarget);
  };
  
  // Handle closing funds menu
  const handleCloseFundsMenu = () => {
    setFundsMenuAnchorEl(null);
  };
  
  // Handle fund selection
  const handleSelectFund = (fundId) => {
    // Save selected fund to localStorage
    localStorage.setItem('current_fund_id', fundId);
    // Reload the page to apply changes
    window.location.reload();
    handleCloseFundsMenu();
  };
  
  // Handle navigation to funds selector
  const handleGoToFunds = () => {
    navigate('/');
  };

  // Handle logout
  const handleLogout = () => {
    if (window.confirm('هل أنت متأكد من تسجيل الخروج؟')) {
      logout();
    }
  };

  return (
    <StyledAppBar position="sticky" sx={{ zIndex: 1200 }}>
      <StyledToolbar sx={{
        justifyContent: 'space-between',
        position: 'relative',
        px: { xs: 1, sm: 2 },
        minHeight: { xs: '56px', sm: '64px' }
      }}>
        {/* Left Side - Manager Icon Only */}
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          order: { xs: 3, sm: 1 }
        }}>
          {/* Manager Info */}
          <Tooltip title={`المدير العام: ${currentFund?.generalManager || 'غير محدد'}`}>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 0.5,
              bgcolor: 'rgba(76, 175, 80, 0.9)',
              px: { xs: 1, sm: 1.5 },
              py: { xs: 0.5, sm: 0.8 },
              borderRadius: '12px',
              backdropFilter: 'blur(10px)',
              border: '2px solid rgba(76, 175, 80, 0.3)',
              boxShadow: '0 4px 15px rgba(76, 175, 80, 0.3)',
              transition: 'all 0.3s ease',
              cursor: 'pointer',
              '&:hover': {
                transform: 'scale(1.05)',
                boxShadow: '0 6px 20px rgba(76, 175, 80, 0.4)'
              }
            }}>
              <PersonIcon sx={{ color: 'white', fontSize: { xs: 18, sm: 22 } }} />
              <Typography
                variant="body2"
                sx={{
                  color: 'white',
                  fontWeight: 'bold',
                  fontSize: { xs: '0.7rem', sm: '0.8rem' },
                  textShadow: '0 1px 2px rgba(0,0,0,0.3)',
                  whiteSpace: 'nowrap',
                  display: { xs: 'none', sm: 'block' }
                }}
              >
                المدير العام
              </Typography>
            </Box>
          </Tooltip>
        </Box>

        {/* Center - Financial Summary Cards */}
        <Box sx={{
          display: 'flex',
          gap: { xs: 0.5, sm: 1 },
          alignItems: 'center',
          transition: 'all 0.3s ease',
          flexWrap: 'nowrap',
          justifyContent: 'center',
          flex: 1,
          mx: { xs: 1, sm: 2 },
          order: { xs: 1, sm: 2 },
          '&:hover .summary-cards': {
            transform: { xs: 'none', sm: 'translateY(-2px)' },
            opacity: 1
          }
        }}>
          {/* Income Card */}
          <Box className="summary-cards" sx={{
            bgcolor: 'rgba(76, 175, 80, 0.9)',
            px: { xs: 0.8, sm: 1.5 },
            py: { xs: 0.6, sm: 0.8 },
            borderRadius: { xs: '10px', sm: '12px' },
            display: 'flex',
            alignItems: 'center',
            gap: { xs: 0.5, sm: 0.8 },
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.2)',
            minWidth: { xs: '80px', sm: '120px' },
            flex: { xs: '1 1 0', sm: '0 0 auto' },
            transition: 'all 0.3s ease',
            opacity: 0.9,
            '&:hover': {
              opacity: 1,
              transform: { xs: 'none', sm: 'scale(1.05)' }
            }
          }}>
            <IncomeIcon sx={{
              color: 'white',
              fontSize: { xs: 14, sm: 18 },
              display: { xs: 'block', sm: 'block' }
            }} />
            <Box sx={{ minWidth: 0, flex: 1 }}>
              <Typography variant="caption" sx={{
                color: 'rgba(255,255,255,0.9)',
                display: 'block',
                lineHeight: 1.1,
                fontSize: { xs: '0.6rem', sm: '0.7rem' },
                textAlign: 'center'
              }}>
                الإيرادات
              </Typography>
              <Typography variant="body2" sx={{
                color: 'white',
                fontWeight: 'bold',
                lineHeight: 1.1,
                fontSize: { xs: '0.7rem', sm: '0.8rem' },
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                textAlign: 'center'
              }}>
                {formatCurrencyLocal(totalIncomeAmount)}
              </Typography>
            </Box>
          </Box>

          {/* Expenses Card */}
          <Box className="summary-cards" sx={{
            bgcolor: 'rgba(244, 67, 54, 0.9)',
            px: { xs: 0.8, sm: 1.5 },
            py: { xs: 0.6, sm: 0.8 },
            borderRadius: { xs: '10px', sm: '12px' },
            display: 'flex',
            alignItems: 'center',
            gap: { xs: 0.5, sm: 0.8 },
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.2)',
            minWidth: { xs: '80px', sm: '120px' },
            flex: { xs: '1 1 0', sm: '0 0 auto' },
            transition: 'all 0.3s ease',
            opacity: 0.9,
            '&:hover': {
              opacity: 1,
              transform: { xs: 'none', sm: 'scale(1.05)' }
            }
          }}>
            <ExpenseIcon sx={{
              color: 'white',
              fontSize: { xs: 14, sm: 18 },
              display: { xs: 'block', sm: 'block' }
            }} />
            <Box sx={{ minWidth: 0, flex: 1 }}>
              <Typography variant="caption" sx={{
                color: 'rgba(255,255,255,0.9)',
                display: 'block',
                lineHeight: 1.1,
                fontSize: { xs: '0.6rem', sm: '0.7rem' },
                textAlign: 'center'
              }}>
                المصروفات
              </Typography>
              <Typography variant="body2" sx={{
                color: 'white',
                fontWeight: 'bold',
                lineHeight: 1.1,
                fontSize: { xs: '0.7rem', sm: '0.8rem' },
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                textAlign: 'center'
              }}>
                {formatCurrencyLocal(totalExpensesAmount)}
              </Typography>
            </Box>
          </Box>

          {/* Balance Card */}
          <Box className="summary-cards" sx={{
            bgcolor: netBalance >= 0 ? 'rgba(103, 58, 183, 0.9)' : 'rgba(255, 152, 0, 0.9)',
            px: { xs: 0.8, sm: 1.5 },
            py: { xs: 0.6, sm: 0.8 },
            borderRadius: { xs: '10px', sm: '12px' },
            display: 'flex',
            alignItems: 'center',
            gap: { xs: 0.5, sm: 0.8 },
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.2)',
            minWidth: { xs: '80px', sm: '120px' },
            flex: { xs: '1 1 0', sm: '0 0 auto' },
            transition: 'all 0.3s ease',
            opacity: 0.9,
            '&:hover': {
              opacity: 1,
              transform: { xs: 'none', sm: 'scale(1.05)' }
            }
          }}>
            <BalanceIcon sx={{
              color: 'white',
              fontSize: { xs: 14, sm: 18 },
              display: { xs: 'block', sm: 'block' }
            }} />
            <Box sx={{ minWidth: 0, flex: 1 }}>
              <Typography variant="caption" sx={{
                color: 'rgba(255,255,255,0.9)',
                display: 'block',
                lineHeight: 1.1,
                fontSize: { xs: '0.6rem', sm: '0.7rem' },
                textAlign: 'center'
              }}>
                الرصيد
              </Typography>
              <Typography variant="body2" sx={{
                color: 'white',
                fontWeight: 'bold',
                lineHeight: 1.1,
                fontSize: { xs: '0.7rem', sm: '0.8rem' },
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                textAlign: 'center'
              }}>
                {formatCurrencyLocal(netBalance)}
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Right Side - Logout Button */}
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          order: { xs: 2, sm: 3 },
          width: { xs: 'auto', sm: '120px' },
          justifyContent: 'flex-end'
        }}>
          {/* Logout Button */}
          <Tooltip title="تسجيل الخروج">
            <IconButton
              onClick={handleLogout}
              sx={{
                bgcolor: 'rgba(244, 67, 54, 0.9)',
                color: 'white',
                border: '2px solid rgba(244, 67, 54, 0.3)',
                boxShadow: '0 4px 15px rgba(244, 67, 54, 0.3)',
                backdropFilter: 'blur(10px)',
                borderRadius: '12px',
                p: { xs: 0.8, sm: 1 },
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: 'rgba(244, 67, 54, 1)',
                  transform: 'scale(1.1)',
                  boxShadow: '0 6px 20px rgba(244, 67, 54, 0.4)'
                }
              }}
            >
              <LogoutIcon sx={{ fontSize: { xs: 16, sm: 18 } }} />
            </IconButton>
          </Tooltip>
        </Box>
      </StyledToolbar>
    </StyledAppBar>
  );
}

export default Header;
