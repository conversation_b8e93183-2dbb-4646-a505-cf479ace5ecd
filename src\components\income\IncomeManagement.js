import React, { useState } from 'react';
import { formatCurrency } from '../../utils/formatters';
import { 
  Box, 
  Typography, 
  Button, 
  Paper, 
  Grid,
  TextField,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  IconButton,
  Divider,
  InputAdornment,
  FormHelperText,
  RadioGroup,
  Radio,
  FormControlLabel,
  FormLabel,
  Checkbox,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import { 
  Add as AddIcon,
  TrendingUp as IncomeIcon,
  CalendarToday as DateIcon,
  Search as SearchIcon,
  AccountBalance as BankIcon,
  Person as PersonIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Group as GroupIcon,
  CheckBox as CheckBoxIcon,
  Check as CheckIcon
} from '@mui/icons-material';
import moment from 'moment';
import momentHijri from 'moment-hijri';
import { useNavigate } from 'react-router-dom';

const IncomeManagement = ({ members, income, addIncome, dateType, updateIncome, deleteIncome }) => {
  const navigate = useNavigate();
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');
  const [newIncome, setNewIncome] = useState({
    memberId: '',
    amount: '',
    bank: '',
    notes: '',
    incomeType: 'individual', // 'all', 'multiple', 'individual'
    memberIds: []
  });
  const [errors, setErrors] = useState({});
  const [editMode, setEditMode] = useState(false);
  const [currentIncomeId, setCurrentIncomeId] = useState(null);
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [incomeToDelete, setIncomeToDelete] = useState(null);

  // Format date based on type (Gregorian or Hijri)
  const formatDate = (dateString) => {
    if (dateType === 'gregorian') {
      return moment(dateString).locale('ar').format('YYYY/MM/DD - hh:mm a');
    } else {
      return momentHijri(dateString).locale('ar').format('iYYYY/iMM/iDD - hh:mm a');
    }
  };

  // Format currency
  const formatCurrencyLocal = (amount) => {
    return formatCurrency(amount);
  };

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setNewIncome({
      memberId: '',
      amount: '',
      bank: '',
      notes: '',
      incomeType: 'individual',
      memberIds: []
    });
    setErrors({});
    setEditMode(false);
    setCurrentIncomeId(null);
  };
  
  // Handle delete confirmation dialog
  const handleOpenDeleteConfirm = (incomeId) => {
    setIncomeToDelete(incomeId);
    setConfirmDeleteOpen(true);
  };

  const handleCloseDeleteConfirm = () => {
    setConfirmDeleteOpen(false);
    setIncomeToDelete(null);
  };

  const handleDeleteIncome = () => {
    if (incomeToDelete && deleteIncome) {
      deleteIncome(incomeToDelete);
      handleCloseDeleteConfirm();
    }
  };
  
  // Handle edit income
  const handleEditIncome = (incomeId) => {
    const incomeToEdit = income.find(item => item.id === incomeId);
    if (incomeToEdit) {
      setCurrentIncomeId(incomeId);
      setNewIncome({
        memberId: incomeToEdit.memberId || '',
        amount: incomeToEdit.amount || '',
        bank: incomeToEdit.bank || '',
        notes: incomeToEdit.notes || '',
        incomeType: incomeToEdit.incomeType || 'individual',
        memberIds: incomeToEdit.memberIds || []
      });
      setEditMode(true);
      setOpen(true);
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    // Validate amount for all income types
    if (!newIncome.amount) {
      newErrors.amount = 'الرجاء إدخال المبلغ';
    } else if (isNaN(newIncome.amount)) {
      newErrors.amount = 'الرجاء إدخال مبلغ صحيح';
    }
    
    // Validate based on income type
    if (newIncome.incomeType === 'individual') {
      if (!newIncome.memberId) {
        newErrors.memberId = 'الرجاء اختيار العضو';
      }
    } else if (newIncome.incomeType === 'multiple') {
      if (!newIncome.memberIds || newIncome.memberIds.length === 0) {
        newErrors.memberIds = 'الرجاء اختيار عضو واحد على الأقل';
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      if (editMode && currentIncomeId) {
        // Update existing income
        if (updateIncome) {
          updateIncome(currentIncomeId, newIncome);
        }
      } else {
        // Add new income
        addIncome(newIncome);
      }
      handleClose();
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewIncome({ ...newIncome, [name]: value });
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({ ...errors, [name]: undefined });
    }
  };
  
  // Handle income type change
  const handleIncomeTypeChange = (e) => {
    const incomeType = e.target.value;
    setNewIncome({ 
      ...newIncome, 
      incomeType,
      // Reset member selection when changing type
      memberId: incomeType === 'individual' ? newIncome.memberId : '',
      memberIds: incomeType === 'multiple' ? newIncome.memberIds : []
    });
  };
  
  // Handle multiple members selection
  const handleMemberCheckboxChange = (memberId) => {
    const memberIds = [...newIncome.memberIds];
    const memberIndex = memberIds.indexOf(memberId);
    
    if (memberIndex === -1) {
      // Add member to selection
      memberIds.push(memberId);
    } else {
      // Remove member from selection
      memberIds.splice(memberIndex, 1);
    }
    
    setNewIncome({ ...newIncome, memberIds });
    
    // Clear error when user selects members
    if (errors.memberIds) {
      setErrors({ ...errors, memberIds: undefined });
    }
  };

  // Navigate to member detail
  const handleViewMember = (id) => {
    navigate(`/member/${id}`);
  };

  // Filter income transactions based on search term
  const filteredIncome = income.filter(transaction => {
    const member = members.find(m => m.id === transaction.memberId);
    return (
      (member && member.name.toLowerCase().includes(search.toLowerCase())) ||
      (transaction.bank && transaction.bank.toLowerCase().includes(search.toLowerCase())) ||
      (transaction.notes && transaction.notes.toLowerCase().includes(search.toLowerCase()))
    );
  });

  // Common bank list for Saudi Arabia
  const bankList = [
    'البنك الأهلي السعودي',
    'البنك السعودي البريطاني (ساب)',
    'البنك السعودي الفرنسي',
    'البنك السعودي للاستثمار',
    'البنك العربي الوطني',
    'بنك البلاد',
    'بنك الجزيرة',
    'بنك الرياض',
    'مصرف الراجحي',
    'مصرف الإنماء',
    'نقدًا',
    'أخرى'
  ];

  return (
    <Box>
      <Paper elevation={3} sx={{ p: 3, borderRadius: '12px', mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h5" fontWeight="bold">
            الإيرادات
          </Typography>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />} 
            onClick={handleClickOpen}
            sx={{ 
              mr: 'auto',
              ml: 0,
              borderRadius: '8px', 
              px: 3, 
              py: 1 
            }}
          >
            إضافة إيراد
          </Button>
        </Box>
      </Paper>
      <Divider sx={{ mb: 4 }} />

      {/* Search */}
      <Paper sx={{ p: 2, mb: 3, borderRadius: 2 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="بحث عن إيراد..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Paper>

      {/* Income Transactions Table */}
      <TableContainer component={Paper} sx={{ borderRadius: 2, overflow: 'hidden' }}>
        <Table>
          <TableHead sx={{ bgcolor: '#f5f5f5' }}>
            <TableRow>
              <TableCell align="center" sx={{ fontWeight: 'bold', width: '50px' }}>#</TableCell>
              <TableCell align="right" sx={{ fontWeight: 'bold' }}>العضو</TableCell>
              <TableCell align="right" sx={{ fontWeight: 'bold' }}>المبلغ</TableCell>
              <TableCell align="right" sx={{ fontWeight: 'bold' }}>البنك</TableCell>
              <TableCell align="right" sx={{ fontWeight: 'bold' }}>التاريخ والوقت</TableCell>
              <TableCell align="right" sx={{ fontWeight: 'bold' }}>ملاحظات</TableCell>
              <TableCell align="center" sx={{ fontWeight: 'bold' }}>عرض العضو</TableCell>
              <TableCell align="center" sx={{ fontWeight: 'bold' }}>إجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredIncome.length > 0 ? (
              filteredIncome.map((transaction, index) => {
                const member = members.find(m => m.id === transaction.memberId);
                return (
                  <TableRow key={transaction.id} sx={{ '&:hover': { bgcolor: '#f9f9f9' } }}>
                    <TableCell align="center" sx={{
                      fontWeight: 'bold',
                      color: 'primary.main',
                      width: '50px'
                    }}>
                      {index + 1}
                    </TableCell>
                    <TableCell align="right">{member ? member.name : 'غير معروف'}</TableCell>
                    <TableCell
                      align="right"
                      sx={{ color: 'success.main', fontWeight: 'bold' }}
                    >
                      {formatCurrencyLocal(transaction.amount)}
                    </TableCell>
                    <TableCell align="right">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <BankIcon sx={{ mr: 1, fontSize: 18, color: 'text.secondary' }} />
                        {transaction.bank || 'غير محدد'}
                      </Box>
                    </TableCell>
                    <TableCell align="right">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <DateIcon sx={{ mr: 1, fontSize: 18, color: 'text.secondary' }} />
                        {formatDate(transaction.createdAt)}
                      </Box>
                    </TableCell>
                    <TableCell align="right">{transaction.notes || '-'}</TableCell>
                    <TableCell align="center">
                      {member && (
                        <IconButton 
                          color="primary" 
                          onClick={() => handleViewMember(member.id)}
                          sx={{ '&:hover': { transform: 'scale(1.1)' } }}
                        >
                          <PersonIcon />
                        </IconButton>
                      )}
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                        <IconButton 
                          color="primary" 
                          onClick={() => handleEditIncome(transaction.id)}
                          sx={{ mx: 0.5, '&:hover': { transform: 'scale(1.1)' } }}
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton 
                          color="error" 
                          onClick={() => handleOpenDeleteConfirm(transaction.id)}
                          sx={{ mx: 0.5, '&:hover': { transform: 'scale(1.1)' } }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell colSpan={7} align="center" sx={{ py: 3 }}>
                  <Typography variant="body1" color="text.secondary">
                    {search ? 'لا توجد نتائج للبحث' : 'لا توجد إيرادات حتى الآن'}
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Add Income Dialog */}
      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IncomeIcon sx={{ mr: 1, color: 'success.main' }} />
            <Typography variant="h6" component="div">
              {editMode ? 'تعديل الإيراد' : 'إضافة إيراد جديد'}
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 0.5 }}>
            {/* Income Type Selection */}
            <Grid item xs={12}>
              <FormControl component="fieldset">
                <FormLabel component="legend">نوع الإيراد</FormLabel>
                <RadioGroup
                  row
                  name="incomeType"
                  value={newIncome.incomeType}
                  onChange={handleIncomeTypeChange}
                >
                  <FormControlLabel 
                    value="all" 
                    control={<Radio />} 
                    label="جميع الأعضاء" 
                  />
                  <FormControlLabel 
                    value="multiple" 
                    control={<Radio />} 
                    label="أعضاء محددين" 
                  />
                  <FormControlLabel 
                    value="individual" 
                    control={<Radio />} 
                    label="عضو واحد" 
                  />
                </RadioGroup>
              </FormControl>
            </Grid>
            
            {/* Member Selection based on income type */}
            {newIncome.incomeType === 'individual' && (
              <Grid item xs={12}>
                <FormControl fullWidth error={!!errors.memberId}>
                  <InputLabel id="member-select-label">العضو</InputLabel>
                  <Select
                    labelId="member-select-label"
                    name="memberId"
                    value={newIncome.memberId}
                    onChange={handleInputChange}
                    label="العضو"
                    startAdornment={
                      <InputAdornment position="start">
                        <PersonIcon />
                      </InputAdornment>
                    }
                  >
                    {members.map((member) => (
                      <MenuItem key={member.id} value={member.id}>
                        {member.name}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.memberId && <FormHelperText>{errors.memberId}</FormHelperText>}
                </FormControl>
              </Grid>
            )}
            
            {/* Multiple Members Selection */}
            {newIncome.incomeType === 'multiple' && (
              <Grid item xs={12}>
                <FormControl fullWidth error={!!errors.memberIds}>
                  <FormLabel component="legend" sx={{ mb: 2, fontWeight: 'bold', color: 'text.primary' }}>
                    اختر الأعضاء
                  </FormLabel>
                  <Paper
                    variant="outlined"
                    sx={{
                      maxHeight: 250,
                      overflow: 'auto',
                      mt: 1,
                      p: 0,
                      borderRadius: 2,
                      border: '2px solid',
                      borderColor: errors.memberIds ? 'error.main' : 'divider'
                    }}
                  >
                    <List dense sx={{ p: 0 }}>
                      {members.map((member, index) => (
                        <ListItem key={member.id} dense>
                          <ListItemIcon>
                            <Checkbox
                              edge="start"
                              checked={newIncome.memberIds.includes(member.id)}
                              onChange={() => handleMemberCheckboxChange(member.id)}
                              tabIndex={-1}
                              disableRipple
                            />
                          </ListItemIcon>
                          <ListItemText primary={member.name} />
                        </ListItem>
                      ))}
                    </List>

                    {/* عداد الأعضاء المحددين */}
                    {newIncome.memberIds.length > 0 && (
                      <Box
                        sx={{
                          p: 2,
                          bgcolor: 'primary.light',
                          borderTop: '1px solid',
                          borderTopColor: 'divider',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        <Typography
                          variant="body2"
                          sx={{
                            color: 'primary.dark',
                            fontWeight: 'bold',
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1
                          }}
                        >
                          <CheckIcon fontSize="small" />
                          تم اختيار {newIncome.memberIds.length} عضو من أصل {members.length}
                        </Typography>
                      </Box>
                    )}
                  </Paper>
                  {errors.memberIds && (
                    <FormHelperText error sx={{ mt: 1, fontSize: '0.875rem' }}>
                      {errors.memberIds}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>
            )}
            
            {/* All Members Info */}
            {newIncome.incomeType === 'all' && (
              <Grid item xs={12}>
                <Paper variant="outlined" sx={{ p: 2, bgcolor: '#f8f9fa' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <GroupIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="subtitle1" fontWeight="bold">
                      سيتم إضافة المبلغ لجميع الأعضاء ({members.length} عضو)
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    سيتم إضافة المبلغ المدخل إلى رصيد كل عضو من أعضاء الصندوق.
                  </Typography>
                </Paper>
              </Grid>
            )}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors.amount} sx={{ mb: 2 }}>
                <TextField
                  label="المبلغ"
                  name="amount"
                  type="number"
                  value={newIncome.amount}
                  onChange={handleInputChange}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">ريال</InputAdornment>
                    ),
                  }}
                  helperText={errors.amount || "يمكن إدخال قيمة سالبة للإشارة إلى مديونية"}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel id="bank-select-label">البنك</InputLabel>
                <Select
                  labelId="bank-select-label"
                  name="bank"
                  value={newIncome.bank}
                  onChange={handleInputChange}
                  label="البنك"
                  startAdornment={
                    <InputAdornment position="start">
                      <BankIcon />
                    </InputAdornment>
                  }
                >
                  {bankList.map((bank) => (
                    <MenuItem key={bank} value={bank}>
                      {bank}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="notes"
                label="ملاحظات"
                fullWidth
                variant="outlined"
                value={newIncome.notes}
                onChange={handleInputChange}
                multiline
                rows={2}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 2, justifyContent: 'flex-start' }}>
          <Button 
            onClick={handleClose} 
            color="inherit" 
            sx={{ ml: 2, mr: 0 }}
          >
            إلغاء
          </Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained" 
            color="primary"
          >
            إضافة
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={confirmDeleteOpen} onClose={handleCloseDeleteConfirm}>
        <DialogTitle>تأكيد الحذف</DialogTitle>
        <DialogContent>
          <DialogContentText>
            هل أنت متأكد من رغبتك في حذف هذا الإيراد؟ لا يمكن التراجع عن هذه العملية.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteConfirm} color="primary">
            إلغاء
          </Button>
          <Button onClick={handleDeleteIncome} color="error" variant="contained">
            حذف
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default IncomeManagement;
