{"name": "savings-fund-manager", "version": "0.2.0", "private": true, "main": "public/electron.js", "homepage": "./", "dependencies": {"@capacitor/android": "^7.3.0", "@capacitor/cli": "^7.3.0", "@capacitor/core": "^7.3.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.8", "@mui/material": "^6.4.8", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "electron": "^35.0.3", "electron-builder": "^25.1.8", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "moment": "^2.30.1", "moment-hijri": "^3.0.0", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-router-dom": "^6.21.1", "react-scripts": "^5.0.1", "react-to-print": "^2.14.15", "styled-components": "^6.1.1", "web-vitals": "^3.5.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron-dev": "electron .", "electron-pack": "electron-builder -c.extraMetadata.main=build/electron.js", "preelectron-pack": "npm run build", "cap:init": "npx cap init \"صندوق التوفير\" \"com.savings.fund\"", "cap:add:android": "npx cap add android", "cap:copy": "npx cap copy", "cap:sync": "npx cap sync", "cap:open:android": "npx cap open android", "cap:build:android": "npm run build && npx cap copy && npx cap open android", "build:android": "npm run build && npx cap copy && npx cap open android", "build:mobile": "npm run build && npx cap sync"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "build": {"appId": "com.savings.fund.manager", "productName": "صندوق التوفير", "files": ["build/**/*", "node_modules/**/*"], "directories": {"buildResources": "assets"}}}