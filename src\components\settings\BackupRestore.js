import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Grid,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Divider
} from '@mui/material';
import {
  Download as DownloadIcon,
  Upload as UploadIcon,
  Backup as BackupIcon,
  Restore as RestoreIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { useFund } from '../../contexts/FundContext';

const BackupRestore = () => {
  const { currentFundId, funds } = useFund();
  const [loading, setLoading] = useState(false);
  const [alert, setAlert] = useState({ open: false, message: '', severity: 'info' });
  const [restoreDialog, setRestoreDialog] = useState(false);
  const [backupData, setBackupData] = useState(null);

  // إنشاء نسخة احتياطية
  const createBackup = () => {
    setLoading(true);
    try {
      const backupData = {
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        fundId: currentFundId,
        data: {}
      };

      // جمع جميع البيانات من localStorage
      const keys = Object.keys(localStorage);
      const fundKeys = keys.filter(key => 
        key.includes(`_${currentFundId}`) || 
        key === 'savings_fund_funds' ||
        key === 'current_fund_id'
      );

      fundKeys.forEach(key => {
        try {
          const value = localStorage.getItem(key);
          backupData.data[key] = JSON.parse(value);
        } catch (e) {
          backupData.data[key] = value;
        }
      });

      // تحويل البيانات إلى JSON
      const jsonString = JSON.stringify(backupData, null, 2);
      
      // إنشاء ملف للتحميل
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      // إنشاء رابط التحميل
      const link = document.createElement('a');
      link.href = url;
      link.download = `backup_${currentFundId}_${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(url);

      setAlert({
        open: true,
        message: 'تم إنشاء النسخة الاحتياطية وتحميلها بنجاح',
        severity: 'success'
      });

    } catch (error) {
      console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
      setAlert({
        open: true,
        message: 'حدث خطأ أثناء إنشاء النسخة الاحتياطية',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // استعادة النسخة الاحتياطية
  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target.result);
        setBackupData(data);
        setRestoreDialog(true);
      } catch (error) {
        setAlert({
          open: true,
          message: 'ملف النسخة الاحتياطية غير صالح',
          severity: 'error'
        });
      }
    };
    reader.readAsText(file);
  };

  // تأكيد الاستعادة
  const confirmRestore = () => {
    setLoading(true);
    try {
      // استعادة البيانات
      Object.entries(backupData.data).forEach(([key, value]) => {
        if (typeof value === 'object') {
          localStorage.setItem(key, JSON.stringify(value));
        } else {
          localStorage.setItem(key, value);
        }
      });

      setAlert({
        open: true,
        message: 'تم استعادة البيانات بنجاح. سيتم إعادة تحميل الصفحة.',
        severity: 'success'
      });

      // إعادة تحميل الصفحة بعد ثانيتين
      setTimeout(() => {
        window.location.reload();
      }, 2000);

    } catch (error) {
      console.error('خطأ في استعادة البيانات:', error);
      setAlert({
        open: true,
        message: 'حدث خطأ أثناء استعادة البيانات',
        severity: 'error'
      });
    } finally {
      setLoading(false);
      setRestoreDialog(false);
    }
  };

  // حساب حجم البيانات
  const calculateDataSize = () => {
    const dataString = JSON.stringify(localStorage);
    const sizeInBytes = new Blob([dataString]).size;
    const sizeInKB = (sizeInBytes / 1024).toFixed(2);
    const sizeInMB = (sizeInKB / 1024).toFixed(2);
    
    if (sizeInMB > 1) {
      return `${sizeInMB} ميجابايت`;
    } else {
      return `${sizeInKB} كيلوبايت`;
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
        <BackupIcon />
        النسخ الاحتياطي واستعادة البيانات
      </Typography>

      {alert.open && (
        <Alert 
          severity={alert.severity} 
          onClose={() => setAlert({ ...alert, open: false })}
          sx={{ mb: 3 }}
        >
          {alert.message}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* إنشاء نسخة احتياطية */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined" sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <DownloadIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">إنشاء نسخة احتياطية</Typography>
              </Box>
              
              <Typography variant="body2" color="text.secondary" paragraph>
                قم بتصدير جميع بيانات الصندوق الحالي إلى ملف JSON
              </Typography>

              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <CheckIcon color="success" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary="بيانات الأعضاء والعائلات" />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CheckIcon color="success" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary="الإيرادات والمصروفات" />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CheckIcon color="success" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary="السحوبات والقروض" />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CheckIcon color="success" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary="إعدادات الصندوق" />
                </ListItem>
              </List>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  حجم البيانات: <strong>{calculateDataSize()}</strong>
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  آخر نسخة: {localStorage.getItem('last_backup') || 'لم يتم إنشاء نسخة بعد'}
                </Typography>
              </Box>

              <Button
                variant="contained"
                startIcon={<DownloadIcon />}
                onClick={createBackup}
                disabled={loading}
                fullWidth
                sx={{ mt: 2 }}
              >
                {loading ? 'جاري الإنشاء...' : 'إنشاء نسخة احتياطية'}
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* استعادة النسخة الاحتياطية */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined" sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <UploadIcon sx={{ mr: 1, color: 'warning.main' }} />
                <Typography variant="h6">استعادة نسخة احتياطية</Typography>
              </Box>
              
              <Typography variant="body2" color="text.secondary" paragraph>
                استعادة البيانات من ملف نسخة احتياطية سابقة
              </Typography>

              <Alert severity="warning" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  <strong>تحذير:</strong> ستؤدي هذه العملية إلى استبدال جميع البيانات الحالية
                </Typography>
              </Alert>

              <input
                accept=".json"
                style={{ display: 'none' }}
                id="backup-file-input"
                type="file"
                onChange={handleFileUpload}
              />
              <label htmlFor="backup-file-input">
                <Button
                  variant="outlined"
                  component="span"
                  startIcon={<UploadIcon />}
                  fullWidth
                  sx={{ mt: 2 }}
                >
                  اختيار ملف النسخة الاحتياطية
                </Button>
              </label>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {loading && (
        <Box sx={{ mt: 3 }}>
          <LinearProgress />
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1, textAlign: 'center' }}>
            جاري المعالجة...
          </Typography>
        </Box>
      )}

      {/* مربع حوار تأكيد الاستعادة */}
      <Dialog open={restoreDialog} onClose={() => setRestoreDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <RestoreIcon />
          تأكيد استعادة النسخة الاحتياطية
        </DialogTitle>
        <DialogContent>
          {backupData && (
            <Box>
              <Alert severity="info" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  سيتم استعادة البيانات التالية:
                </Typography>
              </Alert>
              
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <InfoIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="تاريخ النسخة الاحتياطية"
                    secondary={new Date(backupData.timestamp).toLocaleString('ar-SA')}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <InfoIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="إصدار النسخة"
                    secondary={backupData.version}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <InfoIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="عدد العناصر"
                    secondary={`${Object.keys(backupData.data).length} عنصر`}
                  />
                </ListItem>
              </List>

              <Alert severity="warning" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>تحذير:</strong> ستؤدي هذه العملية إلى حذف جميع البيانات الحالية واستبدالها بالبيانات من النسخة الاحتياطية.
                </Typography>
              </Alert>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRestoreDialog(false)}>إلغاء</Button>
          <Button 
            onClick={confirmRestore} 
            variant="contained" 
            color="warning"
            disabled={loading}
          >
            {loading ? 'جاري الاستعادة...' : 'تأكيد الاستعادة'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BackupRestore;
