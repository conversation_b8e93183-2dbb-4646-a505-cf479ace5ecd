import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Default users database (stored locally)
const DEFAULT_USERS = [
  {
    id: 1,
    username: 'admin',
    password: '123456',
    name: 'المدير العام',
    role: 'admin',
    createdAt: new Date().toISOString()
  },
  {
    id: 2,
    username: 'manager',
    password: '123456',
    name: 'مدير الصندوق',
    role: 'manager',
    createdAt: new Date().toISOString()
  }
];

const USERS_STORAGE_KEY = 'savings_fund_users';
const CURRENT_USER_KEY = 'savings_fund_current_user';
const AUTH_TOKEN_KEY = 'savings_fund_auth_token';

// Helper functions for safe JSON operations
const safelyParseJSON = (key, defaultValue) => {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error(`Error parsing JSON for key ${key}:`, error);
    return defaultValue;
  }
};

const safelySaveJSON = (key, value) => {
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    console.error(`Error saving JSON for key ${key}:`, error);
  }
};

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  // Initialize users database and check authentication on mount
  useEffect(() => {
    // Initialize users database if it doesn't exist
    const existingUsers = safelyParseJSON(USERS_STORAGE_KEY, null);
    if (!existingUsers) {
      safelySaveJSON(USERS_STORAGE_KEY, DEFAULT_USERS);
    }

    // Check if user is already authenticated
    const token = localStorage.getItem(AUTH_TOKEN_KEY);
    const savedUser = safelyParseJSON(CURRENT_USER_KEY, null);

    // Validate token and user data
    if (token && savedUser && savedUser.username && savedUser.password) {
      // Verify user still exists in database
      const users = safelyParseJSON(USERS_STORAGE_KEY, DEFAULT_USERS);
      const userExists = users.find(u => u.id === savedUser.id && u.username === savedUser.username);

      if (userExists) {
        setCurrentUser(savedUser);
        setIsAuthenticated(true);
      } else {
        // Clear invalid authentication data
        localStorage.removeItem(AUTH_TOKEN_KEY);
        localStorage.removeItem(CURRENT_USER_KEY);
      }
    }

    setLoading(false);
  }, []);

  // Login function
  const login = async (username, password) => {
    try {
      const users = safelyParseJSON(USERS_STORAGE_KEY, DEFAULT_USERS);
      const user = users.find(u => u.username === username && u.password === password);
      
      if (user) {
        // Create a simple token (in real app, use proper JWT)
        const token = btoa(`${user.id}-${Date.now()}`);
        
        // Save authentication data
        localStorage.setItem(AUTH_TOKEN_KEY, token);
        safelySaveJSON(CURRENT_USER_KEY, user);
        
        setCurrentUser(user);
        setIsAuthenticated(true);
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  };

  // Logout function
  const logout = () => {
    localStorage.removeItem(AUTH_TOKEN_KEY);
    localStorage.removeItem(CURRENT_USER_KEY);
    setCurrentUser(null);
    setIsAuthenticated(false);
  };

  // Add new user function (for admin)
  const addUser = (userData) => {
    try {
      const users = safelyParseJSON(USERS_STORAGE_KEY, DEFAULT_USERS);
      const newUser = {
        id: Date.now(),
        ...userData,
        createdAt: new Date().toISOString()
      };
      
      const updatedUsers = [...users, newUser];
      safelySaveJSON(USERS_STORAGE_KEY, updatedUsers);
      
      return true;
    } catch (error) {
      console.error('Add user error:', error);
      return false;
    }
  };

  // Update user function
  const updateUser = (userId, userData) => {
    try {
      const users = safelyParseJSON(USERS_STORAGE_KEY, DEFAULT_USERS);
      const updatedUsers = users.map(user => 
        user.id === userId ? { ...user, ...userData } : user
      );
      
      safelySaveJSON(USERS_STORAGE_KEY, updatedUsers);
      
      // Update current user if it's the same user
      if (currentUser && currentUser.id === userId) {
        const updatedCurrentUser = { ...currentUser, ...userData };
        setCurrentUser(updatedCurrentUser);
        safelySaveJSON(CURRENT_USER_KEY, updatedCurrentUser);
      }
      
      return true;
    } catch (error) {
      console.error('Update user error:', error);
      return false;
    }
  };

  // Delete user function
  const deleteUser = (userId) => {
    try {
      const users = safelyParseJSON(USERS_STORAGE_KEY, DEFAULT_USERS);
      const updatedUsers = users.filter(user => user.id !== userId);
      safelySaveJSON(USERS_STORAGE_KEY, updatedUsers);
      return true;
    } catch (error) {
      console.error('Delete user error:', error);
      return false;
    }
  };

  // Get all users function
  const getUsers = () => {
    return safelyParseJSON(USERS_STORAGE_KEY, DEFAULT_USERS);
  };

  // Change password function
  const changePassword = (oldPassword, newPassword) => {
    try {
      if (!currentUser) return false;

      const users = safelyParseJSON(USERS_STORAGE_KEY, DEFAULT_USERS);
      const user = users.find(u => u.id === currentUser.id);

      if (user && user.password === oldPassword) {
        return updateUser(currentUser.id, { password: newPassword });
      }

      return false;
    } catch (error) {
      console.error('Change password error:', error);
      return false;
    }
  };

  // Reset authentication system (for debugging)
  const resetAuth = () => {
    localStorage.removeItem(AUTH_TOKEN_KEY);
    localStorage.removeItem(CURRENT_USER_KEY);
    localStorage.removeItem(USERS_STORAGE_KEY);
    safelySaveJSON(USERS_STORAGE_KEY, DEFAULT_USERS);
    setCurrentUser(null);
    setIsAuthenticated(false);
  };

  const value = {
    currentUser,
    isAuthenticated,
    loading,
    login,
    logout,
    addUser,
    updateUser,
    deleteUser,
    getUsers,
    changePassword,
    resetAuth
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
