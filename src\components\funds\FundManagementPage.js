import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardActions,
  Button,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Chip,
  Alert,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  AccountBalance as FundIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  CalendarToday as DateIcon
} from '@mui/icons-material';
import { useFund } from '../../contexts/FundContext';

const FundManagementPage = () => {
  const { funds, addFund, updateFund, deleteFund, switchFund, currentFundId } = useFund();
  const [openDialog, setOpenDialog] = useState(false);
  const [editingFund, setEditingFund] = useState(null);
  const [formData, setFormData] = useState({
    fundName: '',
    fundFamily: '',
    generalManager: '',
    treasurer: '',
    collector: ''
  });

  const handleOpenDialog = (fund = null) => {
    if (fund) {
      setEditingFund(fund);
      setFormData({
        fundName: fund.fundName || '',
        fundFamily: fund.fundFamily || '',
        generalManager: fund.generalManager || '',
        treasurer: fund.treasurer || '',
        collector: fund.collector || ''
      });
    } else {
      setEditingFund(null);
      setFormData({
        fundName: '',
        fundFamily: '',
        generalManager: '',
        treasurer: '',
        collector: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingFund(null);
    setFormData({
      fundName: '',
      fundFamily: '',
      generalManager: '',
      treasurer: '',
      collector: ''
    });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = () => {
    if (!formData.fundName.trim()) {
      alert('يرجى إدخال اسم الصندوق');
      return;
    }

    if (editingFund) {
      updateFund(editingFund.id, formData);
    } else {
      addFund(formData);
    }

    handleCloseDialog();
  };

  const handleDeleteFund = (fundId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الصندوق؟ سيتم حذف جميع البيانات المرتبطة به.')) {
      deleteFund(fundId);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-SA');
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          إدارة الصناديق
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
          sx={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
            }
          }}
        >
          إنشاء صندوق جديد
        </Button>
      </Box>

      {funds.length === 0 ? (
        <Card sx={{ textAlign: 'center', py: 6 }}>
          <CardContent>
            <FundIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h5" color="text.secondary" gutterBottom>
              لا توجد صناديق مسجلة
            </Typography>
            <Typography variant="body1" color="text.secondary" mb={3}>
              ابدأ بإنشاء صندوق جديد لإدارة أموالك
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => handleOpenDialog()}
              size="large"
            >
              إنشاء أول صندوق
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Grid container spacing={3}>
          {funds.map((fund) => (
            <Grid item xs={12} md={6} lg={4} key={fund.id}>
              <Card 
                sx={{ 
                  height: '100%',
                  border: currentFundId === fund.id ? '2px solid #667eea' : '1px solid #e0e0e0',
                  boxShadow: currentFundId === fund.id ? '0 4px 20px rgba(102, 126, 234, 0.3)' : 1,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    boxShadow: 3,
                    transform: 'translateY(-2px)'
                  }
                }}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <FundIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6" component="h2" fontWeight="bold">
                      {fund.fundName}
                    </Typography>
                    {currentFundId === fund.id && (
                      <Chip 
                        label="نشط" 
                        color="primary" 
                        size="small" 
                        sx={{ ml: 'auto' }}
                      />
                    )}
                  </Box>

                  <Divider sx={{ my: 2 }} />

                  {fund.fundFamily && (
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        <strong>العائلة:</strong> {fund.fundFamily}
                      </Typography>
                    </Box>
                  )}

                  {fund.generalManager && (
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      mb: 1,
                      p: { xs: 0.8, sm: 1 },
                      borderRadius: '8px',
                      bgcolor: 'rgba(255, 193, 7, 0.1)',
                      border: '1px solid rgba(255, 193, 7, 0.3)',
                      flexWrap: 'wrap'
                    }}>
                      <PersonIcon sx={{ fontSize: { xs: 14, sm: 16 }, mr: 0.5, color: '#f57c00' }} />
                      <Typography variant="body2" sx={{
                        color: '#e65100',
                        fontWeight: 'bold',
                        fontSize: { xs: '0.75rem', sm: '0.875rem' },
                        lineHeight: 1.2,
                        wordBreak: 'break-word'
                      }}>
                        <strong>المدير العام:</strong> {fund.generalManager}
                      </Typography>
                    </Box>
                  )}

                  {fund.treasurer && (
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      mb: 1,
                      p: { xs: 0.8, sm: 1 },
                      borderRadius: '8px',
                      bgcolor: 'rgba(76, 175, 80, 0.1)',
                      border: '1px solid rgba(76, 175, 80, 0.3)',
                      flexWrap: 'wrap'
                    }}>
                      <PersonIcon sx={{ fontSize: { xs: 14, sm: 16 }, mr: 0.5, color: '#388e3c' }} />
                      <Typography variant="body2" sx={{
                        color: '#2e7d32',
                        fontWeight: 'bold',
                        fontSize: { xs: '0.75rem', sm: '0.875rem' },
                        lineHeight: 1.2,
                        wordBreak: 'break-word'
                      }}>
                        <strong>أمين الصندوق:</strong> {fund.treasurer}
                      </Typography>
                    </Box>
                  )}

                  {fund.collector && (
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <PersonIcon sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                      <Typography variant="body2" color="text.secondary">
                        <strong>المحصل:</strong> {fund.collector}
                      </Typography>
                    </Box>
                  )}

                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                    <DateIcon sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                    <Typography variant="caption" color="text.secondary">
                      تاريخ الإنشاء: {formatDate(fund.createdAt)}
                    </Typography>
                  </Box>
                </CardContent>

                <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
                  <Box>
                    {currentFundId !== fund.id && (
                      <Button
                        size="small"
                        variant="outlined"
                        onClick={() => switchFund(fund.id)}
                      >
                        تفعيل
                      </Button>
                    )}
                  </Box>
                  
                  <Box>
                    <IconButton
                      size="small"
                      onClick={() => handleOpenDialog(fund)}
                      color="primary"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleDeleteFund(fund.id)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Dialog for Add/Edit Fund */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingFund ? 'تعديل الصندوق' : 'إنشاء صندوق جديد'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="اسم الصندوق *"
              name="fundName"
              value={formData.fundName}
              onChange={handleInputChange}
              margin="normal"
              required
            />
            <TextField
              fullWidth
              label="اسم العائلة"
              name="fundFamily"
              value={formData.fundFamily}
              onChange={handleInputChange}
              margin="normal"
            />
            <TextField
              fullWidth
              label="المدير العام"
              name="generalManager"
              value={formData.generalManager}
              onChange={handleInputChange}
              margin="normal"
            />
            <TextField
              fullWidth
              label="أمين الصندوق"
              name="treasurer"
              value={formData.treasurer}
              onChange={handleInputChange}
              margin="normal"
            />
            <TextField
              fullWidth
              label="المحصل"
              name="collector"
              value={formData.collector}
              onChange={handleInputChange}
              margin="normal"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            إلغاء
          </Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingFund ? 'تحديث' : 'إنشاء'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FundManagementPage;
