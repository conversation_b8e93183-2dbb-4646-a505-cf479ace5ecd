import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Container,
  Paper,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  IconButton,
  Divider
} from '@mui/material';
import {
  AccountBalance as BankIcon,
  Add as AddIcon,
  Close as CloseIcon,
  Savings as SavingsIcon
} from '@mui/icons-material';
import { useFund } from '../../contexts/FundContext';
import Copyright from '../common/Copyright';

const WelcomePage = () => {
  const { addFund } = useFund();
  const [openDialog, setOpenDialog] = useState(false);
  const [newFund, setNewFund] = useState({
    fundName: '',
    fundFamily: '',
    generalManager: '',
    treasurer: '',
    collector: ''
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const handleOpenDialog = () => {
    setOpenDialog(true);
    setErrors({});
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setNewFund({
      fundName: '',
      fundFamily: '',
      generalManager: '',
      treasurer: '',
      collector: ''
    });
    setErrors({});
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewFund(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!newFund.fundName.trim()) {
      newErrors.fundName = 'اسم الصندوق مطلوب';
    }
    
    if (!newFund.fundFamily.trim()) {
      newErrors.fundFamily = 'اسم العائلة/المجموعة مطلوب';
    }
    
    if (!newFund.generalManager.trim()) {
      newErrors.generalManager = 'اسم المدير العام مطلوب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCreateFund = async () => {
    if (validateForm()) {
      setLoading(true);
      try {
        await addFund(newFund);
        handleCloseDialog();
      } catch (error) {
        console.error('Error creating fund:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 2
      }}
    >
      <Container maxWidth="md">
        <Paper
          elevation={24}
          sx={{
            borderRadius: 4,
            overflow: 'hidden',
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)'
          }}
        >
          {/* Header Section */}
          <Box
            sx={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              textAlign: 'center',
              py: 6,
              px: 3
            }}
          >
            <Avatar
              sx={{
                width: 100,
                height: 100,
                margin: '0 auto 24px',
                background: 'rgba(255, 255, 255, 0.2)',
                backdropFilter: 'blur(10px)'
              }}
            >
              <BankIcon sx={{ fontSize: 50 }} />
            </Avatar>
            
            <Typography variant="h3" component="h1" fontWeight="bold" gutterBottom>
              مرحباً بك في نظام إدارة صناديق التوفير
            </Typography>
            
            <Typography variant="h6" sx={{ opacity: 0.9, mt: 2 }}>
              نظام شامل لإدارة صناديق التوفير والمدخرات الجماعية
            </Typography>
          </Box>

          {/* Welcome Content */}
          <CardContent sx={{ p: 6 }}>
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Avatar
                sx={{
                  width: 80,
                  height: 80,
                  margin: '0 auto 16px',
                  background: 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)',
                  boxShadow: '0 8px 25px rgba(76, 175, 80, 0.3)'
                }}
              >
                <SavingsIcon sx={{ fontSize: 40 }} />
              </Avatar>
              
              <Typography variant="h4" component="h2" fontWeight="bold" gutterBottom color="primary">
                ابدأ رحلتك في إدارة المدخرات
              </Typography>
              
              <Typography variant="h6" color="text.secondary" sx={{ mb: 4, lineHeight: 1.6 }}>
                يرجى إنشاء صندوق توفير جديد للبدء في إدارة مدخرات مجموعتك
              </Typography>

              <Box sx={{ mb: 4 }}>
                <Grid container spacing={3} justifyContent="center">
                  <Grid item xs={12} sm={4}>
                    <Card sx={{ 
                      textAlign: 'center', 
                      p: 2, 
                      background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
                      border: '1px solid #2196f3'
                    }}>
                      <Typography variant="h6" color="primary" fontWeight="bold">
                        إدارة الأعضاء
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        تسجيل وإدارة بيانات الأعضاء
                      </Typography>
                    </Card>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Card sx={{ 
                      textAlign: 'center', 
                      p: 2, 
                      background: 'linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%)',
                      border: '1px solid #4caf50'
                    }}>
                      <Typography variant="h6" color="success.main" fontWeight="bold">
                        تتبع المدخرات
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        متابعة الإيرادات والمصروفات
                      </Typography>
                    </Card>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Card sx={{ 
                      textAlign: 'center', 
                      p: 2, 
                      background: 'linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%)',
                      border: '1px solid #ff9800'
                    }}>
                      <Typography variant="h6" color="warning.main" fontWeight="bold">
                        التقارير المالية
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        تقارير شاملة ومفصلة
                      </Typography>
                    </Card>
                  </Grid>
                </Grid>
              </Box>

              <Button
                variant="contained"
                size="large"
                startIcon={<AddIcon />}
                onClick={handleOpenDialog}
                sx={{
                  py: 2,
                  px: 6,
                  borderRadius: 3,
                  background: 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)',
                  fontSize: '1.2rem',
                  fontWeight: 'bold',
                  boxShadow: '0 8px 25px rgba(76, 175, 80, 0.4)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #45a049 0%, #388e3c 100%)',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 12px 35px rgba(76, 175, 80, 0.5)',
                  },
                  transition: 'all 0.3s ease',
                }}
              >
                إنشاء صندوق جديد
              </Button>
            </Box>
          </CardContent>

          {/* Footer */}
          <Box
            sx={{
              background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
              borderTop: '1px solid #e0e0e0'
            }}
          >
            <Copyright variant="footer" showDivider={false} />
          </Box>
        </Paper>
      </Container>

      {/* Create Fund Dialog */}
      <Dialog 
        open={openDialog} 
        onClose={handleCloseDialog} 
        maxWidth="sm" 
        fullWidth 
        dir="rtl"
        PaperProps={{
          sx: {
            borderRadius: 3,
            background: 'rgba(255, 255, 255, 0.98)',
            backdropFilter: 'blur(10px)'
          }
        }}
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Box display="flex" alignItems="center" gap={2}>
              <Avatar sx={{ 
                background: 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)',
                width: 40,
                height: 40
              }}>
                <AddIcon />
              </Avatar>
              <Typography variant="h6" fontWeight="bold">
                إنشاء صندوق توفير جديد
              </Typography>
            </Box>
            <IconButton onClick={handleCloseDialog} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        
        <Divider />
        
        <DialogContent sx={{ pt: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="اسم الصندوق"
                name="fundName"
                value={newFund.fundName}
                onChange={handleInputChange}
                error={!!errors.fundName}
                helperText={errors.fundName}
                required
                variant="outlined"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="اسم العائلة/المجموعة"
                name="fundFamily"
                value={newFund.fundFamily}
                onChange={handleInputChange}
                error={!!errors.fundFamily}
                helperText={errors.fundFamily}
                required
                variant="outlined"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="المدير العام"
                name="generalManager"
                value={newFund.generalManager}
                onChange={handleInputChange}
                error={!!errors.generalManager}
                helperText={errors.generalManager}
                required
                variant="outlined"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="أمين الصندوق"
                name="treasurer"
                value={newFund.treasurer}
                onChange={handleInputChange}
                variant="outlined"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="المحصل"
                name="collector"
                value={newFund.collector}
                onChange={handleInputChange}
                variant="outlined"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        
        <DialogActions sx={{ p: 3, pt: 2 }}>
          <Button 
            onClick={handleCloseDialog}
            variant="outlined"
            sx={{ borderRadius: 2, px: 3 }}
          >
            إلغاء
          </Button>
          <Button
            onClick={handleCreateFund}
            variant="contained"
            disabled={loading}
            startIcon={<AddIcon />}
            sx={{
              borderRadius: 2,
              px: 4,
              background: 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #45a049 0%, #388e3c 100%)',
              }
            }}
          >
            {loading ? 'جاري الإنشاء...' : 'إنشاء الصندوق'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default WelcomePage;
