# سكريبت بناء APK بسيط مع ترقيم
param([string]$Description = "update")

$DateTime = Get-Date -Format "yyyy-MM-dd_HH-mm"

Write-Host "🚀 بناء APK مع ترقيم..." -ForegroundColor Green

# بناء React
Write-Host "⚛️ بناء React..." -ForegroundColor Blue
npm run build

# مزامنة Capacitor  
Write-Host "🔄 مزامنة Capacitor..." -ForegroundColor Blue
npx cap sync android

# بناء APK
Write-Host "📱 بناء APK..." -ForegroundColor Blue
cd android
.\gradlew clean assembleDebug
cd ..

# إنشاء مجلد الإصدارات
if (!(Test-Path "releases")) {
    New-Item -ItemType Directory -Path "releases"
}

# نسخ APK مع اسم جديد
$SourceAPK = "android\app\build\outputs\apk\debug\app-debug.apk"
$NewName = "savings-fund-v1.1.0-$DateTime-$Description.apk"
$TargetAPK = "releases\$NewName"

Copy-Item $SourceAPK $TargetAPK

Write-Host "✅ تم إنشاء APK: $NewName" -ForegroundColor Green
Write-Host "📍 المسار: $TargetAPK" -ForegroundColor Yellow

# عرض الملفات
Write-Host "`n📋 الإصدارات المتوفرة:" -ForegroundColor Cyan
Get-ChildItem "releases" -Filter "*.apk" | Sort-Object LastWriteTime -Descending | Select-Object Name, LastWriteTime
