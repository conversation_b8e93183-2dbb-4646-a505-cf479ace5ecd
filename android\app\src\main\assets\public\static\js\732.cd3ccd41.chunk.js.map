{"version": 3, "file": "static/js/732.cd3ccd41.chunk.js", "mappings": "4GAOA,IAPA,IAAIA,EAAMC,EAAQ,KACdC,EAAyB,qBAAXC,OAAyBC,EAAAA,EAASD,OAChDE,EAAU,CAAC,MAAO,UAClBC,EAAS,iBACTC,EAAML,EAAK,UAAYI,GACvBE,EAAMN,EAAK,SAAWI,IAAWJ,EAAK,gBAAkBI,GAEpDG,EAAI,GAAIF,GAAOE,EAAIJ,EAAQK,OAAQD,IACzCF,EAAML,EAAKG,EAAQI,GAAK,UAAYH,GACpCE,EAAMN,EAAKG,EAAQI,GAAK,SAAWH,IAC5BJ,EAAKG,EAAQI,GAAK,gBAAkBH,GAI7C,IAAIC,IAAQC,EAAK,CACf,IAAIG,EAAO,EACPC,EAAK,EACLC,EAAQ,GACRC,EAAgB,IAAO,GAE3BP,EAAM,SAASQ,GACb,GAAoB,IAAjBF,EAAMH,OAAc,CACrB,IAAIM,EAAOhB,IACPiB,EAAOC,KAAKC,IAAI,EAAGL,GAAiBE,EAAOL,IAC/CA,EAAOM,EAAOD,EACdI,YAAW,WACT,IAAIC,EAAKR,EAAMS,MAAM,GAIrBT,EAAMH,OAAS,EACf,IAAI,IAAID,EAAI,EAAGA,EAAIY,EAAGX,OAAQD,IAC5B,IAAIY,EAAGZ,GAAGc,UACR,IACEF,EAAGZ,GAAGM,SAASJ,EACjB,CAAE,MAAMa,GACNJ,YAAW,WAAa,MAAMI,CAAE,GAAG,EACrC,CAGN,GAAGN,KAAKO,MAAMR,GAChB,CAMA,OALAJ,EAAMa,KAAK,CACTC,SAAUf,EACVG,SAAUA,EACVQ,WAAW,IAENX,CACT,EAEAJ,EAAM,SAASmB,GACb,IAAI,IAAIlB,EAAI,EAAGA,EAAII,EAAMH,OAAQD,IAC5BI,EAAMJ,GAAGkB,SAAWA,IACrBd,EAAMJ,GAAGc,WAAY,EAG3B,CACF,CAEAK,EAAOC,QAAU,SAASC,GAIxB,OAAOvB,EAAIwB,KAAK7B,EAAM4B,EACxB,EACAF,EAAOC,QAAQG,OAAS,WACtBxB,EAAIyB,MAAM/B,EAAMgC,UAClB,EACAN,EAAOC,QAAQM,SAAW,SAASC,GAC5BA,IACHA,EAASlC,GAEXkC,EAAOC,sBAAwB9B,EAC/B6B,EAAOE,qBAAuB9B,CAChC,C,UCrEAoB,EAAOC,QAAU,SAASU,GACtBC,KAAKC,IAAK,EACVD,KAAKE,MAAQ,EAGiB,KAA1BH,EAAaI,OAAO,KACpBJ,EAAeA,EAAaK,OAAO,EAAE,IAIzCL,GADAA,EAAeA,EAAaM,QAAQ,KAAK,KACbC,cAI5B,IAAIC,EAAgB,CAChBC,UAAW,SACXC,aAAc,SACdC,KAAM,SACNC,WAAY,SACZC,MAAO,SACPC,MAAO,SACPC,OAAQ,SACRC,MAAO,SACPC,eAAgB,SAChBC,KAAM,SACNC,WAAY,SACZC,MAAO,SACPC,UAAW,SACXC,UAAW,SACXC,WAAY,SACZC,UAAW,SACXC,MAAO,SACPC,eAAgB,SAChBC,SAAU,SACVC,QAAS,SACTC,KAAM,SACNC,SAAU,SACVC,SAAU,SACVC,cAAe,SACfC,SAAU,SACVC,UAAW,SACXC,UAAW,SACXC,YAAa,SACbC,eAAgB,SAChBC,WAAY,SACZC,WAAY,SACZC,QAAS,SACTC,WAAY,SACZC,aAAc,SACdC,cAAe,SACfC,cAAe,SACfC,cAAe,SACfC,WAAY,SACZC,SAAU,SACVC,YAAa,SACbC,QAAS,SACTC,WAAY,SACZC,SAAU,SACVC,UAAW,SACXC,YAAa,SACbC,YAAa,SACbC,QAAS,SACTC,UAAW,SACXC,WAAY,SACZC,KAAM,SACNC,UAAW,SACXC,KAAM,SACNC,MAAO,SACPC,YAAa,SACbC,SAAU,SACVC,QAAS,SACTC,UAAY,SACZC,OAAS,SACTC,MAAO,SACPC,MAAO,SACPC,SAAU,SACVC,cAAe,SACfC,UAAW,SACXC,aAAc,SACdC,UAAW,SACXC,WAAY,SACZC,UAAW,SACXC,qBAAsB,SACtBC,UAAW,SACXC,WAAY,SACZC,UAAW,SACXC,YAAa,SACbC,cAAe,SACfC,aAAc,SACdC,eAAgB,SAChBC,eAAgB,SAChBC,eAAgB,SAChBC,YAAa,SACbC,KAAM,SACNC,UAAW,SACXC,MAAO,SACPC,QAAS,SACTC,OAAQ,SACRC,iBAAkB,SAClBC,WAAY,SACZC,aAAc,SACdC,aAAc,SACdC,eAAgB,SAChBC,gBAAiB,SACjBC,kBAAmB,SACnBC,gBAAiB,SACjBC,gBAAiB,SACjBC,aAAc,SACdC,UAAW,SACXC,UAAW,SACXC,SAAU,SACVC,YAAa,SACbC,KAAM,SACNC,QAAS,SACTC,MAAO,SACPC,UAAW,SACXC,OAAQ,SACRC,UAAW,SACXC,OAAQ,SACRC,cAAe,SACfC,UAAW,SACXC,cAAe,SACfC,cAAe,SACfC,WAAY,SACZC,UAAW,SACXC,KAAM,SACNC,KAAM,SACNC,KAAM,SACNC,WAAY,SACZC,OAAQ,SACRC,cAAe,SACfC,IAAK,SACLC,UAAW,SACXC,UAAW,SACXC,YAAa,SACbC,OAAQ,SACRC,WAAY,SACZC,SAAU,SACVC,SAAU,SACVC,OAAQ,SACRC,OAAQ,SACRC,QAAS,SACTC,UAAW,SACXC,UAAW,SACXC,KAAM,SACNC,YAAa,SACbC,UAAW,SACXC,IAAK,SACLC,KAAM,SACNC,QAAS,SACTC,OAAQ,SACRC,UAAW,SACXC,OAAQ,SACRC,UAAW,SACXC,MAAO,SACPC,MAAO,SACPC,WAAY,SACZC,OAAQ,SACRC,YAAa,UAEjBxJ,EAAeQ,EAAcR,IAAiBA,EAqD9C,IAjDA,IAAIyJ,EAAa,CACb,CACIC,GAAI,kEACJC,QAAS,CAAC,0BAA2B,yBACrCC,QAAS,SAAUC,GACf,MAAO,CACHC,SAASD,EAAK,IACdC,SAASD,EAAK,IACdC,SAASD,EAAK,IACdE,WAAWF,EAAK,IAExB,GAEJ,CACIH,GAAI,+CACJC,QAAS,CAAC,oBAAqB,oBAC/BC,QAAS,SAAUC,GACf,MAAO,CACHC,SAASD,EAAK,IACdC,SAASD,EAAK,IACdC,SAASD,EAAK,IAEtB,GAEJ,CACIH,GAAI,qDACJC,QAAS,CAAC,UAAW,UACrBC,QAAS,SAAUC,GACf,MAAO,CACHC,SAASD,EAAK,GAAI,IAClBC,SAASD,EAAK,GAAI,IAClBC,SAASD,EAAK,GAAI,IAE1B,GAEJ,CACIH,GAAI,qDACJC,QAAS,CAAC,OAAQ,OAClBC,QAAS,SAAUC,GACf,MAAO,CACHC,SAASD,EAAK,GAAKA,EAAK,GAAI,IAC5BC,SAASD,EAAK,GAAKA,EAAK,GAAI,IAC5BC,SAASD,EAAK,GAAKA,EAAK,GAAI,IAEpC,IAKC3L,EAAI,EAAGA,EAAIuL,EAAWtL,OAAQD,IAAK,CACxC,IAAIwL,EAAKD,EAAWvL,GAAGwL,GACnBM,EAAYP,EAAWvL,GAAG0L,QAC1BC,EAAOH,EAAGO,KAAKjK,GACnB,GAAI6J,EAAM,CACN,IAAIK,EAAWF,EAAUH,GACzB5J,KAAKkK,EAAID,EAAS,GAClBjK,KAAKmK,EAAIF,EAAS,GAClBjK,KAAKoK,EAAIH,EAAS,GACdA,EAAS/L,OAAS,IAClB8B,KAAKE,MAAQ+J,EAAS,IAE1BjK,KAAKC,IAAK,CACd,CAEJ,CAGAD,KAAKkK,EAAKlK,KAAKkK,EAAI,GAAKG,MAAMrK,KAAKkK,GAAM,EAAMlK,KAAKkK,EAAI,IAAO,IAAMlK,KAAKkK,EAC1ElK,KAAKmK,EAAKnK,KAAKmK,EAAI,GAAKE,MAAMrK,KAAKmK,GAAM,EAAMnK,KAAKmK,EAAI,IAAO,IAAMnK,KAAKmK,EAC1EnK,KAAKoK,EAAKpK,KAAKoK,EAAI,GAAKC,MAAMrK,KAAKoK,GAAM,EAAMpK,KAAKoK,EAAI,IAAO,IAAMpK,KAAKoK,EAC1EpK,KAAKE,MAASF,KAAKE,MAAQ,EAAK,EAAMF,KAAKE,MAAQ,GAAOmK,MAAMrK,KAAKE,OAAU,EAAMF,KAAKE,MAG1FF,KAAKsK,MAAQ,WACT,MAAO,OAAStK,KAAKkK,EAAI,KAAOlK,KAAKmK,EAAI,KAAOnK,KAAKoK,EAAI,GAC7D,EACApK,KAAKuK,OAAS,WACV,MAAO,QAAUvK,KAAKkK,EAAI,KAAOlK,KAAKmK,EAAI,KAAOnK,KAAKoK,EAAI,KAAOpK,KAAKE,MAAQ,GAClF,EACAF,KAAKwK,MAAQ,WACT,IAAIN,EAAIlK,KAAKkK,EAAEO,SAAS,IACpBN,EAAInK,KAAKmK,EAAEM,SAAS,IACpBL,EAAIpK,KAAKoK,EAAEK,SAAS,IAIxB,OAHgB,GAAZP,EAAEhM,SAAagM,EAAI,IAAMA,GACb,GAAZC,EAAEjM,SAAaiM,EAAI,IAAMA,GACb,GAAZC,EAAElM,SAAakM,EAAI,IAAMA,GACtB,IAAMF,EAAIC,EAAIC,CACzB,EAGApK,KAAK0K,WAAa,WAId,IAFA,IAAIC,EAAW,IAAIC,MAEV3M,EAAI,EAAGA,EAAIuL,EAAWtL,OAAQD,IAEnC,IADA,IAAIyL,EAAUF,EAAWvL,GAAGyL,QACnBmB,EAAI,EAAGA,EAAInB,EAAQxL,OAAQ2M,IAChCF,EAASA,EAASzM,QAAUwL,EAAQmB,GAI5C,IAAK,IAAIC,KAAMvK,EACXoK,EAASA,EAASzM,QAAU4M,EAGhC,IAAIC,EAAMC,SAASC,cAAc,MACjCF,EAAIG,aAAa,KAAM,qBACvB,IAASjN,EAAI,EAAGA,EAAI0M,EAASzM,OAAQD,IACjC,IACI,IAAIkN,EAAYH,SAASC,cAAc,MACnCG,EAAa,IAAIC,SAASV,EAAS1M,IACnCqN,EAAcN,SAASC,cAAc,OACzCK,EAAYC,MAAMC,QACV,oDAEkBJ,EAAWZ,QAF7B,WAGaY,EAAWZ,QAEhCc,EAAYG,YAAYT,SAASU,eAAe,SAChD,IAAIC,EAAkBX,SAASU,eAC3B,IAAMf,EAAS1M,GAAK,OAASmN,EAAWd,QAAU,OAASc,EAAWZ,SAE1EW,EAAUM,YAAYH,GACtBH,EAAUM,YAAYE,GACtBZ,EAAIU,YAAYN,EAEpB,CAAE,MAAMnM,GAAG,CAEf,OAAO+L,CAEX,CAEJ,C,mBC7SA,eAAAa,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAG,qBAAAC,aAAA,OAAAA,aAAiBA,YAAY1O,IAC9B4B,EAAOC,QAAU,W,OAAG6M,YAAY1O,KAAf,EACX,qBAAAmM,SAAA,OAAAA,SAAaA,QAAQkC,QAC3BzM,EAAOC,QAAU,W,OAAIuM,IAAmBI,GAAgB,GAAvC,EACjBH,EAASlC,QAAQkC,OAIjBE,GAHAH,EAAiB,WACf,IAAAO,E,OACQ,KADRA,EAAKN,KACF,GAAWM,EAAG,EAFF,KAIjBF,EAA4B,IAAnBtC,QAAQyC,SACjBJ,EAAeD,EAAiBE,GAC1BI,KAAK7O,KACX4B,EAAOC,QAAU,W,OAAGgN,KAAK7O,MAAQsO,CAAhB,EACjBA,EAAWO,KAAK7O,QAEhB4B,EAAOC,QAAU,W,OAAO,IAAAgN,MAAOC,UAAYR,CAA1B,EACjBA,GAAe,IAAAO,MAAOC,U,0CChBxB,SAASC,EAAmBC,EAAGC,EAAGzN,EAAGkL,EAAGwC,EAAGC,EAAGC,GAC5C,IACE,IAAI3O,EAAIuO,EAAEG,GAAGC,GACXC,EAAI5O,EAAE6O,KACV,CAAE,MAAON,GACP,YAAYxN,EAAEwN,EAChB,CACAvO,EAAE8O,KAAON,EAAEI,GAAKG,QAAQC,QAAQJ,GAAGK,KAAKhD,EAAGwC,EAC7C,CACA,SAASS,EAAkBX,GACzB,OAAO,WACL,IAAIC,EAAIzM,KACNhB,EAAIU,UACN,OAAO,IAAIsN,SAAQ,SAAU9C,EAAGwC,GAC9B,IAAIC,EAAIH,EAAE/M,MAAMgN,EAAGzN,GACnB,SAASoO,EAAMZ,GACbD,EAAmBI,EAAGzC,EAAGwC,EAAGU,EAAOC,EAAQ,OAAQb,EACrD,CACA,SAASa,EAAOb,GACdD,EAAmBI,EAAGzC,EAAGwC,EAAGU,EAAOC,EAAQ,QAASb,EACtD,CACAY,OAAM,EACR,GACF,CACF,C,soDCtBA,SAASE,EAAcb,GACrB,IAAIxO,ECFN,SAAqBwO,EAAGvC,GACtB,GAAI,WAAY,OAAQuC,KAAOA,EAAG,OAAOA,EACzC,IAAIzN,EAAIyN,EAAEc,OAAOC,aACjB,QAAI,IAAWxO,EAAG,CAChB,IAAIf,EAAIe,EAAEO,KAAKkN,EAAGvC,GAAK,WACvB,GAAI,WAAY,OAAQjM,GAAI,OAAOA,EACnC,MAAM,IAAIwP,UAAU,+CACtB,CACA,OAAQ,WAAavD,EAAIwD,OAASC,QAAQlB,EAC5C,CDPUe,CAAYf,EAAG,UACvB,MAAO,WAAY,OAAQxO,GAAKA,EAAIA,EAAI,EAC1C,CEJA,SAAS2P,EAAgB5O,EAAGkL,EAAGuC,GAC7B,OAAQvC,EAAIoD,EAAcpD,MAAOlL,EAAI6O,OAAOC,eAAe9O,EAAGkL,EAAG,CAC/D4C,MAAOL,EACPsB,YAAY,EACZC,cAAc,EACdC,UAAU,IACPjP,EAAEkL,GAAKuC,EAAGzN,CACjB,C,qBCQIyN,EAAgB,SAASvC,EAAGlL,GAI5B,OAHAyN,EAAgBoB,OAAOK,gBAClB,CAAEC,UAAW,cAAgBvD,OAAS,SAAU6B,EAAGvC,GAAKuC,EAAE0B,UAAYjE,CAAA,GACvE,SAAUuC,EAAGvC,GAAK,IAAK,IAAIlL,KAAKkL,EAAO2D,OAAOO,UAAUC,eAAe9O,KAAK2K,EAAGlL,KAAIyN,EAAEzN,GAAKkL,EAAElL,GAAA,GAC3EkL,EAAGlL,EAAA,EAGrB,SAASkL,EAAUA,EAAGlL,GACzB,GAAiB,mBAANA,GAA0B,OAANA,EAC3B,MAAM,IAAIyO,UAAU,uBAAyBC,OAAO1O,GAAK,iCAE7D,SAASf,IAAO+B,KAAKsO,YAAcpE,CAAA,CADnCuC,EAAcvC,EAAGlL,GAEjBkL,EAAEkE,UAAkB,OAANpP,EAAa6O,OAAOU,OAAOvP,IAAMf,EAAGmQ,UAAYpP,EAAEoP,UAAW,IAAInQ,EAAA,CCgC1E,SCzDOA,EAAOwO,EAA0BvC,GAAA,IAAzBlL,EAAAyN,EAAA,GAAGxO,EAAAwO,EAAA,GACzB,MAAO,CACLzN,EAAIN,KAAK8P,IAAItE,GAAOjM,EAAIS,KAAK+P,IAAIvE,GACjClL,EAAIN,KAAK+P,IAAIvE,GAAOjM,EAAIS,KAAK8P,IAAItE,GAAA,UAKrByC,IAAA,IAAc,IAAAF,EAAA,GAAAvC,EAAA,EAAAA,EAAAxK,UAAAxB,OAAAgM,IAAAuC,EAAAvC,GAAAxK,UAAAwK,GAE1B,IAAK,IAAIlL,EAAI,EAAGA,EAAIyN,EAAQvO,OAAQc,IAClC,GAAI,iBAAoByN,EAAQzN,GAC9B,MAAM,IAAI0P,MACR,2BAA2B1P,EAAA,6BAA8ByN,EAAQzN,GAAA,cAAgByN,EAAQzN,IAIjG,OAAO,EAGT,IAAMwN,EAAK9N,KAAKiQ,GAAA,SASAjC,EAAmBD,EAAavC,EAAYlL,GAC1DyN,EAAEmC,SAAY,IAAMnC,EAAEmC,SAAY,EAAI,EACtCnC,EAAEoC,UAAa,IAAMpC,EAAEoC,UAAa,EAAI,EAEnC,IAAAlC,EAAgBF,EAAAqC,GAAZpC,EAAYD,EAAAsC,GAARC,EAAQvC,EAAAwC,EAALpC,EAAKJ,EAAAyC,EAErBvC,EAAKjO,KAAKyQ,IAAI1C,EAAEqC,IAChBpC,EAAKhO,KAAKyQ,IAAI1C,EAAEsC,IACV,IAAAK,EAAanR,EAAO,EAAEiM,EAAK8E,GAAK,GAAIhQ,EAAK6N,GAAK,IAAKJ,EAAE4C,KAAO,IAAM7C,GAAjEI,EAAAwC,EAAA,GAAKF,EAAAE,EAAA,GACNE,EAAY5Q,KAAK6Q,IAAI3C,EAAK,GAAKlO,KAAK6Q,IAAI5C,EAAI,GAAKjO,KAAK6Q,IAAIL,EAAK,GAAKxQ,KAAK6Q,IAAI7C,EAAI,GAEnF,EAAI4C,IACN3C,GAAMjO,KAAK8Q,KAAKF,GAChB5C,GAAMhO,KAAK8Q,KAAKF,IAElB7C,EAAEqC,GAAKnC,EACPF,EAAEsC,GAAKrC,EACP,IAAM+C,EAAe/Q,KAAK6Q,IAAI5C,EAAI,GAAKjO,KAAK6Q,IAAIL,EAAK,GAAKxQ,KAAK6Q,IAAI7C,EAAI,GAAKhO,KAAK6Q,IAAI3C,EAAK,GACpF8C,GAAWjD,EAAEmC,WAAanC,EAAEoC,UAAY,GAAK,GACjDnQ,KAAK8Q,KAAK9Q,KAAKC,IAAI,GAAID,KAAK6Q,IAAI5C,EAAI,GAAKjO,KAAK6Q,IAAI7C,EAAI,GAAK+C,GAAeA,IACtEE,EAAMhD,EAAKuC,EAAMxC,EAAKgD,EACtBE,GAAOlD,EAAKE,EAAMD,EAAK+C,EACvBG,EAAO5R,EAAO,CAAC0R,EAAKC,GAAMnD,EAAE4C,KAAO,IAAM7C,GAE/CC,EAAEqD,GAAKD,EAAK,IAAM3F,EAAK8E,GAAK,EAC5BvC,EAAEsD,GAAKF,EAAK,IAAM7Q,EAAK6N,GAAK,EAC5BJ,EAAEuD,KAAOtR,KAAKuR,OAAOf,EAAMU,GAAOlD,GAAKE,EAAM+C,GAAOhD,GACpDF,EAAEyD,KAAOxR,KAAKuR,QAAQf,EAAMU,GAAOlD,IAAME,EAAM+C,GAAOhD,GAClD,IAAMF,EAAEoC,WAAapC,EAAEyD,KAAOzD,EAAEuD,OAClCvD,EAAEyD,MAAQ,EAAI1D,GAEZ,IAAMC,EAAEoC,WAAapC,EAAEyD,KAAOzD,EAAEuD,OAClCvD,EAAEyD,MAAQ,EAAI1D,GAEhBC,EAAEuD,MAAQ,IAAMxD,EAChBC,EAAEyD,MAAQ,IAAM1D,CAAA,UAaFwC,EAA2BvC,EAAWvC,EAAWlL,GAC/D2N,EAAcF,EAAGvC,EAAGlL,GAEpB,IAAMf,EAAUwO,EAAIA,EAAIvC,EAAIA,EAAIlL,EAAIA,EAEpC,GAAI,EAAIf,EACN,MAAO,GACF,GAAI,IAAMA,EACf,MAAO,CACL,CACGwO,EAAIzN,GAAMyN,EAAIA,EAAIvC,EAAIA,GACtBA,EAAIlL,GAAMyN,EAAIA,EAAIvC,EAAIA,KAE7B,IAAMsC,EAAO9N,KAAK8Q,KAAKvR,GAEvB,MAAO,CACL,EACGwO,EAAIzN,EAAIkL,EAAIsC,IAASC,EAAIA,EAAIvC,EAAIA,IACjCA,EAAIlL,EAAIyN,EAAID,IAASC,EAAIA,EAAIvC,EAAIA,IACpC,EACGuC,EAAIzN,EAAIkL,EAAIsC,IAASC,EAAIA,EAAIvC,EAAIA,IACjCA,EAAIlL,EAAIyN,EAAID,IAASC,EAAIA,EAAIvC,EAAIA,IAAA,CAIjC,ICjGU2C,EDiGJuC,EAAM1Q,KAAKiQ,GAAK,aAEb/B,EAAKH,EAAWvC,EAAWlL,GACzC,OAAQ,EAAIA,GAAKyN,EAAIzN,EAAIkL,CAAA,UAGXgF,EAAMzC,EAAWvC,EAAYlL,EAAYf,GACvD,OAAOwO,EAAI/N,KAAK8P,IAAIvQ,EAAS,IAAMuO,GAAMtC,EAAKxL,KAAK+P,IAAIxQ,EAAS,IAAMuO,GAAMxN,CAAA,UAG9DsQ,EAAW7C,EAAYvC,EAAYlL,EAAYf,GAC7D,IAAM0O,EAAM,KACNH,EAAMtC,EAAKuC,EACXC,EAAM1N,EAAKkL,EAEX8E,EAAI,EAAIxC,EAAM,GADRvO,EAAKe,GACa,EAAI0N,EAC5BG,EAAkB,GAAbH,EAAMF,GACX4C,EAAI,EAAI5C,EAGd,OAAI9N,KAAKyQ,IAAIH,GAAKrC,EAET,EAAEyC,EAAIvC,GAiBjB,SAAmBJ,EAAWvC,EAAWlL,QAAA,IAAAA,IAAAA,EAAA,MAEvC,IAAMf,EAAiBwO,EAAIA,EAAI,EAAIvC,EAEnC,GAAIjM,GAAkBe,EACpB,MAAO,GACF,GAAIf,GAAkBe,EAC3B,MAAO,EAAEyN,EAAI,GAEf,IAAME,EAAOjO,KAAK8Q,KAAKvR,GAEvB,MAAO,EAAGwO,EAAI,EAAKE,GAAQF,EAAI,EAAKE,EAAA,CAXtC,CAfmBE,EAAImC,EAAGI,EAAIJ,EAAGrC,EAAA,UAIjB8C,EAAShD,EAAYvC,EAAYlL,EAAYf,EAAY0O,GAEvE,IAAMH,EAAI,EAAIG,EAMd,OAAOF,GALID,EAAIA,EAAIA,GAKFtC,GAJN,EAAIsC,EAAIA,EAAIG,GAII3N,GAHhB,EAAIwN,EAAIG,EAAIA,GAGc1O,GAF1B0O,EAAIA,EAAIA,EAAA,ECnIrB,SAAiBF,GAuCf,SAAgBvC,IACd,OAAO2C,GAAK,SAACJ,EAASvC,EAAOlL,GAyB3B,OAxBIyN,EAAQ0D,gBAAA,IAEiB1D,EAAQ2D,KACjC3D,EAAQ2D,IAAMlG,QAAA,IAEWuC,EAAQ4D,KACjC5D,EAAQ4D,IAAMrR,QAAA,IAGWyN,EAAQ6D,KACjC7D,EAAQ6D,IAAMpG,QAAA,IAEWuC,EAAQ8D,KACjC9D,EAAQ8D,IAAMvR,QAAA,IAGWyN,EAAQwC,IACjCxC,EAAQwC,GAAK/E,QAAA,IAEYuC,EAAQyC,IACjCzC,EAAQyC,GAAKlQ,GAEfyN,EAAQ0D,UAAA,GAEH1D,CAAA,IAkEX,SAAgBzN,IACd,IAAIyN,EAAe+D,IACftG,EAAesG,IACfxR,EAAawR,IACbvS,EAAauS,IAEjB,OAAO3D,GAAK,SAACF,EAASH,EAAOE,GA8B3B,OA7BIC,EAAQ8D,KAAOC,EAAYC,kBAC7BhE,EAAQ8D,KAAOC,EAAYE,SAC3BnE,EAAepC,MAAMoC,GAAgBD,EAAQC,EAC7CvC,EAAeG,MAAMH,GAAgBwC,EAAQxC,EAC7CyC,EAAQyD,GAAKzD,EAAQwD,SAAW3D,EAAQC,EAAe,EAAID,EAAQC,EACnEE,EAAQ0D,GAAK1D,EAAQwD,SAAWzD,EAAQxC,EAAe,EAAIwC,EAAQxC,GAEjEyC,EAAQ8D,KAAOC,EAAYE,UAC7BnE,EAAeE,EAAQwD,SAAW3D,EAAQG,EAAQ2D,GAAK3D,EAAQ2D,GAC/DpG,EAAeyC,EAAQwD,SAAWzD,EAAQC,EAAQ4D,GAAK5D,EAAQ4D,KAE/D9D,EAAe+D,IACftG,EAAesG,KAEb7D,EAAQ8D,KAAOC,EAAYG,iBAC7BlE,EAAQ8D,KAAOC,EAAYI,QAC3B9R,EAAaqL,MAAMrL,GAAcwN,EAAQxN,EACzCf,EAAaoM,MAAMpM,GAAcyO,EAAQzO,EACzC0O,EAAQyD,GAAKzD,EAAQwD,SAAW3D,EAAQxN,EAAa,EAAIwN,EAAQxN,EACjE2N,EAAQ0D,GAAK1D,EAAQwD,SAAWzD,EAAQzO,EAAa,EAAIyO,EAAQzO,GAE/D0O,EAAQ8D,KAAOC,EAAYI,SAC7B9R,EAAa2N,EAAQwD,SAAW3D,EAAQG,EAAQyD,GAAKzD,EAAQyD,GAC7DnS,EAAa0O,EAAQwD,SAAWzD,EAAQC,EAAQ0D,GAAK1D,EAAQ0D,KAE7DrR,EAAawR,IACbvS,EAAauS,KAGR7D,CAAA,IAYX,SAAgBH,IACd,IAAIC,EAAa+D,IACbtG,EAAasG,IAEjB,OAAO3D,GAAK,SAAC7N,EAASf,EAAO0O,GAQ3B,GAPI3N,EAAQyR,KAAOC,EAAYG,iBAC7B7R,EAAQyR,KAAOC,EAAYI,QAC3BrE,EAAapC,MAAMoC,GAAcxO,EAAQwO,EACzCvC,EAAaG,MAAMH,GAAcyC,EAAQzC,EACzClL,EAAQoR,GAAKpR,EAAQmR,SAAWlS,EAAQwO,EAAa,EAAIxO,EAAQwO,EACjEzN,EAAQqR,GAAKrR,EAAQmR,SAAWxD,EAAQzC,EAAa,EAAIyC,EAAQzC,GAE/DlL,EAAQyR,KAAOC,EAAYI,QAAS,CACtCrE,EAAazN,EAAQmR,SAAWlS,EAAQe,EAAQoR,GAAKpR,EAAQoR,GAC7DlG,EAAalL,EAAQmR,SAAWxD,EAAQ3N,EAAQqR,GAAKrR,EAAQqR,GAC7D,IAAM7D,EAAKxN,EAAQoR,GACb1D,EAAK1N,EAAQqR,GAEnBrR,EAAQyR,KAAOC,EAAYE,SAC3B5R,EAAQoR,KAAOpR,EAAQmR,SAAW,EAAIlS,GAAc,EAALuO,GAAU,EACzDxN,EAAQqR,KAAOrR,EAAQmR,SAAW,EAAIxD,GAAc,EAALD,GAAU,EACzD1N,EAAQsR,IAAMtR,EAAQiQ,EAAS,EAALzC,GAAU,EACpCxN,EAAQuR,IAAMvR,EAAQkQ,EAAS,EAALxC,GAAU,OAEpCD,EAAa+D,IACbtG,EAAasG,IAGf,OAAOxR,CAAA,IAGX,SAAgB6N,EACdJ,GAEA,IAAIvC,EAAW,EACXlL,EAAW,EACXf,EAAgBuS,IAChB7D,EAAgB6D,IAEpB,OAAO,SAAmBhE,GACxB,GAAInC,MAAMpM,MAAoBuO,EAAQiE,KAAOC,EAAYK,SACvD,MAAM,IAAIrC,MAAM,+BAGlB,IAAMhC,EAASD,EAAED,EAAStC,EAAUlL,EAAUf,EAAe0O,GAmB7D,OAjBIH,EAAQiE,KAAOC,EAAYM,aAC7B9G,EAAWjM,EACXe,EAAW2N,QAAA,IAGcH,EAAQyC,IACjC/E,EAAYsC,EAAQ2D,SAAWjG,EAAWsC,EAAQyC,EAAIzC,EAAQyC,QAAA,IAErCzC,EAAQ0C,IACjClQ,EAAYwN,EAAQ2D,SAAWnR,EAAWwN,EAAQ0C,EAAI1C,EAAQ0C,GAG5D1C,EAAQiE,KAAOC,EAAYK,UAC7B9S,EAAgBiM,EAChByC,EAAgB3N,GAGX0N,CAAA,EAoFX,SAAgBgD,EAAOjD,EAAWvC,EAAWlL,EAAWf,EAAWuO,EAAWE,GAG5E,OAFAC,EAAcF,EAAGvC,EAAGlL,EAAGf,EAAGuO,EAAGE,GAEtBG,GAAK,SAACF,EAASqC,EAAOnC,EAAOuC,GAClC,IAAMxC,EAASD,EAAQyD,GACjBlB,EAASvC,EAAQ2D,GAGjBhB,EAAS3C,EAAQwD,WAAa9F,MAAM+E,GACpCK,OAAA,IAA2B9C,EAAQsC,EAAItC,EAAQsC,EAAKK,EAAS,EAAIN,EACjEU,OAAA,IAA2B/C,EAAQuC,EAAIvC,EAAQuC,EAAKI,EAAS,EAAIzC,EA6BvE,SAAS8C,EAAIlD,GAAa,OAAOA,EAAIA,CAAA,CA3BjCE,EAAQ8D,KAAOC,EAAYO,eAAiB,IAAM/G,IACpDyC,EAAQ8D,KAAOC,EAAYQ,QAC3BvE,EAAQuC,EAAIvC,EAAQwD,SAAW,EAAItD,GAEjCF,EAAQ8D,KAAOC,EAAYS,cAAgB,IAAMnS,IACnD2N,EAAQ8D,KAAOC,EAAYQ,QAC3BvE,EAAQsC,EAAItC,EAAQwD,SAAW,EAAInB,QAAA,IAGVrC,EAAQsC,IACjCtC,EAAQsC,EAAKtC,EAAQsC,EAAIxC,EAAMiD,EAAI1Q,GAAMsQ,EAAS,EAAI9C,SAAA,IAE7BG,EAAQuC,IACjCvC,EAAQuC,EAAKO,EAAIvF,EAAKyC,EAAQuC,EAAIjR,GAAKqR,EAAS,EAAI5C,SAAA,IAE3BC,EAAQyD,KACjCzD,EAAQyD,GAAKzD,EAAQyD,GAAK3D,EAAIE,EAAQ0D,GAAKrR,GAAKsQ,EAAS,EAAI9C,SAAA,IAEpCG,EAAQ0D,KACjC1D,EAAQ0D,GAAKzD,EAAS1C,EAAIyC,EAAQ0D,GAAKpS,GAAKqR,EAAS,EAAI5C,SAAA,IAEhCC,EAAQ2D,KACjC3D,EAAQ2D,GAAK3D,EAAQ2D,GAAK7D,EAAIE,EAAQ4D,GAAKvR,GAAKsQ,EAAS,EAAI9C,SAAA,IAEpCG,EAAQ4D,KACjC5D,EAAQ4D,GAAKrB,EAAShF,EAAIyC,EAAQ4D,GAAKtS,GAAKqR,EAAS,EAAI5C,IAG3D,IAAMkD,EAAMnD,EAAIxO,EAAIiM,EAAIlL,EAExB,QAAI,IAAuB2N,EAAQ0C,OAE7B,IAAM5C,GAAK,IAAMvC,GAAK,IAAMlL,GAAK,IAAMf,GAEzC,GAAI,IAAM2R,SAIDjD,EAAQmC,UACRnC,EAAQoC,UACRpC,EAAQ0C,YACR1C,EAAQiC,gBACRjC,EAAQkC,UACflC,EAAQ8D,KAAOC,EAAYQ,YACtB,CAEL,IAAMrB,EAAOlD,EAAQ0C,KAAO3Q,KAAKiQ,GAAK,IAOhCyC,EAAS1S,KAAK+P,IAAIoB,GAClBwB,EAAS3S,KAAK8P,IAAIqB,GAClBZ,EAAS,EAAIU,EAAIhD,EAAQmC,IACzBwC,EAAS,EAAI3B,EAAIhD,EAAQoC,IACzBwC,EAAI5B,EAAI0B,GAAUpC,EAASU,EAAIyB,GAAUE,EACzCE,EAAI,EAAIJ,EAASC,GAAUpC,EAASqC,GACpCG,EAAI9B,EAAIyB,GAAUnC,EAASU,EAAI0B,GAAUC,EAOzCI,EAAKH,EAAItT,EAAIA,EAAIuT,EAAItH,EAAIjM,EAAIwT,EAAIvH,EAAIA,EACrCyH,EAAKH,GAAK/E,EAAIxO,EAAIiM,EAAIlL,GAAK,GAAKuS,EAAIvS,EAAIf,EAAIwT,EAAIhF,EAAIvC,GACpDC,EAAKoH,EAAIvS,EAAIA,EAAIwS,EAAI/E,EAAIzN,EAAIyS,EAAIhF,EAAIA,EAerCmF,GAAYlT,KAAKuR,MAAM0B,EAAID,EAAKvH,GAAMzL,KAAKiQ,IAAMjQ,KAAKiQ,GAAM,EAM5DkD,EAAYnT,KAAK+P,IAAImD,GACrBE,EAAYpT,KAAK8P,IAAIoD,GAE3BjF,EAAQmC,GAAKpQ,KAAKyQ,IAAIS,GACpBlR,KAAK8Q,KAAKkC,EAAK/B,EAAImC,GAAaH,EAAKE,EAAYC,EAAY3H,EAAKwF,EAAIkC,IACxElF,EAAQoC,GAAKrQ,KAAKyQ,IAAIS,GACpBlR,KAAK8Q,KAAKkC,EAAK/B,EAAIkC,GAAaF,EAAKE,EAAYC,EAAY3H,EAAKwF,EAAImC,IACxEnF,EAAQ0C,KAAiB,IAAVuC,EAAgBlT,KAAKiQ,EAAA,CAW1C,YAAO,IAHoBhC,EAAQkC,WAAa,EAAIe,IAClDjD,EAAQkC,YAAclC,EAAQkC,WAEzBlC,CAAA,IA1bKF,EAAAsF,MAAhB,SAAsBtF,GAEpB,SAASvC,EAAGA,GAAe,OAAOxL,KAAKO,MAAMiL,EAAMuC,GAAYA,CAAA,CAC/D,YAAO,IAAPA,IAHoBA,EAAA,MACpBE,EAAcF,GAEP,SAAeA,GA6BpB,YAAO,IA5BoBA,EAAQ2D,KACjC3D,EAAQ2D,GAAKlG,EAAGuC,EAAQ2D,UAAA,IAEC3D,EAAQ4D,KACjC5D,EAAQ4D,GAAKnG,EAAGuC,EAAQ4D,UAAA,IAGC5D,EAAQ6D,KACjC7D,EAAQ6D,GAAKpG,EAAGuC,EAAQ6D,UAAA,IAEC7D,EAAQ8D,KACjC9D,EAAQ8D,GAAKrG,EAAGuC,EAAQ8D,UAAA,IAGC9D,EAAQwC,IACjCxC,EAAQwC,EAAI/E,EAAGuC,EAAQwC,SAAA,IAEExC,EAAQyC,IACjCzC,EAAQyC,EAAIhF,EAAGuC,EAAQyC,SAAA,IAGEzC,EAAQqC,KACjCrC,EAAQqC,GAAK5E,EAAGuC,EAAQqC,UAAA,IAECrC,EAAQsC,KACjCtC,EAAQsC,GAAK7E,EAAGuC,EAAQsC,KAGnBtC,CAAA,GAIKA,EAAAuF,OAAA9H,EA8BAuC,EAAAwF,OAAhB,WACE,OAAOpF,GAAK,SAACJ,EAASvC,EAAOlL,GAyB3B,OAxBKyN,EAAQ0D,gBAAA,IAEgB1D,EAAQ2D,KACjC3D,EAAQ2D,IAAMlG,QAAA,IAEWuC,EAAQ4D,KACjC5D,EAAQ4D,IAAMrR,QAAA,IAGWyN,EAAQ6D,KACjC7D,EAAQ6D,IAAMpG,QAAA,IAEWuC,EAAQ8D,KACjC9D,EAAQ8D,IAAMvR,QAAA,IAGWyN,EAAQwC,IACjCxC,EAAQwC,GAAK/E,QAAA,IAEYuC,EAAQyC,IACjCzC,EAAQyC,GAAKlQ,GAEfyN,EAAQ0D,UAAA,GAEH1D,CAAA,KAIKA,EAAAyF,cAAhB,SAA8BzF,EAAmBvC,EAAmBlL,GAClE,YAAO,IAAPyN,IAD4BA,GAAA,YAAAvC,IAAmBA,GAAA,YAAAlL,IAAmBA,GAAA,GAC3D6N,GAAK,SAAC5O,EAAS0O,EAAOH,EAAOE,EAAYsC,GAC9C,GAAI3E,MAAMqC,MAAiBzO,EAAQwS,KAAOC,EAAYK,SACpD,MAAM,IAAIrC,MAAM,+BAuBlB,OArBIxE,GAAcjM,EAAQwS,KAAOC,EAAYO,gBAC3ChT,EAAQwS,KAAOC,EAAYQ,QAC3BjT,EAAQiR,EAAIjR,EAAQkS,SAAW,EAAI3D,GAEjCxN,GAAcf,EAAQwS,KAAOC,EAAYS,eAC3ClT,EAAQwS,KAAOC,EAAYQ,QAC3BjT,EAAQgR,EAAIhR,EAAQkS,SAAW,EAAIxD,GAEjCF,GAAcxO,EAAQwS,KAAOC,EAAYM,aAC3C/S,EAAQwS,KAAOC,EAAYQ,QAC3BjT,EAAQgR,EAAIhR,EAAQkS,SAAWzD,EAAaC,EAAQD,EACpDzO,EAAQiR,EAAIjR,EAAQkS,SAAWnB,EAAaxC,EAAQwC,GAElD/Q,EAAQwS,KAAOC,EAAYyB,MAAQ,IAAMlU,EAAQ6Q,IAAM,IAAM7Q,EAAQ8Q,MACvE9Q,EAAQwS,KAAOC,EAAYQ,eACpBjT,EAAQ6Q,UACR7Q,EAAQ8Q,UACR9Q,EAAQoR,YACRpR,EAAQ2Q,gBACR3Q,EAAQ4Q,WAEV5Q,CAAA,KAMKwO,EAAA2F,aAAApT,EAgDAyN,EAAA4F,QAAA7F,EA+BAC,EAAA6F,KAAAzF,EAsCAJ,EAAA8F,SAAhB,SAAyB9F,QAAA,IAAAA,IAAAA,EAAA,GACvBE,EAAcF,GACd,IAAIvC,EAAesG,IACfxR,EAAewR,IACfvS,EAAauS,IACbhE,EAAagE,IAEjB,OAAO3D,GAAK,SAACF,EAASD,EAAOsC,EAAOnC,EAAYuC,GAC9C,IAAMxC,EAAMlO,KAAKyQ,IACbD,GAAA,EACAI,EAAQ,EACRG,EAAQ,EAwBZ,GAtBI9C,EAAQ8D,KAAOC,EAAYC,kBAC7BrB,EAAQjF,MAAMH,GAAgB,EAAIwC,EAAQxC,EAC1CuF,EAAQpF,MAAMrL,GAAgB,EAAIgQ,EAAQhQ,GAExC2N,EAAQ8D,MAAQC,EAAYE,SAAWF,EAAYC,kBACrDzG,EAAeyC,EAAQwD,SAAWzD,EAAQC,EAAQ2D,GAAK3D,EAAQ2D,GAC/DtR,EAAe2N,EAAQwD,SAAWnB,EAAQrC,EAAQ4D,GAAK5D,EAAQ4D,KAE/DrG,EAAesG,IACfxR,EAAewR,KAEb7D,EAAQ8D,KAAOC,EAAYG,gBAC7B5S,EAAaoM,MAAMpM,GAAcyO,EAAQ,EAAIA,EAAQzO,EACrDuO,EAAanC,MAAMmC,GAAcwC,EAAQ,EAAIA,EAAQxC,GAC5CG,EAAQ8D,KAAOC,EAAYI,SACpC7S,EAAa0O,EAAQwD,SAAWzD,EAAQC,EAAQyD,GAAKzD,EAAQyD,GAC7D5D,EAAaG,EAAQwD,SAAWnB,EAAQrC,EAAQ0D,GAAK1D,EAAQ4D,KAE7DtS,EAAauS,IACbhE,EAAagE,KAGX7D,EAAQ8D,KAAOC,EAAY8B,eAC7B7F,EAAQ8D,KAAOC,EAAYyB,MAAQ,IAAMxF,EAAQmC,IAAM,IAAMnC,EAAQoC,KAAOpC,EAAQiC,WACpFjC,EAAQ8D,KAAOC,EAAYE,UAAYjE,EAAQ8D,KAAOC,EAAYC,iBAClEhE,EAAQ8D,KAAOC,EAAYI,SAAWnE,EAAQ8D,KAAOC,EAAYG,eAAgB,CACjF,IAAMnB,OAAA,IAA8B/C,EAAQsC,EAAI,EAC7CtC,EAAQwD,SAAWxD,EAAQsC,EAAItC,EAAQsC,EAAIvC,EACxCiD,OAAA,IAA8BhD,EAAQuC,EAAI,EAC7CvC,EAAQwD,SAAWxD,EAAQuC,EAAIvC,EAAQuC,EAAIF,EAE9CM,EAASjF,MAAMpM,QAAA,IACU0O,EAAQyD,GAAKd,EAClC3C,EAAQwD,SAAWxD,EAAQsC,EACzBtC,EAAQyD,GAAK1D,EAHUzO,EAAayO,EAI1C+C,EAASpF,MAAMmC,QAAA,IACUG,EAAQ0D,GAAKZ,EAClC9C,EAAQwD,SAAWxD,EAAQuC,EACzBvC,EAAQ0D,GAAKrB,EAHUxC,EAAawC,EAK1C,IAAMY,OAAA,IAA+BjD,EAAQ2D,GAAK,EAC/C3D,EAAQwD,SAAWxD,EAAQsC,EAAItC,EAAQ2D,GAAK5D,EACzCmD,OAAA,IAA+BlD,EAAQ4D,GAAK,EAC/C5D,EAAQwD,SAAWxD,EAAQuC,EAAIvC,EAAQ4D,GAAKvB,EAE3CpC,EAAI8C,IAASjD,GAAOG,EAAI+C,IAASlD,GACnCG,EAAI0C,IAAU7C,GAAOG,EAAI6C,IAAUhD,GACnCG,EAAIgD,IAAUnD,GAAOG,EAAIiD,IAAUpD,IACnCyC,GAAA,EAAO,CAUX,OANIvC,EAAQ8D,KAAOC,EAAYM,YACzBpE,EAAIF,EAAQG,IAAeJ,GAAOG,EAAIoC,EAAQI,IAAe3C,IAC/DyC,GAAA,GAIGA,EAAO,GAAKvC,CAAA,KAOPF,EAAAgG,OAAA/C,EA0HAjD,EAAAiG,OAAhB,SAAuBjG,EAAWvC,EAAOlL,QAAA,IAAAkL,IAAPA,EAAA,YAAAlL,IAAOA,EAAA,GACvC2N,EAAcF,EAAGvC,EAAGlL,GACpB,IAAMf,EAAMS,KAAK+P,IAAIhC,GACfD,EAAM9N,KAAK8P,IAAI/B,GAErB,OAAOiD,EAAOlD,EAAKvO,GAAMA,EAAKuO,EAAKtC,EAAIA,EAAIsC,EAAMxN,EAAIf,EAAKe,EAAIkL,EAAIjM,EAAMe,EAAIwN,EAAA,EAE9DC,EAAAkG,UAAhB,SAA0BlG,EAAYvC,GAEpC,YAAO,IAAPA,IAFoCA,EAAA,GACpCyC,EAAcF,EAAIvC,GACXwF,EAAO,EAAG,EAAG,EAAG,EAAGjD,EAAIvC,EAAA,EAEhBuC,EAAAmG,MAAhB,SAAsBnG,EAAYvC,GAEhC,YAAO,IAAPA,IAFgCA,EAAAuC,GAChCE,EAAcF,EAAIvC,GACXwF,EAAOjD,EAAI,EAAG,EAAGvC,EAAI,EAAG,IAEjBuC,EAAAoG,OAAhB,SAAuBpG,GAErB,OADAE,EAAcF,GACPiD,EAAO,EAAG,EAAGhR,KAAKoU,KAAKrG,GAAI,EAAG,EAAG,IAE1BA,EAAAsG,OAAhB,SAAuBtG,GAErB,OADAE,EAAcF,GACPiD,EAAO,EAAGhR,KAAKoU,KAAKrG,GAAI,EAAG,EAAG,EAAG,IAE1BA,EAAAuG,gBAAhB,SAAgCvG,GAE9B,YAAO,IAAPA,IAF8BA,EAAA,GAC9BE,EAAcF,GACPiD,GAAQ,EAAG,EAAG,EAAG,EAAGjD,EAAS,IAEtBA,EAAAwG,gBAAhB,SAAgCxG,GAE9B,YAAO,IAAPA,IAF8BA,EAAA,GAC9BE,EAAcF,GACPiD,EAAO,EAAG,EAAG,GAAI,EAAG,EAAGjD,EAAA,EAGhBA,EAAAyG,OAAhB,WACE,OAAOrG,GAAK,SAACJ,EAASvC,EAAOlL,GAC3B,OAAI0R,EAAYyB,MAAQ1F,EAAQgE,KAAA,SD3UlBhE,EAAevC,EAAYlL,GAAA,IAAA2N,EAAAH,EAAAwC,EAAAnC,EACxCJ,EAAIqD,IACPpD,EAAmBD,EAAKvC,EAAIlL,GAQ9B,IALA,IAAMkQ,EAASxQ,KAAKyU,IAAI1G,EAAIuD,KAAOvD,EAAIyD,MAAiDZ,EAAhC5Q,KAAKC,IAAI8N,EAAIuD,KAAOvD,EAAIyD,MAA4BhB,EACtGO,EAAY/Q,KAAK0U,KAAK9D,EAAW,IAEjCI,EAAqB,IAAI9E,MAAM6E,GACjCE,EAAQzF,EAAI0F,EAAQ5Q,EACf6Q,EAAI,EAAGA,EAAIJ,EAAWI,IAAK,CAClC,IAAMuB,EAAWxE,EAAKH,EAAIuD,KAAOvD,EAAIyD,KAAOL,EAAIJ,GAC1C4B,EAASzE,EAAKH,EAAIuD,KAAOvD,EAAIyD,MAAQL,EAAI,GAAKJ,GAC9CR,EAAWoC,EAASD,EACpBE,EAAI,EAAI,EAAI5S,KAAKkK,IAAIqG,EAAWG,EAAM,GAEtCmC,EAAW,CACf7S,KAAK8P,IAAI4C,EAAWhC,GAAOkC,EAAI5S,KAAK+P,IAAI2C,EAAWhC,GACnD1Q,KAAK+P,IAAI2C,EAAWhC,GAAOkC,EAAI5S,KAAK8P,IAAI4C,EAAWhC,IAF9CoC,EAAAD,EAAA,GAAIE,EAAAF,EAAA,GAGLG,EAAS,CAAChT,KAAK8P,IAAI6C,EAASjC,GAAM1Q,KAAK+P,IAAI4C,EAASjC,IAAnDuC,EAAAD,EAAA,GAAGvH,EAAAuH,EAAA,GACJE,EAAW,CAACD,EAAIL,EAAI5S,KAAK+P,IAAI4C,EAASjC,GAAMjF,EAAImH,EAAI5S,KAAK8P,IAAI6C,EAASjC,IAArEyC,EAAAD,EAAA,GAAIE,EAAAF,EAAA,GACXlC,EAAOG,GAAK,CAACM,SAAU1D,EAAI0D,SAAUM,KAAMC,EAAYE,UACvD,IAAMyC,EAAY,SAACnJ,EAAWlL,GACtB,IAAA2N,EAAiB1O,EAAO,CAACiM,EAAIuC,EAAIqC,GAAI9P,EAAIyN,EAAIsC,IAAKtC,EAAI4C,MAArD7C,EAAAG,EAAA,GAAOD,EAAAC,EAAA,GACd,MAAO,CAACF,EAAIqD,GAAMtD,EAAOC,EAAIsD,GAAMrD,EAAA,EAErCC,EAA+B0G,EAAU7B,EAAIC,GAA5C/B,EAAOG,GAAGO,GAAAzD,EAAA,GAAI+C,EAAOG,GAAGQ,GAAA1D,EAAA,GACzBH,EAA+B6G,EAAUxB,EAAIC,GAA5CpC,EAAOG,GAAGS,GAAA9D,EAAA,GAAIkD,EAAOG,GAAGU,GAAA/D,EAAA,GACzBwC,EAA6BqE,EAAU1B,EAAGxH,GAAzCuF,EAAOG,GAAGZ,EAAAD,EAAA,GAAGU,EAAOG,GAAGX,EAAAF,EAAA,GACpBvC,EAAI0D,WACNT,EAAOG,GAAGO,IAAMT,EAChBD,EAAOG,GAAGQ,IAAMT,EAChBF,EAAOG,GAAGS,IAAMX,EAChBD,EAAOG,GAAGU,IAAMX,EAChBF,EAAOG,GAAGZ,GAAKU,EACfD,EAAOG,GAAGX,GAAKU,GAEhBD,GAAD9C,EAAiB,CAAC6C,EAAOG,GAAGZ,EAAGS,EAAOG,GAAGX,IAAA,GAAjCU,EAAA/C,EAAA,GAEV,OAAO6C,CAAA,CCoS6B,CACnBjD,EAASA,EAAQ0D,SAAW,EAAIjG,EAAOuC,EAAQ0D,SAAW,EAAInR,GAEpEyN,CAAA,KAIKA,EAAA6G,cAAhB,WACE,OAAOzG,GAAK,SAACJ,EAAGvC,EAAIlL,GAQlB,OAPIyN,EAAE0D,WACJjG,EAAK,EACLlL,EAAK,GAEH0R,EAAYyB,MAAQ1F,EAAEgE,MACxB/D,EAAmBD,EAAGvC,EAAIlL,GAErByN,CAAA,KAGKA,EAAA8G,MAAhB,WACE,OAAO,SAAC9G,GACN,IAAMvC,EAAS,CAAC,EAEhB,IAAK,IAAMlL,KAAOyN,EAChBvC,EAAOlL,GAA2ByN,EAAEzN,GAEtC,OAAOkL,CAAA,GAIKuC,EAAA+G,iBAAhB,WACE,IACMvV,EAAQiM,IACRyC,EAAQH,IACR4C,EAASpQ,IACT4N,EACFC,GAAK,SAAC3C,EAASlL,EAAUwN,GAC3B,IAAMK,EAAIuC,EAAOzC,EAAM1O,EAjBlB,SAACwO,GACN,IAAMvC,EAAS,CAAC,EAEhB,IAAK,IAAMlL,KAAOyN,EAChBvC,EAAOlL,GAA2ByN,EAAEzN,GAEtC,OAAOkL,CAAA,CAWsBuC,CAAMvC,MACnC,SAASwF,EAAKjD,GACRA,EAAOG,EAAE6G,OAAQ7G,EAAE6G,KAAOhH,GAC1BA,EAAOG,EAAE8G,OAAQ9G,EAAE8G,KAAOjH,EAAA,CAEhC,SAASkD,EAAKlD,GACRA,EAAOG,EAAE+G,OAAQ/G,EAAE+G,KAAOlH,GAC1BA,EAAOG,EAAEgH,OAAQhH,EAAEgH,KAAOnH,EAAA,CAgBhC,GAdII,EAAE4D,KAAOC,EAAYmD,mBACvBnE,EAAK1Q,GACL2Q,EAAKnD,IAEHK,EAAE4D,KAAOC,EAAYO,eACvBvB,EAAK7C,EAAEoC,GAELpC,EAAE4D,KAAOC,EAAYS,cACvBxB,EAAK9C,EAAEqC,GAELrC,EAAE4D,KAAOC,EAAYQ,UACvBxB,EAAK7C,EAAEoC,GACPU,EAAK9C,EAAEqC,IAELrC,EAAE4D,KAAOC,EAAYE,SAAU,CAEjClB,EAAK7C,EAAEoC,GACPU,EAAK9C,EAAEqC,GAGP,IAFA,IAAAU,EAAA,EAEwBC,EAFJP,EAAWtQ,EAAU6N,EAAEuD,GAAIvD,EAAEyD,GAAIzD,EAAEoC,GAE/BW,EAAAC,EAAA3R,OAAA0R,IAClB,GADKkE,EAAAjE,EAAAD,KACY,EAAIkE,GACvBpE,EAAKD,EAASzQ,EAAU6N,EAAEuD,GAAIvD,EAAEyD,GAAIzD,EAAEoC,EAAG6E,IAK7C,IAFA,IAAA1C,EAAA,EAEwBC,EAFJ/B,EAAW9C,EAAUK,EAAEwD,GAAIxD,EAAE0D,GAAI1D,EAAEqC,GAE/BkC,EAAAC,EAAAnT,OAAAkT,IAClB,GADK0C,EAAAzC,EAAAD,KACY,EAAI0C,GACvBnE,EAAKF,EAASjD,EAAUK,EAAEwD,GAAIxD,EAAE0D,GAAI1D,EAAEqC,EAAG4E,GAAA,CAI/C,GAAIjH,EAAE4D,KAAOC,EAAYyB,IAAK,CAE5BzC,EAAK7C,EAAEoC,GACPU,EAAK9C,EAAEqC,GACPxC,EAAmBG,EAAG7N,EAAUwN,GAwBhC,IArBA,IAAMyC,EAAUpC,EAAEwC,KAAO,IAAM3Q,KAAKiQ,GAE9B2C,EAAK5S,KAAK8P,IAAIS,GAAWpC,EAAEiC,GAC3ByC,EAAK7S,KAAK+P,IAAIQ,GAAWpC,EAAEiC,GAC3B0C,GAAO9S,KAAK+P,IAAIQ,GAAWpC,EAAEkC,GAC7B0C,EAAM/S,KAAK8P,IAAIS,GAAWpC,EAAEkC,GAI5B2C,EAAmB7E,EAAEmD,KAAOnD,EAAEqD,KAClC,CAACrD,EAAEmD,KAAMnD,EAAEqD,OACT,IAAMrD,EAAEqD,KAAO,CAACrD,EAAEqD,KAAO,IAAKrD,EAAEmD,KAAO,KAAO,CAACnD,EAAEqD,KAAMrD,EAAEmD,MAFtD2B,EAAAD,EAAA,GAAQvH,EAAAuH,EAAA,GAGTE,EAAiB,SAACnF,GAAA,IAACvC,EAAAuC,EAAA,GAAIzN,EAAAyN,EAAA,GAErBxO,EAAe,IADNS,KAAKuR,MAAMjR,EAAKkL,GACJxL,KAAKiQ,GAEhC,OAAO1Q,EAAM0T,EAAS1T,EAAM,IAAMA,CAAA,EAAA4T,EAAA,EAKZC,EADJ9C,EAA2BwC,GAAMF,EAAI,GAAGyC,IAAInC,GACxCC,EAAAC,EAAA5T,OAAA2T,KAAbiC,EAAAhC,EAAAD,IACOF,GAAUmC,EAAY3J,GACpCuF,EAAKR,EAAMrC,EAAEiD,GAAIwB,EAAIE,EAAKsC,IAK9B,IADA,IAAAT,EAAA,EACwBW,EADJhF,EAA2ByC,GAAMF,EAAI,GAAGwC,IAAInC,GACxCyB,EAAAW,EAAA9V,OAAAmV,IAAa,CAAhC,IAAMS,GAAAA,EAAAE,EAAAX,IACO1B,GAAUmC,EAAY3J,GACpCwF,EAAKT,EAAMrC,EAAEkD,GAAIwB,EAAIE,EAAKqC,GAAA,EAIhC,OAAO5J,CAAA,IAOT,OAJA0C,EAAE8G,KAAO,IACT9G,EAAE6G,MAAA,IACF7G,EAAEgH,KAAO,IACThH,EAAE+G,MAAA,IACK/G,CAAA,EAjmBX,CAAiBC,IAAAA,EAAA,KCLjB,IAAA6C,EAAAC,EAAA,oBAAAlD,IAAA,CAsEA,OArEEA,EAAA2B,UAAAnP,MAAA,SAAMwN,GACJ,OAAOzM,KAAKiU,UAAUpH,EAAuBkF,MAAMtF,GAAA,EAGrDA,EAAA2B,UAAA8F,MAAA,WACE,OAAOlU,KAAKiU,UAAUpH,EAAuBmF,SAAA,EAG/CvF,EAAA2B,UAAA+F,MAAA,WACE,OAAOnU,KAAKiU,UAAUpH,EAAuBoF,SAAA,EAG/CxF,EAAA2B,UAAAgG,aAAA,SAAa3H,EAAavC,EAAalL,GACrC,OAAOgB,KAAKiU,UAAUpH,EAAuBqF,cAAczF,EAAGvC,EAAGlL,GAAA,EAGnEyN,EAAA2B,UAAAiG,YAAA,WACE,OAAOrU,KAAKiU,UAAUpH,EAAuBuF,eAAA,EAG/C3F,EAAA2B,UAAAkG,MAAA,WACE,OAAOtU,KAAKiU,UAAUpH,EAAuBwF,UAAA,EAG/C5F,EAAA2B,UAAAmG,KAAA,WACE,OAAOvU,KAAKiU,UAAUpH,EAAuBqG,SAAA,EAG/CzG,EAAA2B,UAAAoG,SAAA,SAAS/H,GACP,OAAOzM,KAAKiU,UAAUpH,EAAuB0F,SAAS9F,GAAA,EAGxDA,EAAA2B,UAAAqG,UAAA,SAAUhI,EAAWvC,GACnB,OAAOlK,KAAKiU,UAAUpH,EAAuB8F,UAAUlG,EAAGvC,GAAA,EAG5DuC,EAAA2B,UAAAsG,MAAA,SAAMjI,EAAWvC,GACf,OAAOlK,KAAKiU,UAAUpH,EAAuB+F,MAAMnG,EAAGvC,GAAA,EAGxDuC,EAAA2B,UAAAuG,OAAA,SAAOlI,EAAWvC,EAAYlL,GAC5B,OAAOgB,KAAKiU,UAAUpH,EAAuB6F,OAAOjG,EAAGvC,EAAGlL,GAAA,EAG5DyN,EAAA2B,UAAAwG,OAAA,SAAOnI,EAAWvC,EAAWlL,EAAWf,EAAW0O,EAAWH,GAC5D,OAAOxM,KAAKiU,UAAUpH,EAAuB4F,OAAOhG,EAAGvC,EAAGlL,EAAGf,EAAG0O,EAAGH,GAAA,EAGrEC,EAAA2B,UAAAyG,MAAA,SAAMpI,GACJ,OAAOzM,KAAKiU,UAAUpH,EAAuBgG,OAAOpG,GAAA,EAGtDA,EAAA2B,UAAA0G,MAAA,SAAMrI,GACJ,OAAOzM,KAAKiU,UAAUpH,EAAuBkG,OAAOtG,GAAA,EAGtDA,EAAA2B,UAAA2G,UAAA,SAAUtI,GACR,OAAOzM,KAAKiU,UAAUpH,EAAuBmG,gBAAgBvG,GAAA,EAG/DA,EAAA2B,UAAA4G,UAAA,SAAUvI,GACR,OAAOzM,KAAKiU,UAAUpH,EAAuBoG,gBAAgBxG,GAAA,EAG/DA,EAAA2B,UAAA6G,aAAA,WACE,OAAOjV,KAAKiU,UAAUpH,EAAuByG,gBAAA,EAAA7G,CAAA,CAlEjD,GCGMmD,EAAe,SAACnD,GACpB,YAAQA,GAAK,OAASA,GAAK,OAASA,GAAK,OAASA,CAAA,EAC9CoD,EAAU,SAACpD,GACf,UAAIyI,WAAW,IAAMzI,EAAEyI,WAAW,IAAMzI,EAAEyI,WAAW,IAAM,IAAIA,WAAW,IAAA9D,EAAA,SAAA3E,GAa1E,SAAAzN,IAAA,IAAAkL,EACEuC,EAAAlN,KAAA,mBAVM2K,EAAAiL,UAAoB,GACpBjL,EAAAkL,gBAA2C,EAC3ClL,EAAAmL,oBAAA,EACAnL,EAAAoL,wBAAA,EACApL,EAAAqL,iBAAA,EACArL,EAAAsL,uBAAA,EACAtL,EAAAuL,qBAAA,EACAvL,EAAAwL,QAAoB,GAAAxL,CAAA,CA6Q9B,OArRuCA,EAAAlL,EAAAyN,GAcrCzN,EAAAoP,UAAAuH,OAAA,SAAOlJ,GAGL,QAAI,IAAJA,IAHKA,EAAA,IACLzM,KAAK4V,MAAM,IAAKnJ,GAEZ,IAAMzM,KAAK0V,QAAQxX,SAAW8B,KAAKsV,uBACrC,MAAM,IAAIO,YAAY,yCAExB,OAAOpJ,CAAA,EAGTzN,EAAAoP,UAAAwH,MAAA,SAAMnJ,EAAavC,GAAnB,IAAAlL,EAAA,cAAAkL,IAAmBA,EAAA,IAOjB,IANA,IAAMjM,EAAgB,SAACwO,GACrBvC,EAAShL,KAAKuN,GACdzN,EAAK0W,QAAQxX,OAAS,EACtBc,EAAKsW,wBAAA,CAAyB,EAGvB3I,EAAI,EAAGA,EAAIF,EAAIvO,OAAQyO,IAAK,CACnC,IAAMH,EAAIC,EAAIE,GAERD,IAAa1M,KAAKoV,iBAAmB1E,EAAYyB,KAC5B,IAAxBnS,KAAK0V,QAAQxX,QAAwC,IAAxB8B,KAAK0V,QAAQxX,QACjB,IAA1B8B,KAAKmV,UAAUjX,QACK,MAAnB8B,KAAKmV,WAAwC,MAAnBnV,KAAKmV,WAC5BnG,EAAgBa,EAAQrD,KACR,MAAnBxM,KAAKmV,WAA2B,MAAN3I,GAC3BE,GAGF,IACEmD,EAAQrD,IACPwC,EAMH,GAAI,MAAQxC,GAAK,MAAQA,EAKzB,GACG,MAAQA,GAAK,MAAQA,IACtBxM,KAAKuV,iBACJvV,KAAKwV,sBAMR,GAAI,MAAQhJ,GAAMxM,KAAKuV,iBAAoBvV,KAAKyV,qBAAwB/I,EAAxE,CAOA,GAAI1M,KAAKmV,YAAc,IAAMnV,KAAKoV,eAAgB,CAChD,IAAMvI,EAAMc,OAAO3N,KAAKmV,WACxB,GAAI9K,MAAMwC,GACR,MAAM,IAAIgJ,YAAY,4BAA4BlJ,GAEpD,GAAI3M,KAAKoV,iBAAmB1E,EAAYyB,IACtC,GAAI,IAAMnS,KAAK0V,QAAQxX,QAAU,IAAM8B,KAAK0V,QAAQxX,QAClD,GAAI,EAAI2O,EACN,MAAM,IAAIgJ,YACR,kCAAkChJ,EAAA,eAAkBF,EAAA,UAGnD,IAAI,IAAM3M,KAAK0V,QAAQxX,QAAU,IAAM8B,KAAK0V,QAAQxX,SACrD,MAAQ8B,KAAKmV,WAAa,MAAQnV,KAAKmV,UACzC,MAAM,IAAIU,YACR,yBAAyB7V,KAAKmV,UAAA,eAAwBxI,EAAA,KAK9D3M,KAAK0V,QAAQxW,KAAK2N,GACd7M,KAAK0V,QAAQxX,SAAWmT,EAAmBrR,KAAKoV,kBAC9C1E,EAAYO,gBAAkBjR,KAAKoV,eACrCnX,EAAc,CACZwS,KAAMC,EAAYO,cAClBd,SAAUnQ,KAAKqV,mBACfpG,EAAGpC,IAEI6D,EAAYS,eAAiBnR,KAAKoV,eAC3CnX,EAAc,CACZwS,KAAMC,EAAYS,aAClBhB,SAAUnQ,KAAKqV,mBACfnG,EAAGrC,IAIL7M,KAAKoV,iBAAmB1E,EAAYK,SACpC/Q,KAAKoV,iBAAmB1E,EAAYQ,SACpClR,KAAKoV,iBAAmB1E,EAAYG,gBAEpC5S,EAAc,CACZwS,KAAMzQ,KAAKoV,eACXjF,SAAUnQ,KAAKqV,mBACfpG,EAAGjP,KAAK0V,QAAQ,GAChBxG,EAAGlP,KAAK0V,QAAQ,KAGdhF,EAAYK,UAAY/Q,KAAKoV,iBAC/BpV,KAAKoV,eAAiB1E,EAAYQ,UAE3BlR,KAAKoV,iBAAmB1E,EAAYE,SAC7C3S,EAAc,CACZwS,KAAMC,EAAYE,SAClBT,SAAUnQ,KAAKqV,mBACfjF,GAAIpQ,KAAK0V,QAAQ,GACjBrF,GAAIrQ,KAAK0V,QAAQ,GACjBpF,GAAItQ,KAAK0V,QAAQ,GACjBnF,GAAIvQ,KAAK0V,QAAQ,GACjBzG,EAAGjP,KAAK0V,QAAQ,GAChBxG,EAAGlP,KAAK0V,QAAQ,KAET1V,KAAKoV,iBAAmB1E,EAAYC,gBAC7C1S,EAAc,CACZwS,KAAMC,EAAYC,gBAClBR,SAAUnQ,KAAKqV,mBACf/E,GAAItQ,KAAK0V,QAAQ,GACjBnF,GAAIvQ,KAAK0V,QAAQ,GACjBzG,EAAGjP,KAAK0V,QAAQ,GAChBxG,EAAGlP,KAAK0V,QAAQ,KAET1V,KAAKoV,iBAAmB1E,EAAYI,QAC7C7S,EAAc,CACZwS,KAAMC,EAAYI,QAClBX,SAAUnQ,KAAKqV,mBACfjF,GAAIpQ,KAAK0V,QAAQ,GACjBrF,GAAIrQ,KAAK0V,QAAQ,GACjBzG,EAAGjP,KAAK0V,QAAQ,GAChBxG,EAAGlP,KAAK0V,QAAQ,KAET1V,KAAKoV,iBAAmB1E,EAAYyB,KAC7ClU,EAAc,CACZwS,KAAMC,EAAYyB,IAClBhC,SAAUnQ,KAAKqV,mBACfvG,GAAI9O,KAAK0V,QAAQ,GACjB3G,GAAI/O,KAAK0V,QAAQ,GACjBrG,KAAMrP,KAAK0V,QAAQ,GACnB9G,SAAU5O,KAAK0V,QAAQ,GACvB7G,UAAW7O,KAAK0V,QAAQ,GACxBzG,EAAGjP,KAAK0V,QAAQ,GAChBxG,EAAGlP,KAAK0V,QAAQ,MAItB1V,KAAKmV,UAAY,GACjBnV,KAAKwV,uBAAA,EACLxV,KAAKuV,iBAAA,EACLvV,KAAKyV,qBAAA,EACLzV,KAAKsV,wBAAA,CAAyB,CAGhC,IAAI1F,EAAapD,GAGjB,GAAI,MAAQA,GAAKxM,KAAKsV,uBAEpBtV,KAAKsV,wBAAA,OAIP,GAAI,MAAQ9I,GAAK,MAAQA,GAAK,MAAQA,EAMtC,GAAIwC,EACFhP,KAAKmV,UAAY3I,EACjBxM,KAAKyV,qBAAA,MAFP,CAOA,GAAI,IAAMzV,KAAK0V,QAAQxX,OACrB,MAAM,IAAI2X,YAAY,iCAAiClJ,EAAA,KAEzD,IAAK3M,KAAKsV,uBACR,MAAM,IAAIO,YACR,yBAAyBrJ,EAAA,cAAeG,EAAA,iCAK5C,GAFA3M,KAAKsV,wBAAA,EAED,MAAQ9I,GAAK,MAAQA,EAQlB,GAAI,MAAQA,GAAK,MAAQA,EAC9BxM,KAAKoV,eAAiB1E,EAAYO,cAClCjR,KAAKqV,mBAAqB,MAAQ7I,OAE7B,GAAI,MAAQA,GAAK,MAAQA,EAC9BxM,KAAKoV,eAAiB1E,EAAYS,aAClCnR,KAAKqV,mBAAqB,MAAQ7I,OAE7B,GAAI,MAAQA,GAAK,MAAQA,EAC9BxM,KAAKoV,eAAiB1E,EAAYK,QAClC/Q,KAAKqV,mBAAqB,MAAQ7I,OAE7B,GAAI,MAAQA,GAAK,MAAQA,EAC9BxM,KAAKoV,eAAiB1E,EAAYQ,QAClClR,KAAKqV,mBAAqB,MAAQ7I,OAE7B,GAAI,MAAQA,GAAK,MAAQA,EAC9BxM,KAAKoV,eAAiB1E,EAAYE,SAClC5Q,KAAKqV,mBAAqB,MAAQ7I,OAE7B,GAAI,MAAQA,GAAK,MAAQA,EAC9BxM,KAAKoV,eAAiB1E,EAAYC,gBAClC3Q,KAAKqV,mBAAqB,MAAQ7I,OAE7B,GAAI,MAAQA,GAAK,MAAQA,EAC9BxM,KAAKoV,eAAiB1E,EAAYI,QAClC9Q,KAAKqV,mBAAqB,MAAQ7I,OAE7B,GAAI,MAAQA,GAAK,MAAQA,EAC9BxM,KAAKoV,eAAiB1E,EAAYG,eAClC7Q,KAAKqV,mBAAqB,MAAQ7I,MAE7B,IAAI,MAAQA,GAAK,MAAQA,EAI9B,MAAM,IAAIqJ,YAAY,yBAAyBrJ,EAAA,cAAeG,EAAA,KAH9D3M,KAAKoV,eAAiB1E,EAAYyB,IAClCnS,KAAKqV,mBAAqB,MAAQ7I,CAAA,MAzClCtC,EAAShL,KAAK,CACZuR,KAAMC,EAAYM,aAEpBhR,KAAKsV,wBAAA,EACLtV,KAAKoV,gBAAkB,OA3BvBpV,KAAKmV,UAAY3I,EACjBxM,KAAKyV,oBAAsB,MAAQjJ,CAAA,MArHnCxM,KAAKmV,WAAa3I,EAClBxM,KAAKyV,qBAAA,OANLzV,KAAKmV,WAAa3I,OATlBxM,KAAKmV,WAAa3I,EAClBxM,KAAKuV,iBAAA,OANLvV,KAAKmV,WAAa3I,EAClBxM,KAAKwV,sBAAwBxV,KAAKuV,eAAA,CA2MtC,OAAOrL,CAAA,EAKTlL,EAAAoP,UAAA6F,UAAA,SAAUxH,GAoBR,OAnBeoB,OAAOU,OAAOvO,KAAM,CACjC4V,MAAO,CACL9I,MAAA,SAAM5C,EAAelL,QAAA,IAAAA,IAAAA,EAAA,IAKnB,IAJA,IAAAf,EAAA,EAIgB0O,EAJOkB,OAAOiI,eAAe9V,MAAM4V,MAAMrW,KACvDS,KACAkK,GAEcjM,EAAA0O,EAAAzO,OAAAD,IAAgB,CAA3B,IAAMuO,EAAAG,EAAA1O,GACHyO,EAAKD,EAAUD,GACjB5B,MAAMmL,QAAQrJ,GAChB1N,EAASE,KAAAO,MAATT,EAAiB0N,GAEjB1N,EAASE,KAAKwN,EAAA,CAGlB,OAAO1N,CAAA,MAAAA,CAAA,CAlR2D,CAGrC2Q,GAAAe,EAAA,SAAAjE,GCJrC,SAAAxO,EAAYiM,GAAZ,IAAAlL,EACEyN,EAAAlN,KAAA,mBAEEP,EAAKgX,SADH,iBAAoB9L,EACNjM,EAAY2X,MAAM1L,GAElBA,EAAAlL,CAAA,CA2DtB,OAlEiCkL,EAAAjM,EAAAwO,GAW/BxO,EAAAmQ,UAAA6H,OAAA,WACE,OAAOhY,EAAYgY,OAAOjW,KAAKgW,SAAA,EAGjC/X,EAAAmQ,UAAA8H,UAAA,WACE,IAAMzJ,EAAkBI,EAAuB2G,mBAG/C,OADAxT,KAAKiU,UAAUxH,GACRA,CAAA,EAGTxO,EAAAmQ,UAAA6F,UAAA,SACExH,GAIA,IAFA,IAAMvC,EAAc,GAAAlL,EAAA,EAEEf,EAAA+B,KAAKgW,SAALhX,EAAAf,EAAAC,OAAAc,IAAe,CAAhC,IACG2N,EAAqBF,EAAAxO,EAAAe,IAEvB4L,MAAMmL,QAAQpJ,GAChBzC,EAAYhL,KAAAO,MAAZyK,EAAoByC,GAEpBzC,EAAYhL,KAAKyN,EAAA,CAIrB,OADA3M,KAAKgW,SAAW9L,EACTlK,IAAA,EAGF/B,EAAAgY,OAAP,SAAcxJ,GACZ,ONnB+E,SCnBrDA,GAC5B,IAAIvC,EAAM,GAELU,MAAMmL,QAAQtJ,KACjBA,EAAW,CAACA,IAEd,IAAK,IAAIzN,EAAI,EAAGA,EAAIyN,EAASvO,OAAQc,IAAK,CACxC,IAAMf,EAAUwO,EAASzN,GACzB,GAAIf,EAAQwS,OAASC,EAAYM,WAC/B9G,GAAO,SACF,GAAIjM,EAAQwS,OAASC,EAAYO,cACtC/G,IAAQjM,EAAQkS,SAAW,IAAM,KAC/BlS,EAAQgR,OACL,GAAIhR,EAAQwS,OAASC,EAAYS,aACtCjH,IAAQjM,EAAQkS,SAAW,IAAM,KAC/BlS,EAAQiR,OACL,GAAIjR,EAAQwS,OAASC,EAAYK,QACtC7G,IAAQjM,EAAQkS,SAAW,IAAM,KAC/BlS,EAAQgR,EApBJ,IAoBchR,EAAQiR,OACvB,GAAIjR,EAAQwS,OAASC,EAAYQ,QACtChH,IAAQjM,EAAQkS,SAAW,IAAM,KAC/BlS,EAAQgR,EAvBJ,IAuBchR,EAAQiR,OACvB,GAAIjR,EAAQwS,OAASC,EAAYE,SACtC1G,IAAQjM,EAAQkS,SAAW,IAAM,KAC/BlS,EAAQmS,GA1BJ,IA0BenS,EAAQoS,GA1BvB,IA2BEpS,EAAQqS,GA3BV,IA2BqBrS,EAAQsS,GA3B7B,IA4BEtS,EAAQgR,EA5BV,IA4BoBhR,EAAQiR,OAC7B,GAAIjR,EAAQwS,OAASC,EAAYC,gBACtCzG,IAAQjM,EAAQkS,SAAW,IAAM,KAC/BlS,EAAQqS,GA/BJ,IA+BerS,EAAQsS,GA/BvB,IAgCEtS,EAAQgR,EAhCV,IAgCoBhR,EAAQiR,OAC7B,GAAIjR,EAAQwS,OAASC,EAAYI,QACtC5G,IAAQjM,EAAQkS,SAAW,IAAM,KAC/BlS,EAAQmS,GAnCJ,IAmCenS,EAAQoS,GAnCvB,IAoCEpS,EAAQgR,EApCV,IAoCoBhR,EAAQiR,OAC7B,GAAIjR,EAAQwS,OAASC,EAAYG,eACtC3G,IAAQjM,EAAQkS,SAAW,IAAM,KAC/BlS,EAAQgR,EAvCJ,IAuCchR,EAAQiR,MACvB,IAAIjR,EAAQwS,OAASC,EAAYyB,IAQtC,MAAM,IAAIzD,MACR,4BAA8BzQ,EAAgBwS,KAAA,cAAkBzR,EAAA,KARlEkL,IAAQjM,EAAQkS,SAAW,IAAM,KAC/BlS,EAAQ6Q,GA1CJ,IA0Ce7Q,EAAQ8Q,GA1CvB,IA2CE9Q,EAAQoR,KA3CV,MA4CIpR,EAAQ2Q,SA5CZ,MA4CgC3Q,EAAQ4Q,UA5CxC,IA6CE5Q,EAAQgR,EA7CV,IA6CoBhR,EAAQiR,CAAA,EAQtC,OAAOhF,CAAA,CKbElL,CAAcyN,EAAA,EAGhBxO,EAAA2X,MAAP,SAAanJ,GACX,IAAMvC,EAAS,IAAIkH,EACbpS,EAAyB,GAG/B,OAFAkL,EAAO0L,MAAMnJ,EAAMzN,GACnBkL,EAAOyL,OAAO3W,GACPA,CAAA,EAGOf,EAAA+S,WAAgB,EAChB/S,EAAA8S,QAAa,EACb9S,EAAAgT,cAAmB,EACnBhT,EAAAkT,aAAkB,EAClBlT,EAAAiT,QAAc,GACdjT,EAAA2S,SAAe,GACf3S,EAAA0S,gBAAsB,GACtB1S,EAAA6S,QAAe,IACf7S,EAAA4S,eAAsB,IACtB5S,EAAAkU,IAAW,IACXlU,EAAAuU,cAAgBvU,EAAYiT,QAAUjT,EAAYgT,cAAgBhT,EAAYkT,aAC9ElT,EAAA4V,iBAAmB5V,EAAYgT,cAAgBhT,EAAYkT,aAAelT,EAAYiT,QACtGjT,EAAY2S,SAAW3S,EAAY0S,gBAAkB1S,EAAY6S,QACjE7S,EAAY4S,eAAiB5S,EAAYkU,IAAAlU,CAAA,CD3DJ,CCNN0R,GAoEpB0B,IAAA3B,EAAA,IACRgB,EAAYK,SAAU,EACvBrB,EAACgB,EAAYQ,SAAU,EACvBxB,EAACgB,EAAYO,eAAgB,EAC7BvB,EAACgB,EAAYS,cAAe,EAC5BzB,EAACgB,EAAYM,YAAa,EAC1BtB,EAACgB,EAAYI,SAAU,EACvBpB,EAACgB,EAAYG,gBAAiB,EAC9BnB,EAACgB,EAAYE,UAAW,EACxBlB,EAACgB,EAAYC,iBAAkB,EAC/BjB,EAACgB,EAAYyB,KAAM,EAAAzC,GCpFvB,SAASyG,EAAQC,GAaf,OATED,EADoB,oBAAX5I,QAAoD,kBAApBA,OAAO8I,SACtC,SAAUD,GAClB,cAAcA,CAChB,EAEU,SAAUA,GAClB,OAAOA,GAAyB,oBAAX7I,QAAyB6I,EAAI9H,cAAgBf,QAAU6I,IAAQ7I,OAAOa,UAAY,gBAAkBgI,CAC3H,EAGKD,EAAQC,EACjB,CAoDA,IAAIE,EAAW,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAClwCC,EAAW,CAAC,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAqEvgC,SAASC,EAAuBC,EAAQC,EAAMC,EAAMC,EAAOC,GAKzD,GAJsB,kBAAXJ,IACTA,EAASzL,SAAS8L,eAAeL,KAG9BA,GAA8B,WAApBN,EAAQM,MAA0B,eAAgBA,GAC/D,MAAM,IAAIhJ,UAAU,2EAGtB,IAAIsJ,EAAUN,EAAOO,WAAW,MAEhC,IACE,OAAOD,EAAQE,aAAaP,EAAMC,EAAMC,EAAOC,EACjD,CAAE,MAAO7X,GACP,MAAM,IAAI0P,MAAM,gCAAkC1P,EACpD,CACF,CAYA,SAASkY,EAAkBT,EAAQC,EAAMC,EAAMC,EAAOC,EAAQM,GAC5D,KAAI9M,MAAM8M,IAAWA,EAAS,GAA9B,CAIAA,GAAU,EACV,IAAIC,EAAYZ,EAAuBC,EAAQC,EAAMC,EAAMC,EAAOC,GAClEO,EAcF,SAA8BA,EAAWV,EAAMC,EAAMC,EAAOC,EAAQM,GAYlE,IAXA,IASIE,EATAC,EAASF,EAAUG,KACnBC,EAAM,EAAIL,EAAS,EAEnBM,EAAcb,EAAQ,EACtBc,EAAeb,EAAS,EACxBc,EAAcR,EAAS,EACvBS,EAAYD,GAAeA,EAAc,GAAK,EAC9CE,EAAa,IAAIC,EACjBC,EAAQF,EAGH5Z,EAAI,EAAGA,EAAIuZ,EAAKvZ,IACvB8Z,EAAQA,EAAMtZ,KAAO,IAAIqZ,EAErB7Z,IAAM0Z,IACRN,EAAWU,GAIfA,EAAMtZ,KAAOoZ,EAQb,IAPA,IAAIG,EAAU,KACVC,EAAW,KACXC,EAAK,EACLC,EAAK,EACLC,EAAS9B,EAASa,GAClBkB,EAAS9B,EAASY,GAEbjI,EAAI,EAAGA,EAAI2H,EAAQ3H,IAAK,CAC/B6I,EAAQF,EAMR,IALA,IAAIS,EAAKhB,EAAOa,GACZI,EAAKjB,EAAOa,EAAK,GACjBK,EAAKlB,EAAOa,EAAK,GACjBM,EAAKnB,EAAOa,EAAK,GAEZO,EAAK,EAAGA,EAAKf,EAAae,IACjCX,EAAM7N,EAAIoO,EACVP,EAAM5N,EAAIoO,EACVR,EAAM3N,EAAIoO,EACVT,EAAMpL,EAAI8L,EACVV,EAAQA,EAAMtZ,KAgBhB,IAbA,IAAIka,EAAS,EACTC,EAAS,EACTC,EAAS,EACTC,EAAS,EACTC,EAAUpB,EAAcW,EACxBU,EAAUrB,EAAcY,EACxBU,EAAUtB,EAAca,EACxBU,EAAUvB,EAAcc,EACxBU,EAAOvB,EAAYU,EACnBc,EAAOxB,EAAYW,EACnBc,EAAOzB,EAAYY,EACnBc,EAAO1B,EAAYa,EAEdc,EAAM,EAAGA,EAAM5B,EAAa4B,IAAO,CAC1C,IAAIjK,EAAI6I,IAAOV,EAAc8B,EAAM9B,EAAc8B,IAAQ,GACrDrP,EAAIoN,EAAOhI,GACXnF,EAAImN,EAAOhI,EAAI,GACflF,EAAIkN,EAAOhI,EAAI,GACf3C,EAAI2K,EAAOhI,EAAI,GACfkK,EAAM7B,EAAc4B,EACxBJ,IAASpB,EAAM7N,EAAIA,GAAKsP,EACxBJ,IAASrB,EAAM5N,EAAIA,GAAKqP,EACxBH,IAAStB,EAAM3N,EAAIA,GAAKoP,EACxBF,IAASvB,EAAMpL,EAAIA,GAAK6M,EACxBb,GAAUzO,EACV0O,GAAUzO,EACV0O,GAAUzO,EACV0O,GAAUnM,EACVoL,EAAQA,EAAMtZ,IAChB,CAEAuZ,EAAUH,EACVI,EAAWZ,EAEX,IAAK,IAAIpI,EAAI,EAAGA,EAAI2H,EAAO3H,IAAK,CAC9B,IAAIwK,EAAYH,EAAOlB,IAAWC,EAGlC,GAFAf,EAAOa,EAAK,GAAKsB,EAEC,IAAdA,EAAiB,CACnB,IAAIC,EAAM,IAAMD,EAEhBnC,EAAOa,IAAOgB,EAAOf,IAAWC,GAAUqB,EAC1CpC,EAAOa,EAAK,IAAMiB,EAAOhB,IAAWC,GAAUqB,EAC9CpC,EAAOa,EAAK,IAAMkB,EAAOjB,IAAWC,GAAUqB,CAChD,MACEpC,EAAOa,GAAMb,EAAOa,EAAK,GAAKb,EAAOa,EAAK,GAAK,EAGjDgB,GAAQJ,EACRK,GAAQJ,EACRK,GAAQJ,EACRK,GAAQJ,EACRH,GAAWf,EAAQ9N,EACnB8O,GAAWhB,EAAQ7N,EACnB8O,GAAWjB,EAAQ5N,EACnB8O,GAAWlB,EAAQrL,EAEnB,IAAIgN,EAAK1K,EAAIkI,EAAS,EAEtBwC,EAAKzB,GAAMyB,EAAKlC,EAAckC,EAAKlC,IAAgB,EAKnD0B,GAJAR,GAAUX,EAAQ9N,EAAIoN,EAAOqC,GAK7BP,GAJAR,GAAUZ,EAAQ7N,EAAImN,EAAOqC,EAAK,GAKlCN,GAJAR,GAAUb,EAAQ5N,EAAIkN,EAAOqC,EAAK,GAKlCL,GAJAR,GAAUd,EAAQrL,EAAI2K,EAAOqC,EAAK,GAKlC3B,EAAUA,EAAQvZ,KAClB,IAAImb,GAAY3B,EACZ4B,GAAKD,GAAU1P,EACf4P,GAAKF,GAAUzP,EACf4P,GAAKH,GAAUxP,EACf4P,GAAKJ,GAAUjN,EACnBoM,GAAWc,GACXb,GAAWc,GACXb,GAAWc,GACXb,GAAWc,GACXrB,GAAUkB,GACVjB,GAAUkB,GACVjB,GAAUkB,GACVjB,GAAUkB,GACV/B,EAAWA,EAASxZ,KACpB0Z,GAAM,CACR,CAEAD,GAAMtB,CACR,CAEA,IAAK,IAAIqD,GAAK,EAAGA,GAAKrD,EAAOqD,KAAM,CAGjC,IAAIC,GAAM5C,EAFVa,EAAK8B,IAAM,GAGPE,GAAM7C,EAAOa,EAAK,GAClBiC,GAAM9C,EAAOa,EAAK,GAClBkC,GAAM/C,EAAOa,EAAK,GAClBmC,GAAW3C,EAAcuC,GACzBK,GAAW5C,EAAcwC,GACzBK,GAAW7C,EAAcyC,GACzBK,GAAW9C,EAAc0C,GACzBK,GAAQ9C,EAAYsC,GACpBS,GAAQ/C,EAAYuC,GACpBS,GAAQhD,EAAYwC,GACpBS,GAAQjD,EAAYyC,GAExBtC,EAAQF,EAER,IAAK,IAAIiD,GAAM,EAAGA,GAAMnD,EAAamD,KACnC/C,EAAM7N,EAAIgQ,GACVnC,EAAM5N,EAAIgQ,GACVpC,EAAM3N,EAAIgQ,GACVrC,EAAMpL,EAAI0N,GACVtC,EAAQA,EAAMtZ,KAShB,IANA,IAAIsc,GAAKnE,EACLoE,GAAU,EACVC,GAAU,EACVC,GAAU,EACVC,GAAU,EAELC,GAAM,EAAGA,IAAOjE,EAAQiE,KAAO,CACtCjD,EAAK4C,GAAKd,IAAM,EAEhB,IAAIoB,GAAO1D,EAAcyD,GAEzBV,KAAU3C,EAAM7N,EAAIgQ,GAAM5C,EAAOa,IAAOkD,GACxCV,KAAU5C,EAAM5N,EAAIgQ,GAAM7C,EAAOa,EAAK,IAAMkD,GAC5CT,KAAU7C,EAAM3N,EAAIgQ,GAAM9C,EAAOa,EAAK,IAAMkD,GAC5CR,KAAU9C,EAAMpL,EAAI0N,GAAM/C,EAAOa,EAAK,IAAMkD,GAC5CF,IAAWjB,GACXc,IAAWb,GACXc,IAAWb,GACXc,IAAWb,GACXtC,EAAQA,EAAMtZ,KAEV2c,GAAM1D,IACRqD,IAAMnE,EAEV,CAEAuB,EAAK8B,GACLjC,EAAUH,EACVI,EAAWZ,EAEX,IAAK,IAAIiE,GAAK,EAAGA,GAAKzE,EAAQyE,KAAM,CAClC,IAAIC,GAAMpD,GAAM,EAEhBb,EAAOiE,GAAM,GAAKlB,GAAMQ,GAAQzC,IAAWC,EAEvCgC,GAAM,GACRA,GAAM,IAAMA,GACZ/C,EAAOiE,KAAQb,GAAQtC,IAAWC,GAAUgC,GAC5C/C,EAAOiE,GAAM,IAAMZ,GAAQvC,IAAWC,GAAUgC,GAChD/C,EAAOiE,GAAM,IAAMX,GAAQxC,IAAWC,GAAUgC,IAEhD/C,EAAOiE,IAAOjE,EAAOiE,GAAM,GAAKjE,EAAOiE,GAAM,GAAK,EAGpDb,IAASJ,GACTK,IAASJ,GACTK,IAASJ,GACTK,IAASJ,GACTH,IAAYtC,EAAQ9N,EACpBqQ,IAAYvC,EAAQ7N,EACpBqQ,IAAYxC,EAAQ5N,EACpBqQ,IAAYzC,EAAQrL,EACpB4O,GAAMtB,KAAOsB,GAAMD,GAAK3D,GAAeD,EAAe6D,GAAM7D,GAAgBd,GAAS,EACrF8D,IAASS,IAAWnD,EAAQ9N,EAAIoN,EAAOiE,IACvCZ,IAASK,IAAWhD,EAAQ7N,EAAImN,EAAOiE,GAAM,GAC7CX,IAASK,IAAWjD,EAAQ5N,EAAIkN,EAAOiE,GAAM,GAC7CV,IAASK,IAAWlD,EAAQrL,EAAI2K,EAAOiE,GAAM,GAC7CvD,EAAUA,EAAQvZ,KAClB6b,IAAYJ,GAAMjC,EAAS/N,EAC3BqQ,IAAYJ,GAAMlC,EAAS9N,EAC3BqQ,IAAYJ,GAAMnC,EAAS7N,EAC3BqQ,IAAYJ,GAAMpC,EAAStL,EAC3BwO,IAAWjB,GACXc,IAAWb,GACXc,IAAWb,GACXc,IAAWb,GACXpC,EAAWA,EAASxZ,KACpB0Z,GAAMvB,CACR,CACF,CAEA,OAAOQ,CACT,CApPcoE,CAAqBpE,EAAWV,EAAMC,EAAMC,EAAOC,EAAQM,GACvEV,EAAOO,WAAW,MAAMyE,aAAarE,EAAWV,EAAMC,EALtD,CAMF,CAmcA,IAAImB,EAIJ,SAASA,KApmBT,SAAyB4D,EAAUC,GACjC,KAAMD,aAAoBC,GACxB,MAAM,IAAIlO,UAAU,oCAExB,CAimBEmO,CAAgB5b,KAAM8X,GAEtB9X,KAAKkK,EAAI,EACTlK,KAAKmK,EAAI,EACTnK,KAAKoK,EAAI,EACTpK,KAAK2M,EAAI,EACT3M,KAAKvB,KAAO,IACd,E", "sources": ["../node_modules/raf/index.js", "../node_modules/rgbcolor/index.js", "../node_modules/performance-now/src/performance-now.coffee", "../node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js", "../node_modules/@babel/runtime/helpers/esm/toPropertyKey.js", "../node_modules/@babel/runtime/helpers/esm/toPrimitive.js", "../node_modules/@babel/runtime/helpers/esm/defineProperty.js", "../node_modules/svg-pathdata/node_modules/tslib/tslib.es6.js", "../node_modules/svg-pathdata/src/SVGPathDataEncoder.ts", "../node_modules/svg-pathdata/src/mathUtils.ts", "../node_modules/svg-pathdata/src/SVGPathDataTransformer.ts", "../node_modules/svg-pathdata/src/TransformableSVG.ts", "../node_modules/svg-pathdata/src/SVGPathDataParser.ts", "../node_modules/svg-pathdata/src/SVGPathData.ts", "../node_modules/stackblur-canvas/dist/stackblur-es.js"], "sourcesContent": ["var now = require('performance-now')\n  , root = typeof window === 'undefined' ? global : window\n  , vendors = ['moz', 'webkit']\n  , suffix = 'AnimationFrame'\n  , raf = root['request' + suffix]\n  , caf = root['cancel' + suffix] || root['cancelRequest' + suffix]\n\nfor(var i = 0; !raf && i < vendors.length; i++) {\n  raf = root[vendors[i] + 'Request' + suffix]\n  caf = root[vendors[i] + 'Cancel' + suffix]\n      || root[vendors[i] + 'CancelRequest' + suffix]\n}\n\n// Some versions of FF have rAF but not cAF\nif(!raf || !caf) {\n  var last = 0\n    , id = 0\n    , queue = []\n    , frameDuration = 1000 / 60\n\n  raf = function(callback) {\n    if(queue.length === 0) {\n      var _now = now()\n        , next = Math.max(0, frameDuration - (_now - last))\n      last = next + _now\n      setTimeout(function() {\n        var cp = queue.slice(0)\n        // Clear queue here to prevent\n        // callbacks from appending listeners\n        // to the current frame's queue\n        queue.length = 0\n        for(var i = 0; i < cp.length; i++) {\n          if(!cp[i].cancelled) {\n            try{\n              cp[i].callback(last)\n            } catch(e) {\n              setTimeout(function() { throw e }, 0)\n            }\n          }\n        }\n      }, Math.round(next))\n    }\n    queue.push({\n      handle: ++id,\n      callback: callback,\n      cancelled: false\n    })\n    return id\n  }\n\n  caf = function(handle) {\n    for(var i = 0; i < queue.length; i++) {\n      if(queue[i].handle === handle) {\n        queue[i].cancelled = true\n      }\n    }\n  }\n}\n\nmodule.exports = function(fn) {\n  // Wrap in a new function to prevent\n  // `cancel` potentially being assigned\n  // to the native rAF function\n  return raf.call(root, fn)\n}\nmodule.exports.cancel = function() {\n  caf.apply(root, arguments)\n}\nmodule.exports.polyfill = function(object) {\n  if (!object) {\n    object = root;\n  }\n  object.requestAnimationFrame = raf\n  object.cancelAnimationFrame = caf\n}\n", "/*\n\tBased on rgbcolor.js by <PERSON><PERSON><PERSON> <<EMAIL>>\n\thttp://www.phpied.com/rgb-color-parser-in-javascript/\n*/\n\nmodule.exports = function(color_string) {\n    this.ok = false;\n    this.alpha = 1.0;\n\n    // strip any leading #\n    if (color_string.charAt(0) == '#') { // remove # if any\n        color_string = color_string.substr(1,6);\n    }\n\n    color_string = color_string.replace(/ /g,'');\n    color_string = color_string.toLowerCase();\n\n    // before getting into regexps, try simple matches\n    // and overwrite the input\n    var simple_colors = {\n        aliceblue: 'f0f8ff',\n        antiquewhite: 'faebd7',\n        aqua: '00ffff',\n        aquamarine: '7fffd4',\n        azure: 'f0ffff',\n        beige: 'f5f5dc',\n        bisque: 'ffe4c4',\n        black: '000000',\n        blanchedalmond: 'ffebcd',\n        blue: '0000ff',\n        blueviolet: '8a2be2',\n        brown: 'a52a2a',\n        burlywood: 'deb887',\n        cadetblue: '5f9ea0',\n        chartreuse: '7fff00',\n        chocolate: 'd2691e',\n        coral: 'ff7f50',\n        cornflowerblue: '6495ed',\n        cornsilk: 'fff8dc',\n        crimson: 'dc143c',\n        cyan: '00ffff',\n        darkblue: '00008b',\n        darkcyan: '008b8b',\n        darkgoldenrod: 'b8860b',\n        darkgray: 'a9a9a9',\n        darkgreen: '006400',\n        darkkhaki: 'bdb76b',\n        darkmagenta: '8b008b',\n        darkolivegreen: '556b2f',\n        darkorange: 'ff8c00',\n        darkorchid: '9932cc',\n        darkred: '8b0000',\n        darksalmon: 'e9967a',\n        darkseagreen: '8fbc8f',\n        darkslateblue: '483d8b',\n        darkslategray: '2f4f4f',\n        darkturquoise: '00ced1',\n        darkviolet: '9400d3',\n        deeppink: 'ff1493',\n        deepskyblue: '00bfff',\n        dimgray: '696969',\n        dodgerblue: '1e90ff',\n        feldspar: 'd19275',\n        firebrick: 'b22222',\n        floralwhite: 'fffaf0',\n        forestgreen: '228b22',\n        fuchsia: 'ff00ff',\n        gainsboro: 'dcdcdc',\n        ghostwhite: 'f8f8ff',\n        gold: 'ffd700',\n        goldenrod: 'daa520',\n        gray: '808080',\n        green: '008000',\n        greenyellow: 'adff2f',\n        honeydew: 'f0fff0',\n        hotpink: 'ff69b4',\n        indianred : 'cd5c5c',\n        indigo : '4b0082',\n        ivory: 'fffff0',\n        khaki: 'f0e68c',\n        lavender: 'e6e6fa',\n        lavenderblush: 'fff0f5',\n        lawngreen: '7cfc00',\n        lemonchiffon: 'fffacd',\n        lightblue: 'add8e6',\n        lightcoral: 'f08080',\n        lightcyan: 'e0ffff',\n        lightgoldenrodyellow: 'fafad2',\n        lightgrey: 'd3d3d3',\n        lightgreen: '90ee90',\n        lightpink: 'ffb6c1',\n        lightsalmon: 'ffa07a',\n        lightseagreen: '20b2aa',\n        lightskyblue: '87cefa',\n        lightslateblue: '8470ff',\n        lightslategray: '778899',\n        lightsteelblue: 'b0c4de',\n        lightyellow: 'ffffe0',\n        lime: '00ff00',\n        limegreen: '32cd32',\n        linen: 'faf0e6',\n        magenta: 'ff00ff',\n        maroon: '800000',\n        mediumaquamarine: '66cdaa',\n        mediumblue: '0000cd',\n        mediumorchid: 'ba55d3',\n        mediumpurple: '9370d8',\n        mediumseagreen: '3cb371',\n        mediumslateblue: '7b68ee',\n        mediumspringgreen: '00fa9a',\n        mediumturquoise: '48d1cc',\n        mediumvioletred: 'c71585',\n        midnightblue: '191970',\n        mintcream: 'f5fffa',\n        mistyrose: 'ffe4e1',\n        moccasin: 'ffe4b5',\n        navajowhite: 'ffdead',\n        navy: '000080',\n        oldlace: 'fdf5e6',\n        olive: '808000',\n        olivedrab: '6b8e23',\n        orange: 'ffa500',\n        orangered: 'ff4500',\n        orchid: 'da70d6',\n        palegoldenrod: 'eee8aa',\n        palegreen: '98fb98',\n        paleturquoise: 'afeeee',\n        palevioletred: 'd87093',\n        papayawhip: 'ffefd5',\n        peachpuff: 'ffdab9',\n        peru: 'cd853f',\n        pink: 'ffc0cb',\n        plum: 'dda0dd',\n        powderblue: 'b0e0e6',\n        purple: '800080',\n        rebeccapurple: '663399',\n        red: 'ff0000',\n        rosybrown: 'bc8f8f',\n        royalblue: '4169e1',\n        saddlebrown: '8b4513',\n        salmon: 'fa8072',\n        sandybrown: 'f4a460',\n        seagreen: '2e8b57',\n        seashell: 'fff5ee',\n        sienna: 'a0522d',\n        silver: 'c0c0c0',\n        skyblue: '87ceeb',\n        slateblue: '6a5acd',\n        slategray: '708090',\n        snow: 'fffafa',\n        springgreen: '00ff7f',\n        steelblue: '4682b4',\n        tan: 'd2b48c',\n        teal: '008080',\n        thistle: 'd8bfd8',\n        tomato: 'ff6347',\n        turquoise: '40e0d0',\n        violet: 'ee82ee',\n        violetred: 'd02090',\n        wheat: 'f5deb3',\n        white: 'ffffff',\n        whitesmoke: 'f5f5f5',\n        yellow: 'ffff00',\n        yellowgreen: '9acd32'\n    };\n    color_string = simple_colors[color_string] || color_string;\n    // emd of simple type-in colors\n\n    // array of color definition objects\n    var color_defs = [\n        {\n            re: /^rgba\\((\\d{1,3}),\\s*(\\d{1,3}),\\s*(\\d{1,3}),\\s*((?:\\d?\\.)?\\d)\\)$/,\n            example: ['rgba(123, 234, 45, 0.8)', 'rgba(255,234,245,1.0)'],\n            process: function (bits){\n                return [\n                    parseInt(bits[1]),\n                    parseInt(bits[2]),\n                    parseInt(bits[3]),\n                    parseFloat(bits[4])\n                ];\n            }\n        },\n        {\n            re: /^rgb\\((\\d{1,3}),\\s*(\\d{1,3}),\\s*(\\d{1,3})\\)$/,\n            example: ['rgb(123, 234, 45)', 'rgb(255,234,245)'],\n            process: function (bits){\n                return [\n                    parseInt(bits[1]),\n                    parseInt(bits[2]),\n                    parseInt(bits[3])\n                ];\n            }\n        },\n        {\n            re: /^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n            example: ['#00ff00', '336699'],\n            process: function (bits){\n                return [\n                    parseInt(bits[1], 16),\n                    parseInt(bits[2], 16),\n                    parseInt(bits[3], 16)\n                ];\n            }\n        },\n        {\n            re: /^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n            example: ['#fb0', 'f0f'],\n            process: function (bits){\n                return [\n                    parseInt(bits[1] + bits[1], 16),\n                    parseInt(bits[2] + bits[2], 16),\n                    parseInt(bits[3] + bits[3], 16)\n                ];\n            }\n        }\n    ];\n\n    // search through the definitions to find a match\n    for (var i = 0; i < color_defs.length; i++) {\n        var re = color_defs[i].re;\n        var processor = color_defs[i].process;\n        var bits = re.exec(color_string);\n        if (bits) {\n            var channels = processor(bits);\n            this.r = channels[0];\n            this.g = channels[1];\n            this.b = channels[2];\n            if (channels.length > 3) {\n                this.alpha = channels[3];\n            }\n            this.ok = true;\n        }\n\n    }\n\n    // validate/cleanup values\n    this.r = (this.r < 0 || isNaN(this.r)) ? 0 : ((this.r > 255) ? 255 : this.r);\n    this.g = (this.g < 0 || isNaN(this.g)) ? 0 : ((this.g > 255) ? 255 : this.g);\n    this.b = (this.b < 0 || isNaN(this.b)) ? 0 : ((this.b > 255) ? 255 : this.b);\n    this.alpha = (this.alpha < 0) ? 0 : ((this.alpha > 1.0 || isNaN(this.alpha)) ? 1.0 : this.alpha);\n\n    // some getters\n    this.toRGB = function () {\n        return 'rgb(' + this.r + ', ' + this.g + ', ' + this.b + ')';\n    }\n    this.toRGBA = function () {\n        return 'rgba(' + this.r + ', ' + this.g + ', ' + this.b + ', ' + this.alpha + ')';\n    }\n    this.toHex = function () {\n        var r = this.r.toString(16);\n        var g = this.g.toString(16);\n        var b = this.b.toString(16);\n        if (r.length == 1) r = '0' + r;\n        if (g.length == 1) g = '0' + g;\n        if (b.length == 1) b = '0' + b;\n        return '#' + r + g + b;\n    }\n\n    // help\n    this.getHelpXML = function () {\n\n        var examples = new Array();\n        // add regexps\n        for (var i = 0; i < color_defs.length; i++) {\n            var example = color_defs[i].example;\n            for (var j = 0; j < example.length; j++) {\n                examples[examples.length] = example[j];\n            }\n        }\n        // add type-in colors\n        for (var sc in simple_colors) {\n            examples[examples.length] = sc;\n        }\n\n        var xml = document.createElement('ul');\n        xml.setAttribute('id', 'rgbcolor-examples');\n        for (var i = 0; i < examples.length; i++) {\n            try {\n                var list_item = document.createElement('li');\n                var list_color = new RGBColor(examples[i]);\n                var example_div = document.createElement('div');\n                example_div.style.cssText =\n                        'margin: 3px; '\n                        + 'border: 1px solid black; '\n                        + 'background:' + list_color.toHex() + '; '\n                        + 'color:' + list_color.toHex()\n                ;\n                example_div.appendChild(document.createTextNode('test'));\n                var list_item_value = document.createTextNode(\n                    ' ' + examples[i] + ' -> ' + list_color.toRGB() + ' -> ' + list_color.toHex()\n                );\n                list_item.appendChild(example_div);\n                list_item.appendChild(list_item_value);\n                xml.appendChild(list_item);\n\n            } catch(e){}\n        }\n        return xml;\n\n    }\n\n}\n", "if performance? and performance.now\n  module.exports = -> performance.now()\nelse if process? and process.hrtime\n  module.exports = -> (getNanoSeconds() - nodeLoadTime) / 1e6\n  hrtime = process.hrtime\n  getNanoSeconds = ->\n    hr = hrtime()\n    hr[0] * 1e9 + hr[1]\n  moduleLoadTime = getNanoSeconds()\n  upTime = process.uptime() * 1e9\n  nodeLoadTime = moduleLoadTime - upTime\nelse if Date.now\n  module.exports = -> Date.now() - loadTime\n  loadTime = Date.now()\nelse\n  module.exports = -> new Date().getTime() - loadTime\n  loadTime = new Date().getTime()\n", "function asyncGeneratorStep(n, t, e, r, o, a, c) {\n  try {\n    var i = n[a](c),\n      u = i.value;\n  } catch (n) {\n    return void e(n);\n  }\n  i.done ? t(u) : Promise.resolve(u).then(r, o);\n}\nfunction _asyncToGenerator(n) {\n  return function () {\n    var t = this,\n      e = arguments;\n    return new Promise(function (r, o) {\n      var a = n.apply(t, e);\n      function _next(n) {\n        asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n);\n      }\n      function _throw(n) {\n        asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n);\n      }\n      _next(void 0);\n    });\n  };\n}\nexport { _asyncToGenerator as default };", "import _typeof from \"./typeof.js\";\nimport toPrimitive from \"./toPrimitive.js\";\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nexport { toPropertyKey as default };", "import _typeof from \"./typeof.js\";\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nexport { toPrimitive as default };", "import toPropertyKey from \"./toPropertyKey.js\";\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nexport { _defineProperty as default };", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from) {\r\n    for (var i = 0, il = from.length, j = to.length; i < il; i++, j++)\r\n        to[j] = from[i];\r\n    return to;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import { SVGPathData } from \"./SVGPathData\";\nimport { SVGCommand } from \"./types\";\n\n// Encode SVG PathData\n// http://www.w3.org/TR/SVG/paths.html#PathDataBNF\n\n// Private consts : Char groups\nconst WSP = \" \";\n\nexport function encodeSVGPath(commands: SVGCommand | SVGCommand[]) {\n  let str = \"\";\n\n  if (!Array.isArray(commands)) {\n    commands = [commands];\n  }\n  for (let i = 0; i < commands.length; i++) {\n    const command = commands[i];\n    if (command.type === SVGPathData.CLOSE_PATH) {\n      str += \"z\";\n    } else if (command.type === SVGPathData.HORIZ_LINE_TO) {\n      str += (command.relative ? \"h\" : \"H\") +\n        command.x;\n    } else if (command.type === SVGPathData.VERT_LINE_TO) {\n      str += (command.relative ? \"v\" : \"V\") +\n        command.y;\n    } else if (command.type === SVGPathData.MOVE_TO) {\n      str += (command.relative ? \"m\" : \"M\") +\n        command.x + WSP + command.y;\n    } else if (command.type === SVGPathData.LINE_TO) {\n      str += (command.relative ? \"l\" : \"L\") +\n        command.x + WSP + command.y;\n    } else if (command.type === SVGPathData.CURVE_TO) {\n      str += (command.relative ? \"c\" : \"C\") +\n        command.x1 + WSP + command.y1 +\n        WSP + command.x2 + WSP + command.y2 +\n        WSP + command.x + WSP + command.y;\n    } else if (command.type === SVGPathData.SMOOTH_CURVE_TO) {\n      str += (command.relative ? \"s\" : \"S\") +\n        command.x2 + WSP + command.y2 +\n        WSP + command.x + WSP + command.y;\n    } else if (command.type === SVGPathData.QUAD_TO) {\n      str += (command.relative ? \"q\" : \"Q\") +\n        command.x1 + WSP + command.y1 +\n        WSP + command.x + WSP + command.y;\n    } else if (command.type === SVGPathData.SMOOTH_QUAD_TO) {\n      str += (command.relative ? \"t\" : \"T\") +\n        command.x + WSP + command.y;\n    } else if (command.type === SVGPathData.ARC) {\n      str += (command.relative ? \"a\" : \"A\") +\n        command.rX + WSP + command.rY +\n        WSP + command.xRot +\n        WSP + (+command.lArcFlag) + WSP + (+command.sweepFlag) +\n        WSP + command.x + WSP + command.y;\n    } else {\n      // Unknown command\n      throw new Error(\n        `Unexpected command type \"${ (command as any).type}\" at index ${i}.`);\n    }\n  }\n\n  return str;\n}\n", "import { SVGPathData } from \"./SVGPathData\";\nimport { CommandA, CommandC } from \"./types\";\n\nexport function rotate([x, y]: [number, number], rad: number) {\n  return [\n    x * Math.cos(rad) - y * Math.sin(rad),\n    x * Math.sin(rad) + y * Math.cos(rad),\n  ];\n}\n\nconst DEBUG_CHECK_NUMBERS = true;\nexport function assertNumbers(...numbers: number[]) {\n  if (DEBUG_CHECK_NUMBERS) {\n    for (let i = 0; i < numbers.length; i++) {\n      if (\"number\" !== typeof numbers[i]) {\n        throw new Error(\n          `assertNumbers arguments[${i}] is not a number. ${typeof numbers[i]} == typeof ${numbers[i]}`);\n      }\n    }\n  }\n  return true;\n}\n\nconst PI = Math.PI;\n\n/**\n * https://www.w3.org/TR/SVG/implnote.html#ArcImplementationNotes\n * Fixes rX and rY.\n * Ensures lArcFlag and sweepFlag are 0 or 1\n * Adds center coordinates: command.cX, command.cY (relative or absolute, depending on command.relative)\n * Adds start and end arc parameters (in degrees): command.phi1, command.phi2; phi1 < phi2 iff. c.sweepFlag == true\n */\nexport function annotateArcCommand(c: CommandA, x1: number, y1: number) {\n  c.lArcFlag = (0 === c.lArcFlag) ? 0 : 1;\n  c.sweepFlag = (0 === c.sweepFlag) ? 0 : 1;\n  // tslint:disable-next-line\n  let {rX, rY, x, y} = c;\n\n  rX = Math.abs(c.rX);\n  rY = Math.abs(c.rY);\n  const [x1_, y1_] = rotate([(x1 - x) / 2, (y1 - y) / 2], -c.xRot / 180 * PI);\n  const testValue = Math.pow(x1_, 2) / Math.pow(rX, 2) + Math.pow(y1_, 2) / Math.pow(rY, 2);\n\n  if (1 < testValue) {\n    rX *= Math.sqrt(testValue);\n    rY *= Math.sqrt(testValue);\n  }\n  c.rX = rX;\n  c.rY = rY;\n  const c_ScaleTemp = (Math.pow(rX, 2) * Math.pow(y1_, 2) + Math.pow(rY, 2) * Math.pow(x1_, 2));\n  const c_Scale = (c.lArcFlag !== c.sweepFlag ? 1 : -1) *\n    Math.sqrt(Math.max(0, (Math.pow(rX, 2) * Math.pow(rY, 2) - c_ScaleTemp) / c_ScaleTemp));\n  const cx_ = rX * y1_ / rY * c_Scale;\n  const cy_ = -rY * x1_ / rX * c_Scale;\n  const cRot = rotate([cx_, cy_], c.xRot / 180 * PI);\n\n  c.cX = cRot[0] + (x1 + x) / 2;\n  c.cY = cRot[1] + (y1 + y) / 2;\n  c.phi1 = Math.atan2((y1_ - cy_) / rY, (x1_ - cx_) / rX);\n  c.phi2 = Math.atan2((-y1_ - cy_) / rY, (-x1_ - cx_) / rX);\n  if (0 === c.sweepFlag && c.phi2 > c.phi1) {\n    c.phi2 -= 2 * PI;\n  }\n  if (1 === c.sweepFlag && c.phi2 < c.phi1) {\n    c.phi2 += 2 * PI;\n  }\n  c.phi1 *= 180 / PI;\n  c.phi2 *= 180 / PI;\n}\n\n/**\n * Solves a quadratic system of equations of the form\n *      a * x + b * y = c\n *      x² + y² = 1\n * This can be understood as the intersection of the unit circle with a line.\n *      => y = (c - a x) / b\n *      => x² + (c - a x)² / b² = 1\n *      => x² b² + c² - 2 c a x + a² x² = b²\n *      => (a² + b²) x² - 2 a c x + (c² - b²) = 0\n */\nexport function intersectionUnitCircleLine(a: number, b: number, c: number): [number, number][] {\n  assertNumbers(a, b, c);\n  // cf. pqFormula\n  const termSqr = a * a + b * b - c * c;\n\n  if (0 > termSqr) {\n    return [];\n  } else if (0 === termSqr) {\n    return [\n      [\n        (a * c) / (a * a + b * b),\n        (b * c) / (a * a + b * b)]];\n  }\n  const term = Math.sqrt(termSqr);\n\n  return [\n    [\n      (a * c + b * term) / (a * a + b * b),\n      (b * c - a * term) / (a * a + b * b)],\n    [\n      (a * c - b * term) / (a * a + b * b),\n      (b * c + a * term) / (a * a + b * b)]];\n\n}\n\nexport const DEG = Math.PI / 180;\n\nexport function lerp(a: number, b: number, t: number) {\n  return (1 - t) * a + t * b;\n}\n\nexport function arcAt(c: number, x1: number, x2: number, phiDeg: number) {\n  return c + Math.cos(phiDeg / 180 * PI) * x1 + Math.sin(phiDeg / 180 * PI) * x2;\n}\n\nexport function bezierRoot(x0: number, x1: number, x2: number, x3: number) {\n  const EPS = 1e-6;\n  const x01 = x1 - x0;\n  const x12 = x2 - x1;\n  const x23 = x3 - x2;\n  const a = 3 * x01 + 3 * x23 - 6 * x12;\n  const b = (x12 - x01) * 6;\n  const c = 3 * x01;\n  // solve a * t² + b * t + c = 0\n\n  if (Math.abs(a) < EPS) {\n    // equivalent to b * t + c =>\n    return [-c / b];\n  }\n  return pqFormula(b / a, c / a, EPS);\n\n}\n\nexport function bezierAt(x0: number, x1: number, x2: number, x3: number, t: number) {\n  // console.log(x0, y0, x1, y1, x2, y2, x3, y3, t)\n  const s = 1 - t;\n  const c0 = s * s * s;\n  const c1 = 3 * s * s * t;\n  const c2 = 3 * s * t * t;\n  const c3 = t * t * t;\n\n  return x0 * c0 + x1 * c1 + x2 * c2 + x3 * c3;\n}\n\nfunction pqFormula(p: number, q: number, PRECISION = 1e-6) {\n  // 4 times the discriminant:in\n  const discriminantX4 = p * p / 4 - q;\n\n  if (discriminantX4 < -PRECISION) {\n    return [];\n  } else if (discriminantX4 <= PRECISION) {\n    return [-p / 2];\n  }\n  const root = Math.sqrt(discriminantX4);\n\n  return [-(p / 2) - root, -(p / 2) + root];\n\n}\n\nexport function a2c(arc: CommandA, x0: number, y0: number): CommandC[] {\n  if (!arc.cX) {\n    annotateArcCommand(arc, x0, y0);\n  }\n\n  const phiMin = Math.min(arc.phi1!, arc.phi2!), phiMax = Math.max(arc.phi1!, arc.phi2!), deltaPhi = phiMax - phiMin;\n  const partCount = Math.ceil(deltaPhi / 90 );\n\n  const result: CommandC[] = new Array(partCount);\n  let prevX = x0, prevY = y0;\n  for (let i = 0; i < partCount; i++) {\n    const phiStart = lerp(arc.phi1!, arc.phi2!, i / partCount);\n    const phiEnd = lerp(arc.phi1!, arc.phi2!, (i + 1) / partCount);\n    const deltaPhi = phiEnd - phiStart;\n    const f = 4 / 3 * Math.tan(deltaPhi * DEG / 4);\n    // x1/y1, x2/y2 and x/y coordinates on the unit circle for phiStart/phiEnd\n    const [x1, y1] = [\n      Math.cos(phiStart * DEG) - f * Math.sin(phiStart * DEG),\n      Math.sin(phiStart * DEG) + f * Math.cos(phiStart * DEG)];\n    const [x, y] = [Math.cos(phiEnd * DEG), Math.sin(phiEnd * DEG)];\n    const [x2, y2] = [x + f * Math.sin(phiEnd * DEG), y - f * Math.cos(phiEnd * DEG)];\n    result[i] = {relative: arc.relative, type: SVGPathData.CURVE_TO } as any;\n    const transform = (x: number, y: number) => {\n      const [xTemp, yTemp] = rotate([x * arc.rX, y * arc.rY], arc.xRot);\n      return [arc.cX! + xTemp, arc.cY! + yTemp];\n    };\n    [result[i].x1, result[i].y1] = transform(x1, y1);\n    [result[i].x2, result[i].y2] = transform(x2, y2);\n    [result[i].x, result[i].y] = transform(x, y);\n    if (arc.relative) {\n      result[i].x1 -= prevX;\n      result[i].y1 -= prevY;\n      result[i].x2 -= prevX;\n      result[i].y2 -= prevY;\n      result[i].x -= prevX;\n      result[i].y -= prevY;\n    }\n    [prevX, prevY] = [result[i].x, result[i].y];\n  }\n  return result;\n}\n", "// Transform SVG PathData\n// http://www.w3.org/TR/SVG/paths.html#PathDataBNF\n\nimport { a2c, annotateArcCommand, arcAt, assertNumbers, bezierAt, bezierRoot,\n  intersectionUnitCircleLine } from \"./mathUtils\";\nimport { SVGPathData } from \"./SVGPathData\";\nimport { SVGCommand, TransformFunction } from \"./types\";\n\nexport namespace SVGPathDataTransformer {\n  // Predefined transforming functions\n  // Rounds commands values\n  export function ROUND(roundVal = 1e13) {\n    assertNumbers(roundVal);\n    function rf(val: number) { return Math.round(val * roundVal) / roundVal; }\n    return function round(command: any) {\n      if (\"undefined\" !== typeof command.x1) {\n        command.x1 = rf(command.x1);\n      }\n      if (\"undefined\" !== typeof command.y1) {\n        command.y1 = rf(command.y1);\n      }\n\n      if (\"undefined\" !== typeof command.x2) {\n        command.x2 = rf(command.x2);\n      }\n      if (\"undefined\" !== typeof command.y2) {\n        command.y2 = rf(command.y2);\n      }\n\n      if (\"undefined\" !== typeof command.x) {\n        command.x = rf(command.x);\n      }\n      if (\"undefined\" !== typeof command.y) {\n        command.y = rf(command.y);\n      }\n\n      if (\"undefined\" !== typeof command.rX) {\n        command.rX = rf(command.rX);\n      }\n      if (\"undefined\" !== typeof command.rY) {\n        command.rY = rf(command.rY);\n      }\n\n      return command;\n    };\n  }\n  // Relative to absolute commands\n  export function TO_ABS() {\n    return INFO((command, prevX, prevY) => {\n      if (command.relative) {\n        // x1/y1 values\n        if (\"undefined\" !== typeof command.x1) {\n          command.x1 += prevX;\n        }\n        if (\"undefined\" !== typeof command.y1) {\n          command.y1 += prevY;\n        }\n        // x2/y2 values\n        if (\"undefined\" !== typeof command.x2) {\n          command.x2 += prevX;\n        }\n        if (\"undefined\" !== typeof command.y2) {\n          command.y2 += prevY;\n        }\n        // Finally x/y values\n        if (\"undefined\" !== typeof command.x) {\n          command.x += prevX;\n        }\n        if (\"undefined\" !== typeof command.y) {\n          command.y += prevY;\n        }\n        command.relative = false;\n      }\n      return command;\n    });\n  }\n  // Absolute to relative commands\n  export function TO_REL() {\n    return INFO((command, prevX, prevY) => {\n      if (!command.relative) {\n        // x1/y1 values\n        if (\"undefined\" !== typeof command.x1) {\n          command.x1 -= prevX;\n        }\n        if (\"undefined\" !== typeof command.y1) {\n          command.y1 -= prevY;\n        }\n        // x2/y2 values\n        if (\"undefined\" !== typeof command.x2) {\n          command.x2 -= prevX;\n        }\n        if (\"undefined\" !== typeof command.y2) {\n          command.y2 -= prevY;\n        }\n        // Finally x/y values\n        if (\"undefined\" !== typeof command.x) {\n          command.x -= prevX;\n        }\n        if (\"undefined\" !== typeof command.y) {\n          command.y -= prevY;\n        }\n        command.relative = true;\n      }\n      return command;\n    });\n  }\n  // Convert H, V, Z and A with rX = 0 to L\n  export function NORMALIZE_HVZ(normalizeZ = true, normalizeH = true, normalizeV = true) {\n    return INFO((command, prevX, prevY, pathStartX, pathStartY) => {\n      if (isNaN(pathStartX) && !(command.type & SVGPathData.MOVE_TO)) {\n        throw new Error(\"path must start with moveto\");\n      }\n      if (normalizeH && command.type & SVGPathData.HORIZ_LINE_TO) {\n        command.type = SVGPathData.LINE_TO;\n        command.y = command.relative ? 0 : prevY;\n      }\n      if (normalizeV && command.type & SVGPathData.VERT_LINE_TO) {\n        command.type = SVGPathData.LINE_TO;\n        command.x = command.relative ? 0 : prevX;\n      }\n      if (normalizeZ && command.type & SVGPathData.CLOSE_PATH) {\n        command.type = SVGPathData.LINE_TO;\n        command.x = command.relative ? pathStartX - prevX : pathStartX;\n        command.y = command.relative ? pathStartY - prevY : pathStartY;\n      }\n      if (command.type & SVGPathData.ARC && (0 === command.rX || 0 === command.rY)) {\n        command.type = SVGPathData.LINE_TO;\n        delete command.rX;\n        delete command.rY;\n        delete command.xRot;\n        delete command.lArcFlag;\n        delete command.sweepFlag;\n      }\n      return command;\n    });\n  }\n  /*\n   * Transforms smooth curves and quads to normal curves and quads (SsTt to CcQq)\n   */\n  export function NORMALIZE_ST() {\n    let prevCurveC2X = NaN;\n    let prevCurveC2Y = NaN;\n    let prevQuadCX = NaN;\n    let prevQuadCY = NaN;\n\n    return INFO((command, prevX, prevY) => {\n      if (command.type & SVGPathData.SMOOTH_CURVE_TO) {\n        command.type = SVGPathData.CURVE_TO;\n        prevCurveC2X = isNaN(prevCurveC2X) ? prevX : prevCurveC2X;\n        prevCurveC2Y = isNaN(prevCurveC2Y) ? prevY : prevCurveC2Y;\n        command.x1 = command.relative ? prevX - prevCurveC2X : 2 * prevX - prevCurveC2X;\n        command.y1 = command.relative ? prevY - prevCurveC2Y : 2 * prevY - prevCurveC2Y;\n      }\n      if (command.type & SVGPathData.CURVE_TO) {\n        prevCurveC2X = command.relative ? prevX + command.x2 : command.x2;\n        prevCurveC2Y = command.relative ? prevY + command.y2 : command.y2;\n      } else {\n        prevCurveC2X = NaN;\n        prevCurveC2Y = NaN;\n      }\n      if (command.type & SVGPathData.SMOOTH_QUAD_TO) {\n        command.type = SVGPathData.QUAD_TO;\n        prevQuadCX = isNaN(prevQuadCX) ? prevX : prevQuadCX;\n        prevQuadCY = isNaN(prevQuadCY) ? prevY : prevQuadCY;\n        command.x1 = command.relative ? prevX - prevQuadCX : 2 * prevX - prevQuadCX;\n        command.y1 = command.relative ? prevY - prevQuadCY : 2 * prevY - prevQuadCY;\n      }\n      if (command.type & SVGPathData.QUAD_TO) {\n        prevQuadCX = command.relative ? prevX + command.x1 : command.x1;\n        prevQuadCY = command.relative ? prevY + command.y1 : command.y1;\n      } else {\n        prevQuadCX = NaN;\n        prevQuadCY = NaN;\n      }\n\n      return command;\n    });\n  }\n  /*\n   * A quadratic bézier curve can be represented by a cubic bézier curve which has\n   * the same end points as the quadratic and both control points in place of the\n   * quadratic\"s one.\n   *\n   * This transformer replaces QqTt commands with Cc commands respectively.\n   * This is useful for reading path data into a system which only has a\n   * representation for cubic curves.\n   */\n  export function QT_TO_C() {\n    let prevQuadX1 = NaN;\n    let prevQuadY1 = NaN;\n\n    return INFO((command, prevX, prevY) => {\n      if (command.type & SVGPathData.SMOOTH_QUAD_TO) {\n        command.type = SVGPathData.QUAD_TO;\n        prevQuadX1 = isNaN(prevQuadX1) ? prevX : prevQuadX1;\n        prevQuadY1 = isNaN(prevQuadY1) ? prevY : prevQuadY1;\n        command.x1 = command.relative ? prevX - prevQuadX1 : 2 * prevX - prevQuadX1;\n        command.y1 = command.relative ? prevY - prevQuadY1 : 2 * prevY - prevQuadY1;\n      }\n      if (command.type & SVGPathData.QUAD_TO) {\n        prevQuadX1 = command.relative ? prevX + command.x1 : command.x1;\n        prevQuadY1 = command.relative ? prevY + command.y1 : command.y1;\n        const x1 = command.x1;\n        const y1 = command.y1;\n\n        command.type = SVGPathData.CURVE_TO;\n        command.x1 = ((command.relative ? 0 : prevX) + x1 * 2) / 3;\n        command.y1 = ((command.relative ? 0 : prevY) + y1 * 2) / 3;\n        command.x2 = (command.x + x1 * 2) / 3;\n        command.y2 = (command.y + y1 * 2) / 3;\n      } else {\n        prevQuadX1 = NaN;\n        prevQuadY1 = NaN;\n      }\n\n      return command;\n    });\n  }\n  export function INFO(\n    f: (command: any, prevXAbs: number, prevYAbs: number,\n        pathStartXAbs: number, pathStartYAbs: number) => any | any[]) {\n    let prevXAbs = 0;\n    let prevYAbs = 0;\n    let pathStartXAbs = NaN;\n    let pathStartYAbs = NaN;\n\n    return function transform(command: any) {\n      if (isNaN(pathStartXAbs) && !(command.type & SVGPathData.MOVE_TO)) {\n        throw new Error(\"path must start with moveto\");\n      }\n\n      const result = f(command, prevXAbs, prevYAbs, pathStartXAbs, pathStartYAbs);\n\n      if (command.type & SVGPathData.CLOSE_PATH) {\n        prevXAbs = pathStartXAbs;\n        prevYAbs = pathStartYAbs;\n      }\n\n      if (\"undefined\" !== typeof command.x) {\n        prevXAbs = (command.relative ? prevXAbs + command.x : command.x);\n      }\n      if (\"undefined\" !== typeof command.y) {\n        prevYAbs = (command.relative ? prevYAbs + command.y : command.y);\n      }\n\n      if (command.type & SVGPathData.MOVE_TO) {\n        pathStartXAbs = prevXAbs;\n        pathStartYAbs = prevYAbs;\n      }\n\n      return result;\n    };\n  }\n  /*\n   * remove 0-length segments\n   */\n  export function SANITIZE(EPS = 0) {\n    assertNumbers(EPS);\n    let prevCurveC2X = NaN;\n    let prevCurveC2Y = NaN;\n    let prevQuadCX = NaN;\n    let prevQuadCY = NaN;\n\n    return INFO((command, prevX, prevY, pathStartX, pathStartY) => {\n      const abs = Math.abs;\n      let skip = false;\n      let x1Rel = 0;\n      let y1Rel = 0;\n\n      if (command.type & SVGPathData.SMOOTH_CURVE_TO) {\n        x1Rel = isNaN(prevCurveC2X) ? 0 : prevX - prevCurveC2X;\n        y1Rel = isNaN(prevCurveC2Y) ? 0 : prevY - prevCurveC2Y;\n      }\n      if (command.type & (SVGPathData.CURVE_TO | SVGPathData.SMOOTH_CURVE_TO)) {\n        prevCurveC2X = command.relative ? prevX + command.x2 : command.x2;\n        prevCurveC2Y = command.relative ? prevY + command.y2 : command.y2;\n      } else {\n        prevCurveC2X = NaN;\n        prevCurveC2Y = NaN;\n      }\n      if (command.type & SVGPathData.SMOOTH_QUAD_TO) {\n        prevQuadCX = isNaN(prevQuadCX) ? prevX : 2 * prevX - prevQuadCX;\n        prevQuadCY = isNaN(prevQuadCY) ? prevY : 2 * prevY - prevQuadCY;\n      } else if (command.type & SVGPathData.QUAD_TO) {\n        prevQuadCX = command.relative ? prevX + command.x1 : command.x1;\n        prevQuadCY = command.relative ? prevY + command.y1 : command.y2;\n      } else {\n        prevQuadCX = NaN;\n        prevQuadCY = NaN;\n      }\n\n      if (command.type & SVGPathData.LINE_COMMANDS ||\n        command.type & SVGPathData.ARC && (0 === command.rX || 0 === command.rY || !command.lArcFlag) ||\n        command.type & SVGPathData.CURVE_TO || command.type & SVGPathData.SMOOTH_CURVE_TO ||\n        command.type & SVGPathData.QUAD_TO || command.type & SVGPathData.SMOOTH_QUAD_TO) {\n        const xRel = \"undefined\" === typeof command.x ? 0 :\n          (command.relative ? command.x : command.x - prevX);\n        const yRel = \"undefined\" === typeof command.y ? 0 :\n          (command.relative ? command.y : command.y - prevY);\n\n        x1Rel = !isNaN(prevQuadCX) ? prevQuadCX - prevX :\n          \"undefined\" === typeof command.x1 ? x1Rel :\n            command.relative ? command.x :\n              command.x1 - prevX;\n        y1Rel = !isNaN(prevQuadCY) ? prevQuadCY - prevY :\n          \"undefined\" === typeof command.y1 ? y1Rel :\n            command.relative ? command.y :\n              command.y1 - prevY;\n\n        const x2Rel = \"undefined\" === typeof command.x2 ? 0 :\n          (command.relative ? command.x : command.x2 - prevX);\n        const y2Rel = \"undefined\" === typeof command.y2 ? 0 :\n          (command.relative ? command.y : command.y2 - prevY);\n\n        if (abs(xRel) <= EPS && abs(yRel) <= EPS &&\n          abs(x1Rel) <= EPS && abs(y1Rel) <= EPS &&\n          abs(x2Rel) <= EPS && abs(y2Rel) <= EPS) {\n          skip = true;\n        }\n      }\n\n      if (command.type & SVGPathData.CLOSE_PATH) {\n        if (abs(prevX - pathStartX) <= EPS && abs(prevY - pathStartY) <= EPS) {\n          skip = true;\n        }\n      }\n\n      return skip ? [] : command;\n    });\n  }\n  // SVG Transforms : http://www.w3.org/TR/SVGTiny12/coords.html#TransformList\n  // Matrix : http://apike.ca/prog_svg_transform.html\n  // a c e\n  // b d f\n  export function MATRIX(a: number, b: number, c: number, d: number, e: number, f: number) {\n    assertNumbers(a, b, c, d, e, f);\n\n    return INFO((command, prevX, prevY, pathStartX) => {\n      const origX1 = command.x1;\n      const origX2 = command.x2;\n      // if isNaN(pathStartX), then this is the first command, which is ALWAYS an\n      // absolute MOVE_TO, regardless what the relative flag says\n      const comRel = command.relative && !isNaN(pathStartX);\n      const x = \"undefined\" !== typeof command.x ? command.x : (comRel ? 0 : prevX);\n      const y = \"undefined\" !== typeof command.y ? command.y : (comRel ? 0 : prevY);\n\n      if (command.type & SVGPathData.HORIZ_LINE_TO && 0 !== b) {\n        command.type = SVGPathData.LINE_TO;\n        command.y = command.relative ? 0 : prevY;\n      }\n      if (command.type & SVGPathData.VERT_LINE_TO && 0 !== c) {\n        command.type = SVGPathData.LINE_TO;\n        command.x = command.relative ? 0 : prevX;\n      }\n\n      if (\"undefined\" !== typeof command.x) {\n        command.x = (command.x * a) + (y * c) + (comRel ? 0 : e);\n      }\n      if (\"undefined\" !== typeof command.y) {\n        command.y = (x * b) + command.y * d + (comRel ? 0 : f);\n      }\n      if (\"undefined\" !== typeof command.x1) {\n        command.x1 = command.x1 * a + command.y1 * c + (comRel ? 0 : e);\n      }\n      if (\"undefined\" !== typeof command.y1) {\n        command.y1 = origX1 * b + command.y1 * d + (comRel ? 0 : f);\n      }\n      if (\"undefined\" !== typeof command.x2) {\n        command.x2 = command.x2 * a + command.y2 * c + (comRel ? 0 : e);\n      }\n      if (\"undefined\" !== typeof command.y2) {\n        command.y2 = origX2 * b + command.y2 * d + (comRel ? 0 : f);\n      }\n      function sqr(x: number) { return x * x; }\n      const det = a * d - b * c;\n\n      if (\"undefined\" !== typeof command.xRot) {\n        // Skip if this is a pure translation\n        if (1 !== a || 0 !== b || 0 !== c || 1 !== d) {\n          // Special case for singular matrix\n          if (0 === det) {\n            // In the singular case, the arc is compressed to a line. The actual geometric image of the original\n            // curve under this transform possibly extends beyond the starting and/or ending points of the segment, but\n            // for simplicity we ignore this detail and just replace this command with a single line segment.\n            delete command.rX;\n            delete command.rY;\n            delete command.xRot;\n            delete command.lArcFlag;\n            delete command.sweepFlag;\n            command.type = SVGPathData.LINE_TO;\n          } else {\n            // Convert to radians\n            const xRot = command.xRot * Math.PI / 180;\n\n            // Convert rotated ellipse to general conic form\n            // x0^2/rX^2 + y0^2/rY^2 - 1 = 0\n            // x0 = x*cos(xRot) + y*sin(xRot)\n            // y0 = -x*sin(xRot) + y*cos(xRot)\n            // --> A*x^2 + B*x*y + C*y^2 - 1 = 0, where\n            const sinRot = Math.sin(xRot);\n            const cosRot = Math.cos(xRot);\n            const xCurve = 1 / sqr(command.rX);\n            const yCurve = 1 / sqr(command.rY);\n            const A = sqr(cosRot) * xCurve + sqr(sinRot) * yCurve;\n            const B = 2 * sinRot * cosRot * (xCurve - yCurve);\n            const C = sqr(sinRot) * xCurve + sqr(cosRot) * yCurve;\n\n            // Apply matrix to A*x^2 + B*x*y + C*y^2 - 1 = 0\n            // x1 = a*x + c*y\n            // y1 = b*x + d*y\n            //      (we can ignore e and f, since pure translations don\"t affect the shape of the ellipse)\n            // --> A1*x1^2 + B1*x1*y1 + C1*y1^2 - det^2 = 0, where\n            const A1 = A * d * d - B * b * d + C * b * b;\n            const B1 = B * (a * d + b * c) - 2 * (A * c * d + C * a * b);\n            const C1 = A * c * c - B * a * c + C * a * a;\n\n            // Unapply newXRot to get back to axis-aligned ellipse equation\n            // x1 = x2*cos(newXRot) - y2*sin(newXRot)\n            // y1 = x2*sin(newXRot) + y2*cos(newXRot)\n            // A1*x1^2 + B1*x1*y1 + C1*y1^2 - det^2 =\n            //   x2^2*(A1*cos(newXRot)^2 + B1*sin(newXRot)*cos(newXRot) + C1*sin(newXRot)^2)\n            //   + x2*y2*(2*(C1 - A1)*sin(newXRot)*cos(newXRot) + B1*(cos(newXRot)^2 - sin(newXRot)^2))\n            //   + y2^2*(A1*sin(newXRot)^2 - B1*sin(newXRot)*cos(newXRot) + C1*cos(newXRot)^2)\n            //   (which must have the same zeroes as)\n            // x2^2/newRX^2 + y2^2/newRY^2 - 1\n            //   (so we have)\n            // 2*(C1 - A1)*sin(newXRot)*cos(newXRot) + B1*(cos(newXRot)^2 - sin(newXRot)^2) = 0\n            // (A1 - C1)*sin(2*newXRot) = B1*cos(2*newXRot)\n            // 2*newXRot = atan2(B1, A1 - C1)\n            const newXRot = ((Math.atan2(B1, A1 - C1) + Math.PI) % Math.PI) / 2;\n            // For any integer n, (atan2(B1, A1 - C1) + n*pi)/2 is a solution to the above; incrementing n just swaps\n            // the x and y radii computed below (since that\"s what rotating an ellipse by pi/2 does).  Choosing the\n            // rotation between 0 and pi/2 eliminates the ambiguity and leads to more predictable output.\n\n            // Finally, we get newRX and newRY from the same-zeroes relationship that gave us newXRot\n            const newSinRot = Math.sin(newXRot);\n            const newCosRot = Math.cos(newXRot);\n\n            command.rX = Math.abs(det) /\n              Math.sqrt(A1 * sqr(newCosRot) + B1 * newSinRot * newCosRot + C1 * sqr(newSinRot));\n            command.rY = Math.abs(det) /\n              Math.sqrt(A1 * sqr(newSinRot) - B1 * newSinRot * newCosRot + C1 * sqr(newCosRot));\n            command.xRot = newXRot * 180 / Math.PI;\n          }\n        }\n      }\n      // sweepFlag needs to be inverted when mirroring shapes\n      // see http://www.itk.ilstu.edu/faculty/javila/SVG/SVG_drawing1/elliptical_curve.htm\n      // m 65,10 a 50,25 0 1 0 50,25\n      // M 65,60 A 50,25 0 1 1 115,35\n      if (\"undefined\" !== typeof command.sweepFlag && 0 > det) {\n        command.sweepFlag = +!command.sweepFlag;\n      }\n      return command;\n    });\n  }\n  export function ROTATE(a: number, x = 0, y = 0) {\n    assertNumbers(a, x, y);\n    const sin = Math.sin(a);\n    const cos = Math.cos(a);\n\n    return MATRIX(cos, sin, -sin, cos, x - x * cos + y * sin, y - x * sin - y * cos);\n  }\n  export function TRANSLATE(dX: number, dY = 0) {\n    assertNumbers(dX, dY);\n    return MATRIX(1, 0, 0, 1, dX, dY);\n  }\n  export function SCALE(dX: number, dY = dX) {\n    assertNumbers(dX, dY);\n    return MATRIX(dX, 0, 0, dY, 0, 0);\n  }\n  export function SKEW_X(a: number) {\n    assertNumbers(a);\n    return MATRIX(1, 0, Math.atan(a), 1, 0, 0);\n  }\n  export function SKEW_Y(a: number) {\n    assertNumbers(a);\n    return MATRIX(1, Math.atan(a), 0, 1, 0, 0);\n  }\n  export function X_AXIS_SYMMETRY(xOffset = 0) {\n    assertNumbers(xOffset);\n    return MATRIX(-1, 0, 0, 1, xOffset, 0);\n  }\n  export function Y_AXIS_SYMMETRY(yOffset = 0) {\n    assertNumbers(yOffset);\n    return MATRIX(1, 0, 0, -1, 0, yOffset);\n  }\n  // Convert arc commands to curve commands\n  export function A_TO_C() {\n    return INFO((command, prevX, prevY) => {\n      if (SVGPathData.ARC === command.type) {\n        return a2c(command, command.relative ? 0 : prevX, command.relative ? 0 : prevY);\n      }\n      return command;\n    });\n  }\n  // @see annotateArcCommand\n  export function ANNOTATE_ARCS() {\n    return INFO((c, x1, y1) => {\n      if (c.relative) {\n        x1 = 0;\n        y1 = 0;\n      }\n      if (SVGPathData.ARC === c.type) {\n        annotateArcCommand(c, x1, y1);\n      }\n      return c;\n    });\n  }\n  export function CLONE() {\n    return (c: SVGCommand) => {\n      const result = {} as SVGCommand;\n      // tslint:disable-next-line\n      for (const key in c) {\n        result[key as keyof SVGCommand] = c[key as keyof SVGCommand];\n      }\n      return result;\n    };\n  }\n  // @see annotateArcCommand\n  export function CALCULATE_BOUNDS() {\n    const clone = CLONE();\n    const toAbs = TO_ABS();\n    const qtToC = QT_TO_C();\n    const normST = NORMALIZE_ST();\n    const f: TransformFunction & {minX: number, maxX: number, minY: number, maxY: number} =\n        INFO((command, prevXAbs, prevYAbs) => {\n      const c = normST(qtToC(toAbs(clone(command))));\n      function fixX(absX: number) {\n        if (absX > f.maxX) { f.maxX = absX; }\n        if (absX < f.minX) { f.minX = absX; }\n      }\n      function fixY(absY: number) {\n        if (absY > f.maxY) { f.maxY = absY; }\n        if (absY < f.minY) { f.minY = absY; }\n      }\n      if (c.type & SVGPathData.DRAWING_COMMANDS) {\n        fixX(prevXAbs);\n        fixY(prevYAbs);\n      }\n      if (c.type & SVGPathData.HORIZ_LINE_TO) {\n        fixX(c.x);\n      }\n      if (c.type & SVGPathData.VERT_LINE_TO) {\n        fixY(c.y);\n      }\n      if (c.type & SVGPathData.LINE_TO) {\n        fixX(c.x);\n        fixY(c.y);\n      }\n      if (c.type & SVGPathData.CURVE_TO) {\n        // add start and end points\n        fixX(c.x);\n        fixY(c.y);\n        const xDerivRoots = bezierRoot(prevXAbs, c.x1, c.x2, c.x);\n\n        for (const derivRoot of xDerivRoots) {\n          if (0 < derivRoot && 1 > derivRoot) {\n            fixX(bezierAt(prevXAbs, c.x1, c.x2, c.x, derivRoot));\n          }\n        }\n        const yDerivRoots = bezierRoot(prevYAbs, c.y1, c.y2, c.y);\n\n        for (const derivRoot of yDerivRoots) {\n          if (0 < derivRoot && 1 > derivRoot) {\n            fixY(bezierAt(prevYAbs, c.y1, c.y2, c.y, derivRoot));\n          }\n        }\n      }\n      if (c.type & SVGPathData.ARC) {\n        // add start and end points\n        fixX(c.x);\n        fixY(c.y);\n        annotateArcCommand(c, prevXAbs, prevYAbs);\n        // p = cos(phi) * xv + sin(phi) * yv\n        // dp = -sin(phi) * xv + cos(phi) * yv = 0\n        const xRotRad = c.xRot / 180 * Math.PI;\n        // points on ellipse for phi = 0° and phi = 90°\n        const x0 = Math.cos(xRotRad) * c.rX;\n        const y0 = Math.sin(xRotRad) * c.rX;\n        const x90 = -Math.sin(xRotRad) * c.rY;\n        const y90 = Math.cos(xRotRad) * c.rY;\n\n        // annotateArcCommand returns phi1 and phi2 such that -180° < phi1 < 180° and phi2 is smaller or greater\n        // depending on the sweep flag. Calculate phiMin, phiMax such that -180° < phiMin < 180° and phiMin < phiMax\n        const [phiMin, phiMax] = c.phi1 < c.phi2 ?\n          [c.phi1, c.phi2] :\n          (-180 > c.phi2 ? [c.phi2 + 360, c.phi1 + 360] : [c.phi2, c.phi1]);\n        const normalizeXiEta = ([xi, eta]: [number, number]) => {\n          const phiRad = Math.atan2(eta, xi);\n          const phi = phiRad * 180 / Math.PI;\n\n          return phi < phiMin ? phi + 360 : phi;\n        };\n        // xi = cos(phi), eta = sin(phi)\n\n        const xDerivRoots = intersectionUnitCircleLine(x90, -x0, 0).map(normalizeXiEta);\n        for (const derivRoot of xDerivRoots) {\n          if (derivRoot > phiMin && derivRoot < phiMax) {\n            fixX(arcAt(c.cX, x0, x90, derivRoot));\n          }\n        }\n\n        const yDerivRoots = intersectionUnitCircleLine(y90, -y0, 0).map(normalizeXiEta);\n        for (const derivRoot of yDerivRoots) {\n          if (derivRoot > phiMin && derivRoot < phiMax) {\n            fixY(arcAt(c.cY, y0, y90, derivRoot));\n          }\n        }\n      }\n      return command;\n    }) as any;\n\n    f.minX = Infinity;\n    f.maxX = -Infinity;\n    f.minY = Infinity;\n    f.maxY = -Infinity;\n    return f;\n  }\n}\n", "import { SVGPathDataTransformer } from \"./SVGPathDataTransformer\";\nimport { TransformFunction } from \"./types\";\n\nexport abstract class TransformableSVG {\n  round(x?: number) {\n    return this.transform(SVGPathDataTransformer.ROUND(x));\n  }\n\n  toAbs() {\n    return this.transform(SVGPathDataTransformer.TO_ABS());\n  }\n\n  toRel() {\n    return this.transform(SVGPathDataTransformer.TO_REL());\n  }\n\n  normalizeHVZ(a?: boolean, b?: boolean, c?: boolean) {\n    return this.transform(SVGPathDataTransformer.NORMALIZE_HVZ(a, b, c));\n  }\n\n  normalizeST() {\n    return this.transform(SVGPathDataTransformer.NORMALIZE_ST());\n  }\n\n  qtToC() {\n    return this.transform(SVGPathDataTransformer.QT_TO_C());\n  }\n\n  aToC() {\n    return this.transform(SVGPathDataTransformer.A_TO_C());\n  }\n\n  sanitize(eps?: number) {\n    return this.transform(SVGPathDataTransformer.SANITIZE(eps));\n  }\n\n  translate(x: number, y?: number) {\n    return this.transform(SVGPathDataTransformer.TRANSLATE(x, y));\n  }\n\n  scale(x: number, y?: number) {\n    return this.transform(SVGPathDataTransformer.SCALE(x, y));\n  }\n\n  rotate(a: number, x?: number, y?: number) {\n    return this.transform(SVGPathDataTransformer.ROTATE(a, x, y));\n  }\n\n  matrix(a: number, b: number, c: number, d: number, e: number, f: number) {\n    return this.transform(SVGPathDataTransformer.MATRIX(a, b, c, d, e, f));\n  }\n\n  skewX(a: number) {\n    return this.transform(SVGPathDataTransformer.SKEW_X(a));\n  }\n\n  skewY(a: number) {\n    return this.transform(SVGPathDataTransformer.SKEW_Y(a));\n  }\n\n  xSymmetry(xOffset?: number) {\n    return this.transform(SVGPathDataTransformer.X_AXIS_SYMMETRY(xOffset));\n  }\n\n  ySymmetry(yOffset?: number) {\n    return this.transform(SVGPathDataTransformer.Y_AXIS_SYMMETRY(yOffset));\n  }\n\n  annotateArcs() {\n    return this.transform(SVGPathDataTransformer.ANNOTATE_ARCS());\n  }\n\n  abstract transform(transformFunction: TransformFunction): this;\n}\n", "// Parse SVG PathData\n// http://www.w3.org/TR/SVG/paths.html#PathDataBNF\nimport { COMMAND_ARG_COUNTS, SVGPathData } from \"./SVGPathData\";\nimport { TransformableSVG } from \"./TransformableSVG\";\nimport { SVGCommand, TransformFunction } from \"./types\";\n// Private consts : Char groups\nconst isWhiteSpace = (c: string) =>\n  \" \" === c || \"\\t\" === c || \"\\r\" === c || \"\\n\" === c;\nconst isDigit = (c: string) =>\n  \"0\".charCodeAt(0) <= c.charCodeAt(0) && c.charCodeAt(0) <= \"9\".charCodeAt(0);\nconst COMMANDS = \"mMzZlLhHvVcCsSqQtTaA\";\n\nexport class SVGPathDataParser extends TransformableSVG {\n  private curNumber: string = \"\";\n  private curCommandType: SVGCommand[\"type\"] | -1 = -1;\n  private curCommandRelative = false;\n  private canParseCommandOrComma = true;\n  private curNumberHasExp = false;\n  private curNumberHasExpDigits = false;\n  private curNumberHasDecimal = false;\n  private curArgs: number[] = [];\n\n  constructor() {\n    super();\n  }\n\n  finish(commands: SVGCommand[] = []) {\n    this.parse(\" \", commands);\n    // Adding residual command\n    if (0 !== this.curArgs.length || !this.canParseCommandOrComma) {\n      throw new SyntaxError(\"Unterminated command at the path end.\");\n    }\n    return commands;\n  }\n\n  parse(str: string, commands: SVGCommand[] = []) {\n    const finishCommand = (command: SVGCommand) => {\n      commands.push(command);\n      this.curArgs.length = 0;\n      this.canParseCommandOrComma = true;\n    };\n\n    for (let i = 0; i < str.length; i++) {\n      const c = str[i];\n      // White spaces parsing\n      const isAArcFlag = this.curCommandType === SVGPathData.ARC &&\n        (this.curArgs.length === 3 || this.curArgs.length === 4) &&\n        this.curNumber.length === 1 &&\n        (this.curNumber === \"0\" || this.curNumber === \"1\");\n      const isEndingDigit = isDigit(c) && (\n        (this.curNumber === \"0\" && c === \"0\") ||\n        isAArcFlag\n      );\n\n      if (\n        isDigit(c) &&\n        !isEndingDigit\n      ) {\n        this.curNumber += c;\n        this.curNumberHasExpDigits = this.curNumberHasExp;\n        continue;\n      }\n      if (\"e\" === c || \"E\" === c) {\n        this.curNumber += c;\n        this.curNumberHasExp = true;\n        continue;\n      }\n      if (\n        (\"-\" === c || \"+\" === c) &&\n        this.curNumberHasExp &&\n        !this.curNumberHasExpDigits\n      ) {\n        this.curNumber += c;\n        continue;\n      }\n      // if we already have a \".\", it means we are starting a new number\n      if (\".\" === c && !this.curNumberHasExp && !this.curNumberHasDecimal && !isAArcFlag) {\n        this.curNumber += c;\n        this.curNumberHasDecimal = true;\n        continue;\n      }\n\n      // New number\n      if (this.curNumber && -1 !== this.curCommandType) {\n        const val = Number(this.curNumber);\n        if (isNaN(val)) {\n          throw new SyntaxError(`Invalid number ending at ${i}`);\n        }\n        if (this.curCommandType === SVGPathData.ARC) {\n          if (0 === this.curArgs.length || 1 === this.curArgs.length) {\n            if (0 > val) {\n              throw new SyntaxError(\n                `Expected positive number, got \"${val}\" at index \"${i}\"`,\n              );\n            }\n          } else if (3 === this.curArgs.length || 4 === this.curArgs.length) {\n            if (\"0\" !== this.curNumber && \"1\" !== this.curNumber) {\n              throw new SyntaxError(\n                `Expected a flag, got \"${this.curNumber}\" at index \"${i}\"`,\n              );\n            }\n          }\n        }\n        this.curArgs.push(val);\n        if (this.curArgs.length === COMMAND_ARG_COUNTS[this.curCommandType]) {\n          if (SVGPathData.HORIZ_LINE_TO === this.curCommandType) {\n            finishCommand({\n              type: SVGPathData.HORIZ_LINE_TO,\n              relative: this.curCommandRelative,\n              x: val,\n            });\n          } else if (SVGPathData.VERT_LINE_TO === this.curCommandType) {\n            finishCommand({\n              type: SVGPathData.VERT_LINE_TO,\n              relative: this.curCommandRelative,\n              y: val,\n            });\n            // Move to / line to / smooth quadratic curve to commands (x, y)\n          } else if (\n            this.curCommandType === SVGPathData.MOVE_TO ||\n            this.curCommandType === SVGPathData.LINE_TO ||\n            this.curCommandType === SVGPathData.SMOOTH_QUAD_TO\n          ) {\n            finishCommand({\n              type: this.curCommandType,\n              relative: this.curCommandRelative,\n              x: this.curArgs[0],\n              y: this.curArgs[1],\n            } as SVGCommand);\n            // Switch to line to state\n            if (SVGPathData.MOVE_TO === this.curCommandType) {\n              this.curCommandType = SVGPathData.LINE_TO;\n            }\n          } else if (this.curCommandType === SVGPathData.CURVE_TO) {\n            finishCommand({\n              type: SVGPathData.CURVE_TO,\n              relative: this.curCommandRelative,\n              x1: this.curArgs[0],\n              y1: this.curArgs[1],\n              x2: this.curArgs[2],\n              y2: this.curArgs[3],\n              x: this.curArgs[4],\n              y: this.curArgs[5],\n            });\n          } else if (this.curCommandType === SVGPathData.SMOOTH_CURVE_TO) {\n            finishCommand({\n              type: SVGPathData.SMOOTH_CURVE_TO,\n              relative: this.curCommandRelative,\n              x2: this.curArgs[0],\n              y2: this.curArgs[1],\n              x: this.curArgs[2],\n              y: this.curArgs[3],\n            });\n          } else if (this.curCommandType === SVGPathData.QUAD_TO) {\n            finishCommand({\n              type: SVGPathData.QUAD_TO,\n              relative: this.curCommandRelative,\n              x1: this.curArgs[0],\n              y1: this.curArgs[1],\n              x: this.curArgs[2],\n              y: this.curArgs[3],\n            });\n          } else if (this.curCommandType === SVGPathData.ARC) {\n            finishCommand({\n              type: SVGPathData.ARC,\n              relative: this.curCommandRelative,\n              rX: this.curArgs[0],\n              rY: this.curArgs[1],\n              xRot: this.curArgs[2],\n              lArcFlag: this.curArgs[3] as 0 | 1,\n              sweepFlag: this.curArgs[4] as 0 | 1,\n              x: this.curArgs[5],\n              y: this.curArgs[6],\n            });\n          }\n        }\n        this.curNumber = \"\";\n        this.curNumberHasExpDigits = false;\n        this.curNumberHasExp = false;\n        this.curNumberHasDecimal = false;\n        this.canParseCommandOrComma = true;\n      }\n      // Continue if a white space or a comma was detected\n      if (isWhiteSpace(c)) {\n        continue;\n      }\n      if (\",\" === c && this.canParseCommandOrComma) {\n        // L 0,0, H is not valid:\n        this.canParseCommandOrComma = false;\n        continue;\n      }\n      // if a sign is detected, then parse the new number\n      if (\"+\" === c || \"-\" === c || \".\" === c) {\n        this.curNumber = c;\n        this.curNumberHasDecimal = \".\" === c;\n        continue;\n      }\n      // if a 0 is detected, then parse the new number\n      if (isEndingDigit) {\n        this.curNumber = c;\n        this.curNumberHasDecimal = false;\n        continue;\n      }\n\n      // Adding residual command\n      if (0 !== this.curArgs.length) {\n        throw new SyntaxError(`Unterminated command at index ${i}.`);\n      }\n      if (!this.canParseCommandOrComma) {\n        throw new SyntaxError(\n          `Unexpected character \"${c}\" at index ${i}. Command cannot follow comma`,\n        );\n      }\n      this.canParseCommandOrComma = false;\n      // Detecting the next command\n      if (\"z\" === c || \"Z\" === c) {\n        commands.push({\n          type: SVGPathData.CLOSE_PATH,\n        });\n        this.canParseCommandOrComma = true;\n        this.curCommandType = -1;\n        continue;\n        // Horizontal move to command\n      } else if (\"h\" === c || \"H\" === c) {\n        this.curCommandType = SVGPathData.HORIZ_LINE_TO;\n        this.curCommandRelative = \"h\" === c;\n        // Vertical move to command\n      } else if (\"v\" === c || \"V\" === c) {\n        this.curCommandType = SVGPathData.VERT_LINE_TO;\n        this.curCommandRelative = \"v\" === c;\n        // Move to command\n      } else if (\"m\" === c || \"M\" === c) {\n        this.curCommandType = SVGPathData.MOVE_TO;\n        this.curCommandRelative = \"m\" === c;\n        // Line to command\n      } else if (\"l\" === c || \"L\" === c) {\n        this.curCommandType = SVGPathData.LINE_TO;\n        this.curCommandRelative = \"l\" === c;\n        // Curve to command\n      } else if (\"c\" === c || \"C\" === c) {\n        this.curCommandType = SVGPathData.CURVE_TO;\n        this.curCommandRelative = \"c\" === c;\n        // Smooth curve to command\n      } else if (\"s\" === c || \"S\" === c) {\n        this.curCommandType = SVGPathData.SMOOTH_CURVE_TO;\n        this.curCommandRelative = \"s\" === c;\n        // Quadratic bezier curve to command\n      } else if (\"q\" === c || \"Q\" === c) {\n        this.curCommandType = SVGPathData.QUAD_TO;\n        this.curCommandRelative = \"q\" === c;\n        // Smooth quadratic bezier curve to command\n      } else if (\"t\" === c || \"T\" === c) {\n        this.curCommandType = SVGPathData.SMOOTH_QUAD_TO;\n        this.curCommandRelative = \"t\" === c;\n        // Elliptic arc command\n      } else if (\"a\" === c || \"A\" === c) {\n        this.curCommandType = SVGPathData.ARC;\n        this.curCommandRelative = \"a\" === c;\n      } else {\n        throw new SyntaxError(`Unexpected character \"${c}\" at index ${i}.`);\n      }\n    }\n    return commands;\n  }\n  /**\n   * Return a wrapper around this parser which applies the transformation on parsed commands.\n   */\n  transform(transform: TransformFunction) {\n    const result = Object.create(this, {\n      parse: {\n        value(chunk: string, commands: SVGCommand[] = []) {\n          const parsedCommands = Object.getPrototypeOf(this).parse.call(\n            this,\n            chunk,\n          );\n          for (const c of parsedCommands) {\n            const cT = transform(c);\n            if (Array.isArray(cT)) {\n              commands.push(...cT);\n            } else {\n              commands.push(cT);\n            }\n          }\n          return commands;\n        },\n      },\n    });\n    return result as this;\n  }\n}\n", "import { encodeSV<PERSON>ath } from \"./SVGPathDataEncoder\";\nimport { SVGPathDataParser } from \"./SVGPathDataParser\";\nimport { SVGPathDataTransformer } from \"./SVGPathDataTransformer\";\nimport { TransformableSVG } from \"./TransformableSVG\";\nimport { SVGCommand } from \"./types\";\n\nexport class SVGPathData extends TransformableSVG {\n  commands: SVGCommand[];\n  constructor(content: string | SVGCommand[]) {\n    super();\n    if (\"string\" === typeof content) {\n      this.commands = SVGPathData.parse(content);\n    } else {\n      this.commands = content;\n    }\n  }\n\n  encode() {\n    return SVGPathData.encode(this.commands);\n  }\n\n  getBounds() {\n    const boundsTransform = SVGPathDataTransformer.CALCULATE_BOUNDS();\n\n    this.transform(boundsTransform);\n    return boundsTransform;\n  }\n\n  transform(\n    transformFunction: (input: SVGCommand) => SVGCommand | SVGCommand[],\n  ) {\n    const newCommands = [];\n\n    for (const command of this.commands) {\n      const transformedCommand = transformFunction(command);\n\n      if (Array.isArray(transformedCommand)) {\n        newCommands.push(...transformedCommand);\n      } else {\n        newCommands.push(transformedCommand);\n      }\n    }\n    this.commands = newCommands;\n    return this;\n  }\n\n  static encode(commands: SVGCommand[]) {\n    return encodeSVGPath(commands);\n      }\n\n  static parse(path: string) {\n    const parser = new SVGPathDataParser();\n    const commands: SVGCommand[] = [];\n    parser.parse(path, commands);\n    parser.finish(commands);\n    return commands;\n  }\n\n  static readonly CLOSE_PATH: 1 = 1;\n  static readonly MOVE_TO: 2 = 2;\n  static readonly HORIZ_LINE_TO: 4 = 4;\n  static readonly VERT_LINE_TO: 8 = 8;\n  static readonly LINE_TO: 16 = 16;\n  static readonly CURVE_TO: 32 = 32;\n  static readonly SMOOTH_CURVE_TO: 64 = 64;\n  static readonly QUAD_TO: 128 = 128;\n  static readonly SMOOTH_QUAD_TO: 256 = 256;\n  static readonly ARC: 512 = 512;\n  static readonly LINE_COMMANDS = SVGPathData.LINE_TO | SVGPathData.HORIZ_LINE_TO | SVGPathData.VERT_LINE_TO;\n  static readonly DRAWING_COMMANDS = SVGPathData.HORIZ_LINE_TO | SVGPathData.VERT_LINE_TO | SVGPathData.LINE_TO |\n  SVGPathData.CURVE_TO | SVGPathData.SMOOTH_CURVE_TO | SVGPathData.QUAD_TO |\n  SVGPathData.SMOOTH_QUAD_TO | SVGPathData.ARC;\n}\n\nexport const COMMAND_ARG_COUNTS = {\n    [SVGPathData.MOVE_TO]: 2,\n    [SVGPathData.LINE_TO]: 2,\n    [SVGPathData.HORIZ_LINE_TO]: 1,\n    [SVGPathData.VERT_LINE_TO]: 1,\n    [SVGPathData.CLOSE_PATH]: 0,\n    [SVGPathData.QUAD_TO]: 4,\n    [SVGPathData.SMOOTH_QUAD_TO]: 2,\n    [SVGPathData.CURVE_TO]: 6,\n    [SVGPathData.SMOOTH_CURVE_TO]: 4,\n    [SVGPathData.ARC]: 7,\n};\n\nexport {encodeSVGPath} from \"./SVGPathDataEncoder\";\nexport {SVGPathDataParser} from \"./SVGPathDataParser\";\nexport {SVGPathDataTransformer} from \"./SVGPathDataTransformer\";\n", "function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\n/* eslint-disable no-bitwise -- used for calculations */\n\n/* eslint-disable unicorn/prefer-query-selector -- aiming at\n  backward-compatibility */\n\n/**\n* StackBlur - a fast almost Gaussian Blur For Canvas\n*\n* In case you find this class useful - especially in commercial projects -\n* I am not totally unhappy for a small donation to my PayPal account\n* <EMAIL>\n*\n* Or support me on flattr:\n* {@link https://flattr.com/thing/72791/StackBlur-a-fast-almost-Gaussian-Blur-Effect-for-CanvasJavascript}.\n*\n* @module StackBlur\n* <AUTHOR> Klingemann\n* Contact: <EMAIL>\n* Website: {@link http://www.quasimondo.com/StackBlurForCanvas/StackBlurDemo.html}\n* Twitter: @quasimondo\n*\n* @copyright (c) 2010 Mario Klingemann\n*\n* Permission is hereby granted, free of charge, to any person\n* obtaining a copy of this software and associated documentation\n* files (the \"Software\"), to deal in the Software without\n* restriction, including without limitation the rights to use,\n* copy, modify, merge, publish, distribute, sublicense, and/or sell\n* copies of the Software, and to permit persons to whom the\n* Software is furnished to do so, subject to the following\n* conditions:\n*\n* The above copyright notice and this permission notice shall be\n* included in all copies or substantial portions of the Software.\n*\n* THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n* EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\n* OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n* NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\n* HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\n* WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n* FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\n* OTHER DEALINGS IN THE SOFTWARE.\n*/\nvar mulTable = [512, 512, 456, 512, 328, 456, 335, 512, 405, 328, 271, 456, 388, 335, 292, 512, 454, 405, 364, 328, 298, 271, 496, 456, 420, 388, 360, 335, 312, 292, 273, 512, 482, 454, 428, 405, 383, 364, 345, 328, 312, 298, 284, 271, 259, 496, 475, 456, 437, 420, 404, 388, 374, 360, 347, 335, 323, 312, 302, 292, 282, 273, 265, 512, 497, 482, 468, 454, 441, 428, 417, 405, 394, 383, 373, 364, 354, 345, 337, 328, 320, 312, 305, 298, 291, 284, 278, 271, 265, 259, 507, 496, 485, 475, 465, 456, 446, 437, 428, 420, 412, 404, 396, 388, 381, 374, 367, 360, 354, 347, 341, 335, 329, 323, 318, 312, 307, 302, 297, 292, 287, 282, 278, 273, 269, 265, 261, 512, 505, 497, 489, 482, 475, 468, 461, 454, 447, 441, 435, 428, 422, 417, 411, 405, 399, 394, 389, 383, 378, 373, 368, 364, 359, 354, 350, 345, 341, 337, 332, 328, 324, 320, 316, 312, 309, 305, 301, 298, 294, 291, 287, 284, 281, 278, 274, 271, 268, 265, 262, 259, 257, 507, 501, 496, 491, 485, 480, 475, 470, 465, 460, 456, 451, 446, 442, 437, 433, 428, 424, 420, 416, 412, 408, 404, 400, 396, 392, 388, 385, 381, 377, 374, 370, 367, 363, 360, 357, 354, 350, 347, 344, 341, 338, 335, 332, 329, 326, 323, 320, 318, 315, 312, 310, 307, 304, 302, 299, 297, 294, 292, 289, 287, 285, 282, 280, 278, 275, 273, 271, 269, 267, 265, 263, 261, 259];\nvar shgTable = [9, 11, 12, 13, 13, 14, 14, 15, 15, 15, 15, 16, 16, 16, 16, 17, 17, 17, 17, 17, 17, 17, 18, 18, 18, 18, 18, 18, 18, 18, 18, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24];\n/**\n * @param {string|HTMLImageElement} img\n * @param {string|HTMLCanvasElement} canvas\n * @param {Float} radius\n * @param {boolean} blurAlphaChannel\n * @param {boolean} useOffset\n * @param {boolean} skipStyles\n * @returns {undefined}\n */\n\nfunction processImage(img, canvas, radius, blurAlphaChannel, useOffset, skipStyles) {\n  if (typeof img === 'string') {\n    img = document.getElementById(img);\n  }\n\n  if (!img || Object.prototype.toString.call(img).slice(8, -1) === 'HTMLImageElement' && !('naturalWidth' in img)) {\n    return;\n  }\n\n  var dimensionType = useOffset ? 'offset' : 'natural';\n  var w = img[dimensionType + 'Width'];\n  var h = img[dimensionType + 'Height']; // add ImageBitmap support,can blur texture source\n\n  if (Object.prototype.toString.call(img).slice(8, -1) === 'ImageBitmap') {\n    w = img.width;\n    h = img.height;\n  }\n\n  if (typeof canvas === 'string') {\n    canvas = document.getElementById(canvas);\n  }\n\n  if (!canvas || !('getContext' in canvas)) {\n    return;\n  }\n\n  if (!skipStyles) {\n    canvas.style.width = w + 'px';\n    canvas.style.height = h + 'px';\n  }\n\n  canvas.width = w;\n  canvas.height = h;\n  var context = canvas.getContext('2d');\n  context.clearRect(0, 0, w, h);\n  context.drawImage(img, 0, 0, img.naturalWidth, img.naturalHeight, 0, 0, w, h);\n\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n\n  if (blurAlphaChannel) {\n    processCanvasRGBA(canvas, 0, 0, w, h, radius);\n  } else {\n    processCanvasRGB(canvas, 0, 0, w, h, radius);\n  }\n}\n/**\n * @param {string|HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @throws {Error|TypeError}\n * @returns {ImageData} See {@link https://html.spec.whatwg.org/multipage/canvas.html#imagedata}\n */\n\n\nfunction getImageDataFromCanvas(canvas, topX, topY, width, height) {\n  if (typeof canvas === 'string') {\n    canvas = document.getElementById(canvas);\n  }\n\n  if (!canvas || _typeof(canvas) !== 'object' || !('getContext' in canvas)) {\n    throw new TypeError('Expecting canvas with `getContext` method ' + 'in processCanvasRGB(A) calls!');\n  }\n\n  var context = canvas.getContext('2d');\n\n  try {\n    return context.getImageData(topX, topY, width, height);\n  } catch (e) {\n    throw new Error('unable to access image data: ' + e);\n  }\n}\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {undefined}\n */\n\n\nfunction processCanvasRGBA(canvas, topX, topY, width, height, radius) {\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n\n  radius |= 0;\n  var imageData = getImageDataFromCanvas(canvas, topX, topY, width, height);\n  imageData = processImageDataRGBA(imageData, topX, topY, width, height, radius);\n  canvas.getContext('2d').putImageData(imageData, topX, topY);\n}\n/**\n * @param {ImageData} imageData\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {ImageData}\n */\n\n\nfunction processImageDataRGBA(imageData, topX, topY, width, height, radius) {\n  var pixels = imageData.data;\n  var div = 2 * radius + 1; // const w4 = width << 2;\n\n  var widthMinus1 = width - 1;\n  var heightMinus1 = height - 1;\n  var radiusPlus1 = radius + 1;\n  var sumFactor = radiusPlus1 * (radiusPlus1 + 1) / 2;\n  var stackStart = new BlurStack();\n  var stack = stackStart;\n  var stackEnd;\n\n  for (var i = 1; i < div; i++) {\n    stack = stack.next = new BlurStack();\n\n    if (i === radiusPlus1) {\n      stackEnd = stack;\n    }\n  }\n\n  stack.next = stackStart;\n  var stackIn = null,\n      stackOut = null,\n      yw = 0,\n      yi = 0;\n  var mulSum = mulTable[radius];\n  var shgSum = shgTable[radius];\n\n  for (var y = 0; y < height; y++) {\n    stack = stackStart;\n    var pr = pixels[yi],\n        pg = pixels[yi + 1],\n        pb = pixels[yi + 2],\n        pa = pixels[yi + 3];\n\n    for (var _i = 0; _i < radiusPlus1; _i++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack.a = pa;\n      stack = stack.next;\n    }\n\n    var rInSum = 0,\n        gInSum = 0,\n        bInSum = 0,\n        aInSum = 0,\n        rOutSum = radiusPlus1 * pr,\n        gOutSum = radiusPlus1 * pg,\n        bOutSum = radiusPlus1 * pb,\n        aOutSum = radiusPlus1 * pa,\n        rSum = sumFactor * pr,\n        gSum = sumFactor * pg,\n        bSum = sumFactor * pb,\n        aSum = sumFactor * pa;\n\n    for (var _i2 = 1; _i2 < radiusPlus1; _i2++) {\n      var p = yi + ((widthMinus1 < _i2 ? widthMinus1 : _i2) << 2);\n      var r = pixels[p],\n          g = pixels[p + 1],\n          b = pixels[p + 2],\n          a = pixels[p + 3];\n      var rbs = radiusPlus1 - _i2;\n      rSum += (stack.r = r) * rbs;\n      gSum += (stack.g = g) * rbs;\n      bSum += (stack.b = b) * rbs;\n      aSum += (stack.a = a) * rbs;\n      rInSum += r;\n      gInSum += g;\n      bInSum += b;\n      aInSum += a;\n      stack = stack.next;\n    }\n\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var x = 0; x < width; x++) {\n      var paInitial = aSum * mulSum >>> shgSum;\n      pixels[yi + 3] = paInitial;\n\n      if (paInitial !== 0) {\n        var _a2 = 255 / paInitial;\n\n        pixels[yi] = (rSum * mulSum >>> shgSum) * _a2;\n        pixels[yi + 1] = (gSum * mulSum >>> shgSum) * _a2;\n        pixels[yi + 2] = (bSum * mulSum >>> shgSum) * _a2;\n      } else {\n        pixels[yi] = pixels[yi + 1] = pixels[yi + 2] = 0;\n      }\n\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n      aSum -= aOutSum;\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n      aOutSum -= stackIn.a;\n\n      var _p = x + radius + 1;\n\n      _p = yw + (_p < widthMinus1 ? _p : widthMinus1) << 2;\n      rInSum += stackIn.r = pixels[_p];\n      gInSum += stackIn.g = pixels[_p + 1];\n      bInSum += stackIn.b = pixels[_p + 2];\n      aInSum += stackIn.a = pixels[_p + 3];\n      rSum += rInSum;\n      gSum += gInSum;\n      bSum += bInSum;\n      aSum += aInSum;\n      stackIn = stackIn.next;\n      var _stackOut = stackOut,\n          _r = _stackOut.r,\n          _g = _stackOut.g,\n          _b = _stackOut.b,\n          _a = _stackOut.a;\n      rOutSum += _r;\n      gOutSum += _g;\n      bOutSum += _b;\n      aOutSum += _a;\n      rInSum -= _r;\n      gInSum -= _g;\n      bInSum -= _b;\n      aInSum -= _a;\n      stackOut = stackOut.next;\n      yi += 4;\n    }\n\n    yw += width;\n  }\n\n  for (var _x = 0; _x < width; _x++) {\n    yi = _x << 2;\n\n    var _pr = pixels[yi],\n        _pg = pixels[yi + 1],\n        _pb = pixels[yi + 2],\n        _pa = pixels[yi + 3],\n        _rOutSum = radiusPlus1 * _pr,\n        _gOutSum = radiusPlus1 * _pg,\n        _bOutSum = radiusPlus1 * _pb,\n        _aOutSum = radiusPlus1 * _pa,\n        _rSum = sumFactor * _pr,\n        _gSum = sumFactor * _pg,\n        _bSum = sumFactor * _pb,\n        _aSum = sumFactor * _pa;\n\n    stack = stackStart;\n\n    for (var _i3 = 0; _i3 < radiusPlus1; _i3++) {\n      stack.r = _pr;\n      stack.g = _pg;\n      stack.b = _pb;\n      stack.a = _pa;\n      stack = stack.next;\n    }\n\n    var yp = width;\n    var _gInSum = 0,\n        _bInSum = 0,\n        _aInSum = 0,\n        _rInSum = 0;\n\n    for (var _i4 = 1; _i4 <= radius; _i4++) {\n      yi = yp + _x << 2;\n\n      var _rbs = radiusPlus1 - _i4;\n\n      _rSum += (stack.r = _pr = pixels[yi]) * _rbs;\n      _gSum += (stack.g = _pg = pixels[yi + 1]) * _rbs;\n      _bSum += (stack.b = _pb = pixels[yi + 2]) * _rbs;\n      _aSum += (stack.a = _pa = pixels[yi + 3]) * _rbs;\n      _rInSum += _pr;\n      _gInSum += _pg;\n      _bInSum += _pb;\n      _aInSum += _pa;\n      stack = stack.next;\n\n      if (_i4 < heightMinus1) {\n        yp += width;\n      }\n    }\n\n    yi = _x;\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var _y = 0; _y < height; _y++) {\n      var _p2 = yi << 2;\n\n      pixels[_p2 + 3] = _pa = _aSum * mulSum >>> shgSum;\n\n      if (_pa > 0) {\n        _pa = 255 / _pa;\n        pixels[_p2] = (_rSum * mulSum >>> shgSum) * _pa;\n        pixels[_p2 + 1] = (_gSum * mulSum >>> shgSum) * _pa;\n        pixels[_p2 + 2] = (_bSum * mulSum >>> shgSum) * _pa;\n      } else {\n        pixels[_p2] = pixels[_p2 + 1] = pixels[_p2 + 2] = 0;\n      }\n\n      _rSum -= _rOutSum;\n      _gSum -= _gOutSum;\n      _bSum -= _bOutSum;\n      _aSum -= _aOutSum;\n      _rOutSum -= stackIn.r;\n      _gOutSum -= stackIn.g;\n      _bOutSum -= stackIn.b;\n      _aOutSum -= stackIn.a;\n      _p2 = _x + ((_p2 = _y + radiusPlus1) < heightMinus1 ? _p2 : heightMinus1) * width << 2;\n      _rSum += _rInSum += stackIn.r = pixels[_p2];\n      _gSum += _gInSum += stackIn.g = pixels[_p2 + 1];\n      _bSum += _bInSum += stackIn.b = pixels[_p2 + 2];\n      _aSum += _aInSum += stackIn.a = pixels[_p2 + 3];\n      stackIn = stackIn.next;\n      _rOutSum += _pr = stackOut.r;\n      _gOutSum += _pg = stackOut.g;\n      _bOutSum += _pb = stackOut.b;\n      _aOutSum += _pa = stackOut.a;\n      _rInSum -= _pr;\n      _gInSum -= _pg;\n      _bInSum -= _pb;\n      _aInSum -= _pa;\n      stackOut = stackOut.next;\n      yi += width;\n    }\n  }\n\n  return imageData;\n}\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {undefined}\n */\n\n\nfunction processCanvasRGB(canvas, topX, topY, width, height, radius) {\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n\n  radius |= 0;\n  var imageData = getImageDataFromCanvas(canvas, topX, topY, width, height);\n  imageData = processImageDataRGB(imageData, topX, topY, width, height, radius);\n  canvas.getContext('2d').putImageData(imageData, topX, topY);\n}\n/**\n * @param {ImageData} imageData\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {ImageData}\n */\n\n\nfunction processImageDataRGB(imageData, topX, topY, width, height, radius) {\n  var pixels = imageData.data;\n  var div = 2 * radius + 1; // const w4 = width << 2;\n\n  var widthMinus1 = width - 1;\n  var heightMinus1 = height - 1;\n  var radiusPlus1 = radius + 1;\n  var sumFactor = radiusPlus1 * (radiusPlus1 + 1) / 2;\n  var stackStart = new BlurStack();\n  var stack = stackStart;\n  var stackEnd;\n\n  for (var i = 1; i < div; i++) {\n    stack = stack.next = new BlurStack();\n\n    if (i === radiusPlus1) {\n      stackEnd = stack;\n    }\n  }\n\n  stack.next = stackStart;\n  var stackIn = null;\n  var stackOut = null;\n  var mulSum = mulTable[radius];\n  var shgSum = shgTable[radius];\n  var p, rbs;\n  var yw = 0,\n      yi = 0;\n\n  for (var y = 0; y < height; y++) {\n    var pr = pixels[yi],\n        pg = pixels[yi + 1],\n        pb = pixels[yi + 2],\n        rOutSum = radiusPlus1 * pr,\n        gOutSum = radiusPlus1 * pg,\n        bOutSum = radiusPlus1 * pb,\n        rSum = sumFactor * pr,\n        gSum = sumFactor * pg,\n        bSum = sumFactor * pb;\n    stack = stackStart;\n\n    for (var _i5 = 0; _i5 < radiusPlus1; _i5++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack = stack.next;\n    }\n\n    var rInSum = 0,\n        gInSum = 0,\n        bInSum = 0;\n\n    for (var _i6 = 1; _i6 < radiusPlus1; _i6++) {\n      p = yi + ((widthMinus1 < _i6 ? widthMinus1 : _i6) << 2);\n      rSum += (stack.r = pr = pixels[p]) * (rbs = radiusPlus1 - _i6);\n      gSum += (stack.g = pg = pixels[p + 1]) * rbs;\n      bSum += (stack.b = pb = pixels[p + 2]) * rbs;\n      rInSum += pr;\n      gInSum += pg;\n      bInSum += pb;\n      stack = stack.next;\n    }\n\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var x = 0; x < width; x++) {\n      pixels[yi] = rSum * mulSum >>> shgSum;\n      pixels[yi + 1] = gSum * mulSum >>> shgSum;\n      pixels[yi + 2] = bSum * mulSum >>> shgSum;\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n      p = yw + ((p = x + radius + 1) < widthMinus1 ? p : widthMinus1) << 2;\n      rInSum += stackIn.r = pixels[p];\n      gInSum += stackIn.g = pixels[p + 1];\n      bInSum += stackIn.b = pixels[p + 2];\n      rSum += rInSum;\n      gSum += gInSum;\n      bSum += bInSum;\n      stackIn = stackIn.next;\n      rOutSum += pr = stackOut.r;\n      gOutSum += pg = stackOut.g;\n      bOutSum += pb = stackOut.b;\n      rInSum -= pr;\n      gInSum -= pg;\n      bInSum -= pb;\n      stackOut = stackOut.next;\n      yi += 4;\n    }\n\n    yw += width;\n  }\n\n  for (var _x2 = 0; _x2 < width; _x2++) {\n    yi = _x2 << 2;\n\n    var _pr2 = pixels[yi],\n        _pg2 = pixels[yi + 1],\n        _pb2 = pixels[yi + 2],\n        _rOutSum2 = radiusPlus1 * _pr2,\n        _gOutSum2 = radiusPlus1 * _pg2,\n        _bOutSum2 = radiusPlus1 * _pb2,\n        _rSum2 = sumFactor * _pr2,\n        _gSum2 = sumFactor * _pg2,\n        _bSum2 = sumFactor * _pb2;\n\n    stack = stackStart;\n\n    for (var _i7 = 0; _i7 < radiusPlus1; _i7++) {\n      stack.r = _pr2;\n      stack.g = _pg2;\n      stack.b = _pb2;\n      stack = stack.next;\n    }\n\n    var _rInSum2 = 0,\n        _gInSum2 = 0,\n        _bInSum2 = 0;\n\n    for (var _i8 = 1, yp = width; _i8 <= radius; _i8++) {\n      yi = yp + _x2 << 2;\n      _rSum2 += (stack.r = _pr2 = pixels[yi]) * (rbs = radiusPlus1 - _i8);\n      _gSum2 += (stack.g = _pg2 = pixels[yi + 1]) * rbs;\n      _bSum2 += (stack.b = _pb2 = pixels[yi + 2]) * rbs;\n      _rInSum2 += _pr2;\n      _gInSum2 += _pg2;\n      _bInSum2 += _pb2;\n      stack = stack.next;\n\n      if (_i8 < heightMinus1) {\n        yp += width;\n      }\n    }\n\n    yi = _x2;\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var _y2 = 0; _y2 < height; _y2++) {\n      p = yi << 2;\n      pixels[p] = _rSum2 * mulSum >>> shgSum;\n      pixels[p + 1] = _gSum2 * mulSum >>> shgSum;\n      pixels[p + 2] = _bSum2 * mulSum >>> shgSum;\n      _rSum2 -= _rOutSum2;\n      _gSum2 -= _gOutSum2;\n      _bSum2 -= _bOutSum2;\n      _rOutSum2 -= stackIn.r;\n      _gOutSum2 -= stackIn.g;\n      _bOutSum2 -= stackIn.b;\n      p = _x2 + ((p = _y2 + radiusPlus1) < heightMinus1 ? p : heightMinus1) * width << 2;\n      _rSum2 += _rInSum2 += stackIn.r = pixels[p];\n      _gSum2 += _gInSum2 += stackIn.g = pixels[p + 1];\n      _bSum2 += _bInSum2 += stackIn.b = pixels[p + 2];\n      stackIn = stackIn.next;\n      _rOutSum2 += _pr2 = stackOut.r;\n      _gOutSum2 += _pg2 = stackOut.g;\n      _bOutSum2 += _pb2 = stackOut.b;\n      _rInSum2 -= _pr2;\n      _gInSum2 -= _pg2;\n      _bInSum2 -= _pb2;\n      stackOut = stackOut.next;\n      yi += width;\n    }\n  }\n\n  return imageData;\n}\n/**\n *\n */\n\n\nvar BlurStack =\n/**\n * Set properties.\n */\nfunction BlurStack() {\n  _classCallCheck(this, BlurStack);\n\n  this.r = 0;\n  this.g = 0;\n  this.b = 0;\n  this.a = 0;\n  this.next = null;\n};\n\nexport { BlurStack, processCanvasRGB as canvasRGB, processCanvasRGBA as canvasRGBA, processImage as image, processImageDataRGB as imageDataRGB, processImageDataRGBA as imageDataRGBA };\n"], "names": ["now", "require", "root", "window", "global", "vendors", "suffix", "raf", "caf", "i", "length", "last", "id", "queue", "frameDuration", "callback", "_now", "next", "Math", "max", "setTimeout", "cp", "slice", "cancelled", "e", "round", "push", "handle", "module", "exports", "fn", "call", "cancel", "apply", "arguments", "polyfill", "object", "requestAnimationFrame", "cancelAnimationFrame", "color_string", "this", "ok", "alpha", "char<PERSON>t", "substr", "replace", "toLowerCase", "simple_colors", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "dodgerblue", "feldspar", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "<PERSON><PERSON>rey", "lightgreen", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslateblue", "lightslategray", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "violetred", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "color_defs", "re", "example", "process", "bits", "parseInt", "parseFloat", "processor", "exec", "channels", "r", "g", "b", "isNaN", "toRGB", "toRGBA", "toHex", "toString", "getHelpXML", "examples", "Array", "j", "sc", "xml", "document", "createElement", "setAttribute", "list_item", "list_color", "RGBColor", "example_div", "style", "cssText", "append<PERSON><PERSON><PERSON>", "createTextNode", "list_item_value", "getNanoSeconds", "hrtime", "loadTime", "moduleLoadTime", "nodeLoadTime", "upTime", "performance", "hr", "uptime", "Date", "getTime", "asyncGeneratorStep", "n", "t", "o", "a", "c", "u", "value", "done", "Promise", "resolve", "then", "_asyncToGenerator", "_next", "_throw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Symbol", "toPrimitive", "TypeError", "String", "Number", "_defineProperty", "Object", "defineProperty", "enumerable", "configurable", "writable", "setPrototypeOf", "__proto__", "prototype", "hasOwnProperty", "constructor", "create", "cos", "sin", "Error", "PI", "lArcFlag", "sweepFlag", "rX", "rY", "s", "x", "y", "abs", "h", "xRot", "p", "pow", "sqrt", "m", "O", "l", "T", "v", "cX", "cY", "phi1", "atan2", "phi2", "relative", "x1", "y1", "x2", "y2", "NaN", "type", "_", "SMOOTH_CURVE_TO", "CURVE_TO", "SMOOTH_QUAD_TO", "QUAD_TO", "MOVE_TO", "CLOSE_PATH", "HORIZ_LINE_TO", "LINE_TO", "VERT_LINE_TO", "f", "N", "d", "E", "A", "C", "M", "R", "I", "S", "L", "ROUND", "TO_ABS", "TO_REL", "NORMALIZE_HVZ", "ARC", "NORMALIZE_ST", "QT_TO_C", "INFO", "SANITIZE", "LINE_COMMANDS", "MATRIX", "ROTATE", "TRANSLATE", "SCALE", "SKEW_X", "atan", "SKEW_Y", "X_AXIS_SYMMETRY", "Y_AXIS_SYMMETRY", "A_TO_C", "min", "ceil", "H", "ANNOTATE_ARCS", "CLONE", "CALCULATE_BOUNDS", "maxX", "minX", "maxY", "minY", "DRAWING_COMMANDS", "w", "map", "U", "transform", "toAbs", "toRel", "normalizeHVZ", "normalizeST", "qtToC", "aToC", "sanitize", "translate", "scale", "rotate", "matrix", "skewX", "skewY", "xSymmetry", "ySymmetry", "annotateArcs", "charCodeAt", "curN<PERSON>ber", "curCommandType", "curCommandRelative", "canParseCommandOrComma", "curNumberHasExp", "curNumberHasExpDigits", "curNumberHasDecimal", "curArgs", "finish", "parse", "SyntaxError", "getPrototypeOf", "isArray", "commands", "encode", "getBounds", "_typeof", "obj", "iterator", "mulTable", "shgTable", "getImageDataFromCanvas", "canvas", "topX", "topY", "width", "height", "getElementById", "context", "getContext", "getImageData", "processCanvasRGBA", "radius", "imageData", "stackEnd", "pixels", "data", "div", "widthMinus1", "heightMinus1", "radiusPlus1", "sumFactor", "stackStart", "BlurStack", "stack", "stackIn", "stackOut", "yw", "yi", "mulSum", "shgSum", "pr", "pg", "pb", "pa", "_i", "rInSum", "gInSum", "bInSum", "aInSum", "rOutSum", "gOutSum", "bOutSum", "aOutSum", "rSum", "gSum", "bSum", "aSum", "_i2", "rbs", "paInitial", "_a2", "_p", "_stackOut", "_r", "_g", "_b", "_a", "_x", "_pr", "_pg", "_pb", "_pa", "_rOutSum", "_gOutSum", "_bOutSum", "_aOutSum", "_rSum", "_gSum", "_bSum", "_aSum", "_i3", "yp", "_gInSum", "_bInSum", "_aInSum", "_rInSum", "_i4", "_rbs", "_y", "_p2", "processImageDataRGBA", "putImageData", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_classCallCheck"], "sourceRoot": ""}