import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Divider,
  Alert,
  Paper,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip
} from '@mui/material';
import {
  Save as SaveIcon,
  Settings as SettingsIcon,
  People as PeopleIcon,
  Security as SecurityIcon,
  Backup as BackupIcon,
  Notifications as NotificationsIcon,
  Palette as PaletteIcon,
  Language as LanguageIcon,
  Download as DownloadIcon,
  Upload as UploadIcon,
  Delete as DeleteIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import UserManagement from '../users/UserManagement';
import { useUser } from '../../contexts/UserContext';

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{
          p: { xs: 1, sm: 2, md: 3 },
          maxWidth: '100%',
          overflow: 'hidden'
        }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const NewSettingsPage = ({ fundSettings, setFundSettings }) => {
  const { hasPermission, PERMISSIONS } = useUser();
  const [activeTab, setActiveTab] = useState(0);
  const [localSettings, setLocalSettings] = useState(fundSettings);
  const [systemSettings, setSystemSettings] = useState({
    notifications: true,
    autoBackup: false,
    theme: 'light',
    language: 'ar',
    currency: 'SAR'
  });
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [backupDialog, setBackupDialog] = useState(false);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setLocalSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSystemSettingChange = (setting, value) => {
    setSystemSettings(prev => ({
      ...prev,
      [setting]: value
    }));
  };

  const handleSave = () => {
    setFundSettings(localSettings);
    setAlertMessage('تم حفظ الإعدادات بنجاح');
    setShowAlert(true);
    setTimeout(() => setShowAlert(false), 3000);
  };

  const handleExportData = () => {
    try {
      const data = {
        fundSettings: localSettings,
        systemSettings,
        exportDate: new Date().toISOString()
      };
      
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `savings-fund-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      setAlertMessage('تم تصدير البيانات بنجاح');
      setShowAlert(true);
      setTimeout(() => setShowAlert(false), 3000);
    } catch (error) {
      setAlertMessage('حدث خطأ أثناء تصدير البيانات');
      setShowAlert(true);
      setTimeout(() => setShowAlert(false), 3000);
    }
  };

  const handleImportData = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target.result);
          if (data.fundSettings) {
            setLocalSettings(data.fundSettings);
          }
          if (data.systemSettings) {
            setSystemSettings(data.systemSettings);
          }
          setAlertMessage('تم استيراد البيانات بنجاح');
          setShowAlert(true);
          setTimeout(() => setShowAlert(false), 3000);
        } catch (error) {
          setAlertMessage('حدث خطأ أثناء استيراد البيانات');
          setShowAlert(true);
          setTimeout(() => setShowAlert(false), 3000);
        }
      };
      reader.readAsText(file);
    }
  };

  const tabs = [
    { label: 'معلومات الصندوق', icon: <SettingsIcon />, permission: PERMISSIONS.MANAGE_SETTINGS },
    { label: 'إدارة المستخدمين', icon: <PeopleIcon />, permission: PERMISSIONS.MANAGE_USERS },
    { label: 'الأمان والصلاحيات', icon: <SecurityIcon />, permission: PERMISSIONS.MANAGE_SETTINGS },
    { label: 'النسخ الاحتياطي', icon: <BackupIcon />, permission: PERMISSIONS.BACKUP_RESTORE },
    { label: 'الإشعارات', icon: <NotificationsIcon />, permission: PERMISSIONS.MANAGE_SETTINGS },
    { label: 'المظهر واللغة', icon: <PaletteIcon />, permission: PERMISSIONS.MANAGE_SETTINGS },
    { label: 'معلومات النظام', icon: <InfoIcon />, permission: null }
  ];

  const visibleTabs = tabs.filter(tab => !tab.permission || hasPermission(tab.permission));

  return (
    <Box sx={{
      p: { xs: 1, sm: 2, md: 3 },
      maxWidth: '100%',
      overflow: 'hidden'
    }}>
      <Typography
        variant="h4"
        component="h1"
        gutterBottom
        fontWeight="bold"
        sx={{
          fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' },
          textAlign: { xs: 'center', sm: 'left' },
          mb: { xs: 2, sm: 3 }
        }}
      >
        إعدادات النظام
      </Typography>

      <Divider sx={{ mb: { xs: 2, sm: 3 } }} />

      {showAlert && (
        <Alert
          severity="success"
          sx={{
            mb: { xs: 2, sm: 3 },
            mx: { xs: 0, sm: 'auto' },
            maxWidth: '100%'
          }}
        >
          {alertMessage}
        </Alert>
      )}

      <Card sx={{
        boxShadow: { xs: 1, sm: 3 },
        borderRadius: { xs: 1, sm: 2 }
      }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            allowScrollButtonsMobile
            sx={{
              '& .MuiTab-root': {
                minHeight: { xs: 56, sm: 64, md: 72 },
                textTransform: 'none',
                fontSize: { xs: '0.875rem', sm: '0.95rem', md: '1rem' },
                fontWeight: 600,
                minWidth: { xs: 120, sm: 140, md: 160 },
                px: { xs: 1, sm: 2 },
                '& .MuiTab-iconWrapper': {
                  fontSize: { xs: '1.2rem', sm: '1.4rem' }
                }
              },
              '& .MuiTabs-scrollButtons': {
                '&.Mui-disabled': {
                  opacity: 0.3
                }
              },
              '& .MuiTabs-indicator': {
                height: 3
              }
            }}
          >
            {visibleTabs.map((tab, index) => (
              <Tab
                key={index}
                icon={tab.icon}
                label={tab.label}
                iconPosition="start"
                sx={{
                  gap: { xs: 0.5, sm: 1 },
                  flexDirection: { xs: 'column', sm: 'row' }
                }}
              />
            ))}
          </Tabs>
        </Box>

        {/* Tab 0: Fund Information */}
        <TabPanel value={activeTab} index={0}>
          <Grid container spacing={{ xs: 2, sm: 3 }}>
            <Grid item xs={12} lg={8}>
              <Typography
                variant="h6"
                gutterBottom
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  fontSize: { xs: '1.1rem', sm: '1.25rem' },
                  mb: { xs: 2, sm: 3 }
                }}
              >
                <SettingsIcon sx={{ fontSize: { xs: '1.2rem', sm: '1.5rem' } }} />
                معلومات الصندوق
              </Typography>

              <Grid container spacing={{ xs: 2, sm: 2.5 }}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="اسم الصندوق"
                    name="fundName"
                    value={localSettings.fundName}
                    onChange={handleInputChange}
                    variant="outlined"
                    size={window.innerWidth < 600 ? "small" : "medium"}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="اسم العائلة/المجموعة"
                    name="fundFamily"
                    value={localSettings.fundFamily}
                    onChange={handleInputChange}
                    variant="outlined"
                    size={window.innerWidth < 600 ? "small" : "medium"}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="المدير العام"
                    name="generalManager"
                    value={localSettings.generalManager}
                    onChange={handleInputChange}
                    variant="outlined"
                    size={window.innerWidth < 600 ? "small" : "medium"}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="أمين الصندوق"
                    name="treasurer"
                    value={localSettings.treasurer}
                    onChange={handleInputChange}
                    variant="outlined"
                    size={window.innerWidth < 600 ? "small" : "medium"}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="المحصل"
                    name="collector"
                    value={localSettings.collector}
                    onChange={handleInputChange}
                    variant="outlined"
                    size={window.innerWidth < 600 ? "small" : "medium"}
                  />
                </Grid>
              </Grid>

              <Box sx={{
                mt: { xs: 2, sm: 3 },
                display: 'flex',
                justifyContent: { xs: 'center', sm: 'flex-end' },
                width: '100%'
              }}>
                <Button
                  variant="contained"
                  startIcon={<SaveIcon />}
                  onClick={handleSave}
                  size={window.innerWidth < 600 ? "medium" : "large"}
                  sx={{
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                    },
                    minWidth: { xs: '140px', sm: 'auto' },
                    px: { xs: 3, sm: 4 }
                  }}
                >
                  حفظ الإعدادات
                </Button>
              </Box>
            </Grid>

            <Grid item xs={12} lg={4}>
              <Paper sx={{
                p: { xs: 2, sm: 2.5 },
                bgcolor: 'background.default',
                mt: { xs: 2, lg: 0 }
              }}>
                <Typography
                  variant="h6"
                  gutterBottom
                  sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}
                >
                  إرشادات
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  paragraph
                  sx={{ fontSize: { xs: '0.875rem', sm: '0.875rem' } }}
                >
                  • اسم الصندوق سيظهر في جميع التقارير والوثائق
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  paragraph
                  sx={{ fontSize: { xs: '0.875rem', sm: '0.875rem' } }}
                >
                  • معلومات المسؤولين ستظهر في التقارير الرسمية
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ fontSize: { xs: '0.875rem', sm: '0.875rem' } }}
                >
                  • تأكد من صحة البيانات قبل الحفظ
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Tab 1: User Management */}
        {hasPermission(PERMISSIONS.MANAGE_USERS) && (
          <TabPanel value={activeTab} index={visibleTabs.findIndex(tab => tab.permission === PERMISSIONS.MANAGE_USERS)}>
            <UserManagement />
          </TabPanel>
        )}

        {/* Tab 2: Security & Permissions */}
        {hasPermission(PERMISSIONS.MANAGE_SETTINGS) && (
          <TabPanel value={activeTab} index={visibleTabs.findIndex(tab => tab.label === 'الأمان والصلاحيات')}>
            <Typography
              variant="h6"
              gutterBottom
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                fontSize: { xs: '1.1rem', sm: '1.25rem' },
                mb: { xs: 2, sm: 3 }
              }}
            >
              <SecurityIcon sx={{ fontSize: { xs: '1.2rem', sm: '1.5rem' } }} />
              إعدادات الأمان والصلاحيات
            </Typography>

            <Grid container spacing={{ xs: 2, sm: 3 }}>
              <Grid item xs={12} md={6}>
                <Card variant="outlined" sx={{ height: 'fit-content' }}>
                  <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                    <Typography
                      variant="subtitle1"
                      gutterBottom
                      sx={{ fontSize: { xs: '1rem', sm: '1.1rem' } }}
                    >
                      إعدادات كلمة المرور
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <FormControlLabel
                        control={<Switch defaultChecked size={window.innerWidth < 600 ? "small" : "medium"} />}
                        label={
                          <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                            طلب كلمة مرور قوية
                          </Typography>
                        }
                      />
                      <FormControlLabel
                        control={<Switch size={window.innerWidth < 600 ? "small" : "medium"} />}
                        label={
                          <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                            انتهاء صلاحية كلمة المرور (90 يوم)
                          </Typography>
                        }
                      />
                      <FormControlLabel
                        control={<Switch defaultChecked size={window.innerWidth < 600 ? "small" : "medium"} />}
                        label={
                          <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                            تسجيل محاولات الدخول
                          </Typography>
                        }
                      />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card variant="outlined" sx={{ height: 'fit-content' }}>
                  <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                    <Typography
                      variant="subtitle1"
                      gutterBottom
                      sx={{ fontSize: { xs: '1rem', sm: '1.1rem' } }}
                    >
                      إعدادات الجلسة
                    </Typography>
                    <FormControl fullWidth sx={{ mb: 2 }}>
                      <InputLabel sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                        مدة انتهاء الجلسة
                      </InputLabel>
                      <Select
                        defaultValue={30}
                        label="مدة انتهاء الجلسة"
                        size={window.innerWidth < 600 ? "small" : "medium"}
                      >
                        <MenuItem value={15}>15 دقيقة</MenuItem>
                        <MenuItem value={30}>30 دقيقة</MenuItem>
                        <MenuItem value={60}>ساعة واحدة</MenuItem>
                        <MenuItem value={120}>ساعتان</MenuItem>
                      </Select>
                    </FormControl>
                    <FormControlLabel
                      control={<Switch defaultChecked size={window.innerWidth < 600 ? "small" : "medium"} />}
                      label={
                        <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                          تسجيل خروج تلقائي عند عدم النشاط
                        </Typography>
                      }
                    />
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>
        )}

        {/* Tab 3: Backup & Restore */}
        {hasPermission(PERMISSIONS.BACKUP_RESTORE) && (
          <TabPanel value={activeTab} index={visibleTabs.findIndex(tab => tab.permission === PERMISSIONS.BACKUP_RESTORE)}>
            <Typography
              variant="h6"
              gutterBottom
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                fontSize: { xs: '1.1rem', sm: '1.25rem' },
                mb: { xs: 2, sm: 3 }
              }}
            >
              <BackupIcon sx={{ fontSize: { xs: '1.2rem', sm: '1.5rem' } }} />
              النسخ الاحتياطي والاستعادة
            </Typography>

            <Grid container spacing={{ xs: 2, sm: 3 }}>
              <Grid item xs={12} md={6}>
                <Card variant="outlined" sx={{ height: 'fit-content' }}>
                  <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                    <Typography
                      variant="subtitle1"
                      gutterBottom
                      sx={{ fontSize: { xs: '1rem', sm: '1.1rem' } }}
                    >
                      إنشاء نسخة احتياطية
                    </Typography>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      paragraph
                      sx={{ fontSize: { xs: '0.875rem', sm: '0.875rem' } }}
                    >
                      قم بتصدير جميع بيانات الصندوق الحالي
                    </Typography>
                    <Button
                      variant="contained"
                      startIcon={<DownloadIcon />}
                      onClick={handleExportData}
                      fullWidth
                      size={window.innerWidth < 600 ? "medium" : "large"}
                      sx={{
                        py: { xs: 1, sm: 1.5 },
                        fontSize: { xs: '0.875rem', sm: '1rem' }
                      }}
                    >
                      تصدير البيانات
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card variant="outlined" sx={{ height: 'fit-content' }}>
                  <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                    <Typography
                      variant="subtitle1"
                      gutterBottom
                      sx={{ fontSize: { xs: '1rem', sm: '1.1rem' } }}
                    >
                      استعادة البيانات
                    </Typography>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      paragraph
                      sx={{ fontSize: { xs: '0.875rem', sm: '0.875rem' } }}
                    >
                      استيراد بيانات من نسخة احتياطية سابقة
                    </Typography>
                    <input
                      accept=".json"
                      style={{ display: 'none' }}
                      id="import-file"
                      type="file"
                      onChange={handleImportData}
                    />
                    <label htmlFor="import-file">
                      <Button
                        variant="outlined"
                        component="span"
                        startIcon={<UploadIcon />}
                        fullWidth
                        size={window.innerWidth < 600 ? "medium" : "large"}
                        sx={{
                          py: { xs: 1, sm: 1.5 },
                          fontSize: { xs: '0.875rem', sm: '1rem' }
                        }}
                      >
                        استيراد البيانات
                      </Button>
                    </label>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>
        )}

        {/* Tab 4: Notifications */}
        {hasPermission(PERMISSIONS.MANAGE_SETTINGS) && (
          <TabPanel value={activeTab} index={visibleTabs.findIndex(tab => tab.label === 'الإشعارات')}>
            <Typography
              variant="h6"
              gutterBottom
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                fontSize: { xs: '1.1rem', sm: '1.25rem' },
                mb: { xs: 2, sm: 3 }
              }}
            >
              <NotificationsIcon sx={{ fontSize: { xs: '1.2rem', sm: '1.5rem' } }} />
              إعدادات الإشعارات
            </Typography>

            <Grid container spacing={{ xs: 2, sm: 3 }}>
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                    <Typography
                      variant="subtitle1"
                      gutterBottom
                      sx={{ fontSize: { xs: '1rem', sm: '1.1rem' } }}
                    >
                      إشعارات النظام
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={systemSettings.notifications}
                            onChange={(e) => handleSystemSettingChange('notifications', e.target.checked)}
                            size={window.innerWidth < 600 ? "small" : "medium"}
                          />
                        }
                        label={
                          <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                            تفعيل الإشعارات
                          </Typography>
                        }
                      />
                      <FormControlLabel
                        control={<Switch defaultChecked size={window.innerWidth < 600 ? "small" : "medium"} />}
                        label={
                          <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                            إشعارات العمليات المالية
                          </Typography>
                        }
                      />
                      <FormControlLabel
                        control={<Switch size={window.innerWidth < 600 ? "small" : "medium"} />}
                        label={
                          <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                            إشعارات إضافة أعضاء جدد
                          </Typography>
                        }
                      />
                      <FormControlLabel
                        control={<Switch defaultChecked size={window.innerWidth < 600 ? "small" : "medium"} />}
                        label={
                          <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                            إشعارات النسخ الاحتياطي
                          </Typography>
                        }
                      />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>
        )}

        {/* Tab 5: Appearance & Language */}
        {hasPermission(PERMISSIONS.MANAGE_SETTINGS) && (
          <TabPanel value={activeTab} index={visibleTabs.findIndex(tab => tab.label === 'المظهر واللغة')}>
            <Typography
              variant="h6"
              gutterBottom
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                fontSize: { xs: '1.1rem', sm: '1.25rem' },
                mb: { xs: 2, sm: 3 }
              }}
            >
              <PaletteIcon sx={{ fontSize: { xs: '1.2rem', sm: '1.5rem' } }} />
              إعدادات المظهر واللغة
            </Typography>

            <Grid container spacing={{ xs: 2, sm: 3 }}>
              <Grid item xs={12} md={6}>
                <Card variant="outlined" sx={{ height: 'fit-content' }}>
                  <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                    <Typography
                      variant="subtitle1"
                      gutterBottom
                      sx={{ fontSize: { xs: '1rem', sm: '1.1rem' } }}
                    >
                      المظهر
                    </Typography>
                    <FormControl fullWidth sx={{ mb: 2 }}>
                      <InputLabel sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                        المظهر
                      </InputLabel>
                      <Select
                        value={systemSettings.theme}
                        onChange={(e) => handleSystemSettingChange('theme', e.target.value)}
                        label="المظهر"
                        size={window.innerWidth < 600 ? "small" : "medium"}
                      >
                        <MenuItem value="light">فاتح</MenuItem>
                        <MenuItem value="dark">داكن</MenuItem>
                        <MenuItem value="auto">تلقائي</MenuItem>
                      </Select>
                    </FormControl>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card variant="outlined" sx={{ height: 'fit-content' }}>
                  <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                    <Typography
                      variant="subtitle1"
                      gutterBottom
                      sx={{ fontSize: { xs: '1rem', sm: '1.1rem' } }}
                    >
                      اللغة والعملة
                    </Typography>
                    <FormControl fullWidth sx={{ mb: 2 }}>
                      <InputLabel sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                        اللغة
                      </InputLabel>
                      <Select
                        value={systemSettings.language}
                        onChange={(e) => handleSystemSettingChange('language', e.target.value)}
                        label="اللغة"
                        size={window.innerWidth < 600 ? "small" : "medium"}
                      >
                        <MenuItem value="ar">العربية</MenuItem>
                        <MenuItem value="en">English</MenuItem>
                      </Select>
                    </FormControl>
                    <FormControl fullWidth>
                      <InputLabel sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                        العملة
                      </InputLabel>
                      <Select
                        value={systemSettings.currency}
                        onChange={(e) => handleSystemSettingChange('currency', e.target.value)}
                        label="العملة"
                        size={window.innerWidth < 600 ? "small" : "medium"}
                      >
                        <MenuItem value="SAR">ريال سعودي (SAR)</MenuItem>
                        <MenuItem value="USD">دولار أمريكي (USD)</MenuItem>
                        <MenuItem value="EUR">يورو (EUR)</MenuItem>
                      </Select>
                    </FormControl>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>
        )}

        {/* Tab 6: System Information */}
        <TabPanel value={activeTab} index={visibleTabs.findIndex(tab => tab.label === 'معلومات النظام')}>
          <Typography
            variant="h6"
            gutterBottom
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              fontSize: { xs: '1.1rem', sm: '1.25rem' },
              mb: { xs: 2, sm: 3 }
            }}
          >
            <InfoIcon sx={{ fontSize: { xs: '1.2rem', sm: '1.5rem' } }} />
            معلومات النظام
          </Typography>

          <Grid container spacing={{ xs: 2, sm: 3 }}>
            <Grid item xs={12} md={6}>
              <Card variant="outlined" sx={{ height: 'fit-content' }}>
                <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                  <Typography
                    variant="subtitle1"
                    gutterBottom
                    sx={{ fontSize: { xs: '1rem', sm: '1.1rem' } }}
                  >
                    معلومات التطبيق
                  </Typography>
                  <List sx={{ py: 0 }}>
                    <ListItem sx={{ px: 0, py: { xs: 0.5, sm: 1 } }}>
                      <ListItemText
                        primary={
                          <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' }, fontWeight: 600 }}>
                            اسم التطبيق
                          </Typography>
                        }
                        secondary={
                          <Typography sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}>
                            نظام إدارة صناديق التوفير
                          </Typography>
                        }
                      />
                    </ListItem>
                    <ListItem sx={{ px: 0, py: { xs: 0.5, sm: 1 } }}>
                      <ListItemText
                        primary={
                          <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' }, fontWeight: 600 }}>
                            الإصدار
                          </Typography>
                        }
                        secondary={
                          <Typography sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}>
                            1.0.0
                          </Typography>
                        }
                      />
                    </ListItem>
                    <ListItem sx={{ px: 0, py: { xs: 0.5, sm: 1 } }}>
                      <ListItemText
                        primary={
                          <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' }, fontWeight: 600 }}>
                            تاريخ الإصدار
                          </Typography>
                        }
                        secondary={
                          <Typography sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}>
                            {new Date().toLocaleDateString('ar-SA')}
                          </Typography>
                        }
                      />
                    </ListItem>
                    <ListItem sx={{ px: 0, py: { xs: 0.5, sm: 1 } }}>
                      <ListItemText
                        primary={
                          <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' }, fontWeight: 600 }}>
                            المطور
                          </Typography>
                        }
                        secondary={
                          <Typography sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}>
                            ناصر مسعود آل مستنير
                          </Typography>
                        }
                      />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card variant="outlined" sx={{ height: 'fit-content' }}>
                <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                  <Typography
                    variant="subtitle1"
                    gutterBottom
                    sx={{ fontSize: { xs: '1rem', sm: '1.1rem' } }}
                  >
                    إحصائيات الاستخدام
                  </Typography>
                  <List sx={{ py: 0 }}>
                    <ListItem sx={{ px: 0, py: { xs: 0.5, sm: 1 } }}>
                      <ListItemText
                        primary={
                          <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' }, fontWeight: 600 }}>
                            تاريخ التثبيت
                          </Typography>
                        }
                        secondary={
                          <Typography sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}>
                            {new Date().toLocaleDateString('ar-SA')}
                          </Typography>
                        }
                      />
                    </ListItem>
                    <ListItem sx={{ px: 0, py: { xs: 0.5, sm: 1 } }}>
                      <ListItemText
                        primary={
                          <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' }, fontWeight: 600 }}>
                            آخر نسخة احتياطية
                          </Typography>
                        }
                        secondary={
                          <Typography sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}>
                            لم يتم إنشاء نسخة احتياطية بعد
                          </Typography>
                        }
                      />
                    </ListItem>
                    <ListItem sx={{ px: 0, py: { xs: 0.5, sm: 1 } }}>
                      <ListItemText
                        primary={
                          <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' }, fontWeight: 600 }}>
                            حجم البيانات
                          </Typography>
                        }
                        secondary={
                          <Typography sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}>
                            تقريباً 2.5 ميجابايت
                          </Typography>
                        }
                      />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>
      </Card>
    </Box>
  );
};

export default NewSettingsPage;
