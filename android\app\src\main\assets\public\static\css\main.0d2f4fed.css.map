{"version": 3, "file": "static/css/main.0d2f4fed.css", "mappings": "gGAEA,KACE,aACF,CAEA,EACE,qBAAsB,CAEtB,SACF,CAEA,OAJE,QAaF,CATA,KAKE,kCAAmC,CACnC,iCAAkC,CAClC,aAAc,CALd,2IAEY,CAIZ,gBACF,CAEA,KACE,uEAEF,CAWA,4DACE,gBACF,CAGA,eACE,8BACF,CAGA,oBAEE,UAAW,CADX,SAEF,CAEA,0BACE,kBACF,CAEA,0BACE,eAAgB,CAChB,iBACF,CAEA,gCACE,eACF,CAGA,SACE,gCACF,CAEA,kBACE,GACE,SACF,CACA,GACE,SACF,CACF,CAGA,yBAEE,kBACE,0BACF,CAEA,kBACE,2BACF,CAEA,kBACE,0BACF,CAGA,gBAEE,yBAA4B,CAC5B,yBAA2B,CAF3B,0BAGF,CAGA,cACE,oBACF,CAEA,qBACE,sBACF,CAGA,mBAEE,yBAA4B,CAD5B,qBAEF,CAGA,mBACE,sBACF,CAEA,mBACE,yBACF,CACF,CAEA,yBAEE,kBACE,0BACF,CAEA,kBACE,0BACF,CAEA,gBAEE,0BAA6B,CAC7B,yBAA2B,CAF3B,yBAGF,CAEA,qBACE,qBACF,CAEA,mBAEE,0BAA6B,CAD7B,qBAEF,CAGA,wBAEE,wBAA0B,CAD1B,yBAEF,CAEA,eACE,yBACF,CAGA,gBAEE,2BAA6B,CAD7B,mBAEF,CAGA,aACE,+BACF,CACF,CAGA,aACE,MACE,OAAQ,CACR,WACF,CAEA,KACE,wBAA0B,CAC1B,yBACF,CAGA,UACE,sBACF,CAGA,eAEE,+BAAiC,CADjC,yBAEF,CAEA,mBACE,kBACF,CAEA,cACE,qBACF,CAGA,eACE,wBACF,CAEA,mBAEE,+BAAiC,CADjC,yBAEF,CAOA,0CAHE,uBAMF,CAHA,kBAEE,qBACF,CACF,CC/NA,KAEE,aAAc,CADd,gBAEF,CAEA,UACE,aAAc,CACd,mBACF,CAEA,8CACE,UACE,2CACF,CACF,CAEA,YAKE,kBAAmB,CAJnB,wBAAyB,CAOzB,UAAY,CALZ,YAAa,CACb,qBAAsB,CAGtB,4BAA6B,CAD7B,sBAAuB,CAJvB,gBAOF,CAEA,UACE,aACF,CAEA,yBACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CAEA,eAGE,aAAc,CAFd,YAAa,CACb,YAAa,CAEb,gBACF,CAEA,cACE,WAAY,CAGZ,kBAAmB,CADnB,eAAgB,CADhB,YAGF,CAGA,yBACE,eACE,qBAAsB,CACtB,YACF,CAEA,cAGE,QAAO,CAFP,cAAe,CAGf,eAAgB,CAFhB,WAGF,CACF,CAEA,yBACE,cACE,WACF,CACF,CAEA,MACE,uBACF,CAEA,YAEE,+BAAyC,CADzC,0BAEF,CAEA,aACE,wBACF,CAEA,mBACE,oBACF,CAGA,cACE,+BACF,CAEA,oBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAGA,mBACE,8BACF,CAOA,2DACE,0BACF,CAGA,sBACE,aACF,CAEA,mBACE,gBACF,CAGA,kBAEE,2BAA6B,CAD7B,4BAA8B,CAE9B,0BACF,CAGA,kBACE,0BAA2B,CAC3B,gBACF", "sources": ["index.css", "App.css"], "sourcesContent": ["@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');\n\nhtml {\n  direction: rtl;\n}\n\n* {\n  box-sizing: border-box;\n  margin: 0;\n  padding: 0;\n}\n\nbody {\n  margin: 0;\n  font-family: '<PERSON><PERSON><PERSON>', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  direction: rtl;\n  text-align: right;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n/* MUI adjustments for RTL */\n.MuiFormControl-root {\n  text-align: right;\n}\n\n.MuiInputBase-input {\n  text-align: right;\n}\n\n.MuiTableCell-root {\n  text-align: right;\n}\n\n/* Arabic number class */\n.arabic-number {\n  font-family: '<PERSON><PERSON><PERSON>', sans-serif;\n}\n\n/* Custom scrollbar */\n::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #888;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #555;\n}\n\n/* Animation effects */\n.fade-in {\n  animation: fadeIn 0.5s ease-in-out;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n/* Mobile Responsive Global Styles */\n@media (max-width: 768px) {\n  /* Reduce font sizes for mobile */\n  .MuiTypography-h4 {\n    font-size: 1.5rem !important;\n  }\n\n  .MuiTypography-h5 {\n    font-size: 1.25rem !important;\n  }\n\n  .MuiTypography-h6 {\n    font-size: 1.1rem !important;\n  }\n\n  /* Reduce button sizes */\n  .MuiButton-root {\n    padding: 6px 12px !important;\n    font-size: 0.8rem !important;\n    min-height: 36px !important;\n  }\n\n  /* Reduce card padding */\n  .MuiCard-root {\n    margin: 4px !important;\n  }\n\n  .MuiCardContent-root {\n    padding: 12px !important;\n  }\n\n  /* Reduce table cell padding */\n  .MuiTableCell-root {\n    padding: 8px !important;\n    font-size: 0.8rem !important;\n  }\n\n  /* Reduce form field sizes */\n  .MuiTextField-root {\n    margin: 4px 0 !important;\n  }\n\n  .MuiInputBase-root {\n    font-size: 0.9rem !important;\n  }\n}\n\n@media (max-width: 480px) {\n  /* Extra small screens */\n  .MuiTypography-h4 {\n    font-size: 1.3rem !important;\n  }\n\n  .MuiTypography-h5 {\n    font-size: 1.1rem !important;\n  }\n\n  .MuiButton-root {\n    padding: 4px 8px !important;\n    font-size: 0.75rem !important;\n    min-height: 32px !important;\n  }\n\n  .MuiCardContent-root {\n    padding: 8px !important;\n  }\n\n  .MuiTableCell-root {\n    padding: 4px !important;\n    font-size: 0.75rem !important;\n  }\n\n  /* Ensure tables are fully responsive */\n  .MuiTableContainer-root {\n    overflow-x: auto !important;\n    max-width: 100% !important;\n  }\n\n  .MuiTable-root {\n    min-width: 300px !important;\n  }\n\n  /* Fix button positioning */\n  .MuiButton-root {\n    z-index: 1 !important;\n    position: relative !important;\n  }\n\n  /* Ensure proper spacing for mobile sidebar */\n  .MuiBox-root {\n    box-sizing: border-box !important;\n  }\n}\n\n/* Print Styles */\n@media print {\n  @page {\n    size: A4;\n    margin: 0.5in;\n  }\n\n  body {\n    font-size: 12px !important;\n    line-height: 1.3 !important;\n  }\n\n  /* Hide non-essential elements during print */\n  .no-print {\n    display: none !important;\n  }\n\n  /* Compact spacing for print */\n  .MuiPaper-root {\n    box-shadow: none !important;\n    border: 1px solid #ddd !important;\n  }\n\n  .MuiGrid-container {\n    margin: 0 !important;\n  }\n\n  .MuiGrid-item {\n    padding: 2px !important;\n  }\n\n  /* Table optimizations */\n  .MuiTable-root {\n    font-size: 10px !important;\n  }\n\n  .MuiTableCell-root {\n    padding: 2px 4px !important;\n    border: 1px solid #ccc !important;\n  }\n\n  /* Prevent page breaks inside tables */\n  .MuiTableContainer-root {\n    page-break-inside: avoid;\n  }\n\n  .MuiTableRow-root {\n    page-break-inside: avoid;\n    page-break-after: auto;\n  }\n}\n", ".App {\n  text-align: right;\n  direction: rtl;\n}\n\n.App-logo {\n  height: 40vmin;\n  pointer-events: none;\n}\n\n@media (prefers-reduced-motion: no-preference) {\n  .App-logo {\n    animation: App-logo-spin infinite 20s linear;\n  }\n}\n\n.App-header {\n  background-color: #282c34;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  font-size: calc(10px + 2vmin);\n  color: white;\n}\n\n.App-link {\n  color: #61dafb;\n}\n\n@keyframes App-logo-spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.app-container {\n  display: flex;\n  height: 100vh;\n  direction: rtl;\n  text-align: right;\n}\n\n.content-area {\n  flex-grow: 1;\n  padding: 16px;\n  overflow-y: auto;\n  margin-right: 240px; /* لإضافة مساحة للقائمة الجانبية */\n}\n\n/* Mobile Responsive Styles */\n@media (max-width: 768px) {\n  .app-container {\n    flex-direction: column;\n    height: 100vh;\n  }\n\n  .content-area {\n    margin-right: 0;\n    padding: 8px;\n    flex: 1;\n    overflow-y: auto;\n  }\n}\n\n@media (max-width: 480px) {\n  .content-area {\n    padding: 4px;\n  }\n}\n\n.card {\n  transition: all 0.3s ease;\n}\n\n.card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\n}\n\n.icon-button {\n  transition: transform 0.2s;\n}\n\n.icon-button:hover {\n  transform: scale(1.1);\n}\n\n/* Animation for dashboard widgets */\n.animate-card {\n  animation: fadeInUp 0.5s ease-out;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Custom styles for number inputs to ensure they display Arabic numbers */\ninput[type=\"number\"] {\n  font-family: 'Tajawal', sans-serif;\n}\n\n/* إضافة دعم RTL للعناصر الأخرى */\n.MuiTableCell-head, .MuiTableCell-body {\n  text-align: right !important;\n}\n\n.MuiFormControl-root {\n  text-align: right !important;\n}\n\n/* تعديل لعناصر المادة يو آي بحيث تدعم RTL */\nbutton.MuiButton-root {\n  direction: rtl;\n}\n\n.MuiInputBase-root {\n  text-align: right;\n}\n\n/* تعديل للقوائم المنسدلة */\n.MuiSelect-select {\n  padding-right: 14px !important;\n  padding-left: 32px !important;\n  text-align: right !important;\n}\n\n/* خصائص إضافية للاتجاه من اليمين لليسار */\n.MuiMenuItem-root {\n  justify-content: flex-start;\n  text-align: right;\n}\n"], "names": [], "sourceRoot": ""}