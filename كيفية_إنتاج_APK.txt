🚀 دليل إنتاج APK لتطبيق صندوق التوفير
==========================================

📱 الطريقة الأولى: PWA (الأسرع - 5 دقائق)
============================================

1️⃣ بناء التطبيق:
   - افتح Terminal/Command Prompt
   - اذهب لمجلد المشروع: cd savings-fund-manager
   - شغل الأمر: npm run build

2️⃣ رفع التطبيق:
   - اذهب إلى موقع Netlify.com
   - اسحب مجلد "build" إلى الموقع
   - احصل على الرابط

3️⃣ تثبيت على الجوال:
   - افتح الرابط في Chrome على الجوال
   - اضغط على القائمة (⋮)
   - اختر "إضافة إلى الشاشة الرئيسية"
   - ✅ تم! التطبيق مثبت كتطبيق أصلي

📦 الطريقة الثانية: APK حقيقي (متقدم - 30 دقيقة)
================================================

المتطلبات:
- Android Studio (حمل من: developer.android.com/studio)
- Java JDK 8+

الخطوات:
1️⃣ بناء التطبيق: npm run build
2️⃣ تهيئة Capacitor: npm run cap:init
3️⃣ إضافة Android: npm run cap:add:android
4️⃣ فتح Android Studio: npm run cap:build:android

في Android Studio:
- انتظر حتى ينتهي التحميل
- Build → Build Bundle(s) / APK(s) → Build APK(s)
- انتظر حتى ينتهي البناء
- اضغط "locate" لفتح مجلد APK
- ستجد ملف: app-debug.apk

🛠️ أدوات مساعدة
===============

للويندوز:
- شغل ملف: build-apk.bat
- اتبع التعليمات على الشاشة

للماك/لينكس:
- شغل ملف: ./build-apk.sh
- اتبع التعليمات على الشاشة

📋 ملفات مهمة
=============
- BUILD_APK_QUICK.md - دليل سريع
- APK_BUILD_GUIDE.md - دليل مفصل
- README_APK.md - معلومات شاملة
- build-apk.bat - أداة ويندوز
- build-apk.sh - أداة ماك/لينكس

🎯 أيهما أختار؟
===============

PWA (الطريقة الأولى):
✅ سريع وسهل
✅ لا يحتاج برامج إضافية
✅ يعمل على جميع الأجهزة
❌ يحتاج إنترنت للتشغيل الأول

APK (الطريقة الثانية):
✅ تطبيق أصلي 100%
✅ يعمل بدون إنترنت
✅ وصول كامل لمميزات الجهاز
❌ يحتاج إعداد بيئة التطوير

🆘 حل المشاكل
=============

مشكلة: "npm command not found"
الحل: ثبت Node.js من nodejs.org

مشكلة: "Android Studio لا يفتح المشروع"
الحل: تأكد من تثبيت Java JDK

مشكلة: "Build failed"
الحل: نظف المشروع وأعد المحاولة

مشكلة: "PWA لا يظهر خيار التثبيت"
الحل: تأكد من استخدام HTTPS

📞 للمساعدة
===========
1. تأكد من تشغيل "npm start" أولاً للتأكد من عمل التطبيق
2. راجع رسائل الخطأ في Terminal
3. تأكد من اتصال الإنترنت

روابط مفيدة:
- Android Studio: developer.android.com/studio
- Node.js: nodejs.org
- Netlify: netlify.com
