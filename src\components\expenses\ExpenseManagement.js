import React, { useState } from 'react';
import { formatCurrency } from '../../utils/formatters';
import { 
  Box, 
  Typography, 
  Button, 
  Paper, 
  Grid,
  TextField,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  IconButton,
  Divider,
  InputAdornment,
  FormHelperText,
  FormControlLabel,
  Checkbox,
  Radio,
  RadioGroup,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  FormLabel,
  Menu
} from '@mui/material';
import { 
  Add as AddIcon,
  TrendingDown as ExpenseIcon,
  CalendarToday as DateIcon,
  Search as SearchIcon,
  EventNote as EventIcon,
  Person as PersonIcon,
  Receipt as ReceiptIcon,
  Group as GroupIcon,
  PersonAdd as PersonAddIcon,
  CheckBox as CheckBoxIcon,
  CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Check as CheckIcon
} from '@mui/icons-material';
import moment from 'moment';
import momentHijri from 'moment-hijri';
import { useNavigate } from 'react-router-dom';

const ExpenseManagement = ({ members, expenses, addExpense, updateExpense, deleteExpense, dateType }) => {
  const navigate = useNavigate();
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');
  const [expenseType, setExpenseType] = useState('all'); // 'all', 'deduct_same_amount', 'single', 'multiple'
  const [selectedMembers, setSelectedMembers] = useState([]);
  const [newExpense, setNewExpense] = useState({
    amount: '',
    reason: '',
    notes: '',
    memberId: '',
    memberIds: []
  });
  const [errors, setErrors] = useState({});
  const [isEditing, setIsEditing] = useState(false);
  const [currentExpenseId, setCurrentExpenseId] = useState(null);
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);

  // Format date based on type (Gregorian or Hijri)
  const formatDate = (dateString) => {
    if (dateType === 'gregorian') {
      return moment(dateString).locale('ar').format('YYYY/MM/DD - hh:mm a');
    } else {
      return momentHijri(dateString).locale('ar').format('iYYYY/iMM/iDD - hh:mm a');
    }
  };

  // Format currency
  const formatCurrencyLocal = (amount) => {
    return formatCurrency(amount);
  };

  const handleClickOpen = () => {
    setIsEditing(false);
    setNewExpense({
      amount: '',
      reason: '',
      notes: '',
      memberId: '',
      memberIds: []
    });
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setNewExpense({
      amount: '',
      reason: '',
      notes: '',
      memberId: '',
      memberIds: []
    });
    setExpenseType('all');
    setSelectedMembers([]);
    setErrors({});
  };

  const validateForm = () => {
    const newErrors = {};
    if (!newExpense.amount) {
      newErrors.amount = 'الرجاء إدخال المبلغ';
    } else if (isNaN(newExpense.amount)) {
      newErrors.amount = 'الرجاء إدخال مبلغ صحيح';
    }
    
    if (!newExpense.reason) {
      newErrors.reason = 'الرجاء إدخال المناسبة/الغرض';
    }
    
    if (expenseType === 'single' && !newExpense.memberId) {
      newErrors.memberId = 'الرجاء اختيار العضو';
    }
    
    if (expenseType === 'multiple' && selectedMembers.length === 0) {
      newErrors.memberIds = 'الرجاء اختيار عضو واحد على الأقل';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      let expenseData;
      
      switch(expenseType) {
        case 'all':
          // تقسيم المبلغ على الجميع
          expenseData = {
            ...newExpense,
            memberId: null,
            memberIds: members.map(member => member.id),
            expenseType: 'all'
          };
          break;
        
        case 'deduct_same_amount':
          // خصم نفس المبلغ من حساب جميع الأعضاء
          expenseData = {
            ...newExpense,
            memberId: null,
            memberIds: members.map(member => member.id),
            expenseType: 'deduct_same_amount'
          };
          break;
        
        case 'single':
          // خصم من عضو واحد
          expenseData = {
            ...newExpense,
            memberIds: [],
            expenseType: 'single'
          };
          break;
        
        case 'multiple':
          // خصم من أعضاء محددين
          expenseData = {
            ...newExpense,
            memberId: null,
            memberIds: selectedMembers,
            expenseType: 'multiple'
          };
          break;
      }
      
      if (isEditing) {
        updateExpense(currentExpenseId, expenseData);
      } else {
        addExpense(expenseData);
      }
      handleClose();
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewExpense({ ...newExpense, [name]: value });
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({ ...errors, [name]: undefined });
    }
  };

  const handleExpenseTypeChange = (e) => {
    setExpenseType(e.target.value);
    
    // Reset member selections when changing expense type
    if (e.target.value === 'all') {
      setNewExpense({...newExpense, memberId: ''});
      setSelectedMembers([]);
    } else if (e.target.value === 'single') {
      setSelectedMembers([]);
    } else if (e.target.value === 'multiple') {
      setNewExpense({...newExpense, memberId: ''});
    }
    
    // Clear any related errors
    setErrors({...errors, memberId: undefined, memberIds: undefined});
  };
  
  const handleMemberCheckboxChange = (memberId) => {
    setSelectedMembers(prev => {
      if (prev.includes(memberId)) {
        return prev.filter(id => id !== memberId);
      } else {
        return [...prev, memberId];
      }
    });
    
    // Clear error when user selects members
    if (errors.memberIds) {
      setErrors({...errors, memberIds: undefined});
    }
  };

  // Navigate to member detail
  const handleViewMember = (id) => {
    navigate(`/member/${id}`);
  };
  
  // عرض قائمة بالأعضاء المحددين للمصروف
  const [memberListOpen, setMemberListOpen] = useState(false);
  const [selectedExpenseMembers, setSelectedExpenseMembers] = useState([]);
  
  const handleViewMultipleMembers = (memberIds) => {
    const selectedMembers = members.filter(member => memberIds.includes(member.id));
    setSelectedExpenseMembers(selectedMembers);
    setMemberListOpen(true);
  };
  
  const handleMemberListClose = () => {
    setMemberListOpen(false);
    setSelectedExpenseMembers([]);
  };

  // Open menu
  const handleMenuOpen = (event, expenseId) => {
    setMenuAnchorEl(event.currentTarget);
    // تأكد من تعيين معرف المصروف بشكل صحيح
    console.log('تعيين معرف المصروف للقائمة:', expenseId);
    setCurrentExpenseId(expenseId);
  };

  // Close menu
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
    // لا نقوم بإعادة تعيين معرف المصروف هنا لضمان استمرار توفره للعمليات اللاحقة
    // setCurrentExpenseId(null);
  };

  // Edit expense
  const handleEditExpense = () => {
    handleMenuClose();
    const expense = expenses.find(e => e.id === currentExpenseId);
    if (expense) {
      // Set expense type based on the expense data
      let type = 'all';
      if (expense.memberId) {
        type = 'single';
      } else if (expense.memberIds && expense.memberIds.length > 0 && expense.memberIds.length < members.length) {
        type = 'multiple';
      }
      
      setExpenseType(type);
      setNewExpense({
        amount: expense.amount,
        reason: expense.reason || '',
        notes: expense.notes || '',
        memberId: expense.memberId || '',
        memberIds: expense.memberIds || []
      });
      
      if (type === 'multiple') {
        setSelectedMembers(expense.memberIds || []);
      }
      
      setIsEditing(true);
      setCurrentExpenseId(expense.id);
      setOpen(true);
    }
  };

  // Open delete confirmation
  const handleDeleteConfirmOpen = () => {
    // تأكد من أن معرف المصروف لا يزال محفوظًا قبل فتح نافذة التأكيد
    console.log('فتح نافذة تأكيد الحذف للمصروف برقم:', currentExpenseId);
    handleMenuClose();
    setConfirmDeleteOpen(true);
  };

  // Close delete confirmation
  const handleDeleteConfirmClose = () => {
    setConfirmDeleteOpen(false);
    setCurrentExpenseId(null);
  };

  // Delete expense
  const handleDeleteConfirm = () => {
    try {
      // نسخ معرف المصروف إلى متغير محلي لضمان عدم فقدانه
      const expenseId = currentExpenseId;
      console.log('محاولة حذف المصروف برقم:', expenseId);
      
      if (expenseId) {
        // استدعاء دالة الحذف مع التأكد من تمرير المعرف كقيمة وليس كمرجع
        if (typeof deleteExpense === 'function') {
          // تخزين المصروف قبل حذفه للتأكد من تحديث أرصدة الأعضاء بشكل صحيح
          const expenseToDelete = expenses.find(e => e.id === expenseId);
          if (expenseToDelete) {
            // استدعاء دالة الحذف بشكل مباشر مع تمرير المعرف كقيمة
            deleteExpense(expenseId);
            console.log('تم استدعاء دالة حذف المصروف بنجاح');
          } else {
            console.error('لم يتم العثور على المصروف المراد حذفه');
          }
        } else {
          console.error('دالة حذف المصروف غير معرفة أو غير صالحة');
        }
      } else {
        console.error('لم يتم تحديد معرف المصروف للحذف');
      }
    } catch (error) {
      console.error('حدث خطأ أثناء محاولة حذف المصروف:', error);
    } finally {
      // إغلاق نافذة التأكيد بغض النظر عن نجاح أو فشل العملية
      setConfirmDeleteOpen(false);
      // إعادة تعيين معرف المصروف الحالي - نؤخر هذه الخطوة لضمان اكتمال عملية الحذف
      setTimeout(() => {
        setCurrentExpenseId(null);
        // إغلاق القائمة المنسدلة
        setMenuAnchorEl(null);
      }, 100);
    }
  };

  // Filter expenses based on search term
  const filteredExpenses = expenses.filter(expense => {
    const member = expense.memberId ? members.find(m => m.id === expense.memberId) : null;
    return (
      (expense.reason && expense.reason.toLowerCase().includes(search.toLowerCase())) ||
      (expense.notes && expense.notes.toLowerCase().includes(search.toLowerCase())) ||
      (member && member.name.toLowerCase().includes(search.toLowerCase()))
    );
  });

  // Common expense reasons
  const commonExpenseReasons = [
    'أجرة مقر',
    'مناسبة اجتماعية',
    'صيانة',
    'مستلزمات',
    'تبرعات',
    'رواتب',
    'مصاريف إدارية',
    'أخرى'
  ];

  return (
    <Box>
      <Paper elevation={3} sx={{ p: 3, borderRadius: '12px', mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h5" fontWeight="bold">
            المصروفات
          </Typography>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />} 
            onClick={handleClickOpen}
            sx={{ 
              mr: 'auto',
              ml: 0,
              borderRadius: '8px', 
              px: 3, 
              py: 1 
            }}
          >
            إضافة مصروف
          </Button>
        </Box>
      </Paper>
      <Divider sx={{ mb: 4 }} />

      {/* Search */}
      <Paper sx={{ p: 2, mb: 3, borderRadius: 2 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="بحث عن مصروفات..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Paper>

      {/* Expenses Table */}
      <TableContainer component={Paper} sx={{ borderRadius: 2, overflow: 'hidden' }}>
        <Table>
          <TableHead sx={{ bgcolor: '#f5f5f5' }}>
            <TableRow>
              <TableCell align="center" sx={{ fontWeight: 'bold', width: '50px' }}>#</TableCell>
              <TableCell align="right" sx={{ fontWeight: 'bold' }}>العضو</TableCell>
              <TableCell align="right" sx={{ fontWeight: 'bold' }}>المبلغ</TableCell>
              <TableCell align="right" sx={{ fontWeight: 'bold' }}>التاريخ والوقت</TableCell>
              <TableCell align="right" sx={{ fontWeight: 'bold' }}>المناسبة/الغرض</TableCell>
              <TableCell align="right" sx={{ fontWeight: 'bold' }}>ملاحظات</TableCell>
              <TableCell align="center" sx={{ fontWeight: 'bold' }}>عرض العضو</TableCell>
              <TableCell align="center" sx={{ fontWeight: 'bold' }}>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredExpenses.length > 0 ? (
              filteredExpenses.map((expense, index) => {
                const member = expense.memberId ? members.find(m => m.id === expense.memberId) : null;
                return (
                  <TableRow key={expense.id} sx={{ '&:hover': { bgcolor: '#f9f9f9' } }}>
                    <TableCell align="center" sx={{
                      fontWeight: 'bold',
                      color: 'primary.main',
                      width: '50px'
                    }}>
                      {index + 1}
                    </TableCell>
                    <TableCell align="right">
                      {expense.expenseType === 'all' ? (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <GroupIcon sx={{ mr: 1, fontSize: 18, color: 'primary.main' }} />
                          <Typography variant="body2" fontWeight="medium" color="primary.main">
                            تقسيم المبلغ على الجميع
                          </Typography>
                        </Box>
                      ) : expense.expenseType === 'deduct_same_amount' ? (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <GroupIcon sx={{ mr: 1, fontSize: 18, color: 'error.main' }} />
                          <Typography variant="body2" fontWeight="medium" color="error.main">
                            خصم من حساب جميع الأعضاء
                          </Typography>
                        </Box>
                      ) : expense.expenseType === 'multiple' && expense.memberIds && expense.memberIds.length > 0 ? (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <PersonAddIcon sx={{ mr: 1, fontSize: 18, color: 'info.main' }} />
                          <Typography variant="body2" fontWeight="medium" color="info.main">
                            {expense.memberIds.length} أعضاء
                          </Typography>
                        </Box>
                      ) : member ? (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <PersonIcon sx={{ mr: 1, fontSize: 18, color: 'text.primary' }} />
                          {member.name}
                        </Box>
                      ) : '-'}
                    </TableCell>
                    <TableCell
                      align="right"
                      sx={{ color: 'error.main', fontWeight: 'bold' }}
                    >
                      {formatCurrencyLocal(expense.amount)}
                    </TableCell>
                    <TableCell align="right">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <DateIcon sx={{ mr: 1, fontSize: 18, color: 'text.secondary' }} />
                        {formatDate(expense.createdAt)}
                      </Box>
                    </TableCell>
                    <TableCell align="right">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <EventIcon sx={{ mr: 1, fontSize: 18, color: 'text.secondary' }} />
                        {expense.reason || 'غير محدد'}
                      </Box>
                    </TableCell>
                    <TableCell align="right">{expense.notes || '-'}</TableCell>
                    <TableCell align="center">
                      {expense.expenseType === 'all' || expense.expenseType === 'deduct_same_amount' ? (
                        <IconButton 
                          color={expense.expenseType === 'all' ? "primary" : "error"} 
                          onClick={() => handleViewMultipleMembers(members.map(m => m.id))}
                          sx={{ '&:hover': { transform: 'scale(1.1)' } }}
                          title={expense.expenseType === 'all' ? "عرض جميع الأعضاء (تقسيم المبلغ)" : "عرض جميع الأعضاء (خصم نفس المبلغ)"}
                        >
                          <GroupIcon />
                        </IconButton>
                      ) : expense.expenseType === 'multiple' && expense.memberIds && expense.memberIds.length > 0 ? (
                        <IconButton 
                          color="info" 
                          onClick={() => handleViewMultipleMembers(expense.memberIds)}
                          sx={{ '&:hover': { transform: 'scale(1.1)' } }}
                          title="عرض الأعضاء المحددين"
                        >
                          <PersonAddIcon />
                        </IconButton>
                      ) : member ? (
                        <IconButton 
                          color="primary" 
                          onClick={() => handleViewMember(member.id)}
                          sx={{ '&:hover': { transform: 'scale(1.1)' } }}
                          title="عرض العضو"
                        >
                          <PersonIcon />
                        </IconButton>
                      ) : null}
                    </TableCell>
                    <TableCell align="center">
                      <IconButton
                        size="small"
                        onClick={(e) => handleMenuOpen(e, expense.id)}
                        sx={{ '&:hover': { color: 'primary.main' } }}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell colSpan={8} align="center" sx={{ py: 3 }}>
                  <Typography variant="body1" color="text.secondary">
                    {search ? 'لا توجد نتائج للبحث' : 'لا توجد مصروفات حتى الآن'}
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Add Expense Dialog */}
      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <ExpenseIcon sx={{ mr: 1, color: 'error.main' }} />
            <Typography variant="h6" component="div">
              إضافة مصروف جديد
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 0.5 }}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors.amount} sx={{ mb: 2 }}>
                <TextField
                  label="المبلغ"
                  name="amount"
                  type="number"
                  value={newExpense.amount}
                  onChange={handleInputChange}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">ريال</InputAdornment>
                    ),
                  }}
                  helperText={errors.amount || "يمكن إدخال قيمة سالبة للإشارة إلى مديونية/استرجاع"}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors.reason}>
                <InputLabel id="reason-select-label">المناسبة/الغرض</InputLabel>
                <Select
                  labelId="reason-select-label"
                  name="reason"
                  value={newExpense.reason}
                  onChange={handleInputChange}
                  label="المناسبة/الغرض"
                  startAdornment={
                    <InputAdornment position="start">
                      <ReceiptIcon />
                    </InputAdornment>
                  }
                >
                  {commonExpenseReasons.map((reason) => (
                    <MenuItem key={reason} value={reason}>
                      {reason}
                    </MenuItem>
                  ))}
                </Select>
                {errors.reason && <FormHelperText>{errors.reason}</FormHelperText>}
              </FormControl>
            </Grid>
            
            {/* Expense Assignment Options */}
            <Grid item xs={12}>
              <FormControl component="fieldset" sx={{ width: '100%', mb: 2 }}>
                <FormLabel component="legend" sx={{ mb: 1, fontWeight: 'bold', color: 'text.primary' }}>
                  تعيين المصروف
                </FormLabel>
                <RadioGroup
                  name="expenseType"
                  value={expenseType}
                  onChange={handleExpenseTypeChange}
                >
                  <FormControlLabel 
                    value="all" 
                    control={<Radio />} 
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <GroupIcon sx={{ mr: 1, color: 'primary.main' }} />
                        <Typography>تقسيم المبلغ على الجميع</Typography>
                      </Box>
                    } 
                  />
                  
                  <FormControlLabel 
                    value="deduct_same_amount" 
                    control={<Radio />} 
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <GroupIcon sx={{ mr: 1, color: 'error.main' }} />
                        <Typography>خصم من حساب جميع الأعضاء (نفس المبلغ لكل عضو)</Typography>
                      </Box>
                    } 
                  />
                  
                  <FormControlLabel 
                    value="single" 
                    control={<Radio />} 
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
                        <Typography>عضو واحد</Typography>
                      </Box>
                    } 
                  />
                  
                  <FormControlLabel 
                    value="multiple" 
                    control={<Radio />} 
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <PersonAddIcon sx={{ mr: 1, color: 'primary.main' }} />
                        <Typography>أعضاء محددين</Typography>
                      </Box>
                    } 
                  />
                </RadioGroup>
              </FormControl>
              
              {/* Single Member Selection */}
              {expenseType === 'single' && (
                <FormControl fullWidth error={!!errors.memberId} sx={{ mb: 2 }}>
                  <InputLabel id="member-select-label">العضو</InputLabel>
                  <Select
                    labelId="member-select-label"
                    name="memberId"
                    value={newExpense.memberId}
                    onChange={handleInputChange}
                    label="العضو"
                    startAdornment={
                      <InputAdornment position="start">
                        <PersonIcon />
                      </InputAdornment>
                    }
                  >
                    {members.map((member) => (
                      <MenuItem key={member.id} value={member.id}>
                        {member.name}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.memberId && <FormHelperText>{errors.memberId}</FormHelperText>}
                </FormControl>
              )}
              
              {/* Multiple Members Selection */}
              {expenseType === 'multiple' && (
                <FormControl fullWidth error={!!errors.memberIds}>
                  <FormLabel component="legend" sx={{ mb: 2, fontWeight: 'bold', color: 'text.primary' }}>
                    اختر الأعضاء المراد خصم المصروف من أرصدتهم
                  </FormLabel>
                  <Paper
                    variant="outlined"
                    sx={{
                      maxHeight: 250,
                      overflow: 'auto',
                      mt: 1,
                      p: 0,
                      borderRadius: 2,
                      border: '2px solid',
                      borderColor: errors.memberIds ? 'error.main' : 'divider'
                    }}
                  >
                    <List dense sx={{ p: 0 }}>
                      {members.map((member, index) => (
                        <ListItem key={member.id} dense sx={{ py: 0.5, px: 1 }}>
                          <Checkbox
                            checked={selectedMembers.includes(member.id)}
                            onChange={(e) => {
                              e.stopPropagation();
                              handleMemberCheckboxChange(member.id);
                            }}
                            color="primary"
                            size="small"
                            sx={{ mr: 1, p: 0.5 }}
                          />
                          <Typography
                            variant="body2"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleMemberCheckboxChange(member.id);
                            }}
                            sx={{
                              fontSize: '0.95rem',
                              fontWeight: selectedMembers.includes(member.id) ? 'bold' : 'normal',
                              color: selectedMembers.includes(member.id) ? 'error.main' : 'text.primary',
                              cursor: 'pointer',
                              flex: 1,
                              '&:hover': {
                                color: 'error.main'
                              }
                            }}
                          >
                            {member.name}
                          </Typography>
                        </ListItem>
                      ))}
                    </List>

                    {/* عداد الأعضاء المحددين */}
                    {selectedMembers.length > 0 && (
                      <Box
                        sx={{
                          p: 2,
                          bgcolor: 'error.light',
                          borderTop: '1px solid',
                          borderTopColor: 'divider',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        <Typography
                          variant="body2"
                          sx={{
                            color: 'error.dark',
                            fontWeight: 'bold',
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1
                          }}
                        >
                          <CheckIcon fontSize="small" />
                          تم اختيار {selectedMembers.length} عضو من أصل {members.length}
                        </Typography>
                      </Box>
                    )}
                  </Paper>
                  {errors.memberIds && (
                    <FormHelperText error sx={{ mt: 1, fontSize: '0.875rem' }}>
                      {errors.memberIds}
                    </FormHelperText>
                  )}
                </FormControl>
              )}
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                name="notes"
                label="ملاحظات"
                fullWidth
                variant="outlined"
                value={newExpense.notes}
                onChange={handleInputChange}
                multiline
                rows={2}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 2, justifyContent: 'flex-start' }}>
          <Button 
            onClick={handleClose} 
            color="inherit" 
            sx={{ ml: 2, mr: 0 }}
          >
            إلغاء
          </Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained" 
            color="primary"
          >
            إضافة
          </Button>
        </DialogActions>
      </Dialog>

      {/* Action Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        slotProps={{
          paper: {
            sx: {
              mt: 0.5,
              ml: 0.5,
              '& .MuiMenuItem-root': {
                px: 2,
                py: 1,
              },
            },
          },
        }}
      >
        <MenuItem onClick={handleEditExpense}>
          <EditIcon fontSize="small" sx={{ ml: 1 }} />
          تعديل
        </MenuItem>
        <MenuItem onClick={handleDeleteConfirmOpen} sx={{ color: 'error.main' }}>
          <DeleteIcon fontSize="small" sx={{ ml: 1 }} />
          حذف
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={confirmDeleteOpen}
        onClose={handleDeleteConfirmClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <DeleteIcon sx={{ mr: 1, color: 'error.main' }} />
            تأكيد الحذف
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1">
            هل أنت متأكد من رغبتك في حذف هذا المصروف؟ لا يمكن التراجع عن هذا الإجراء.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ p: 2, justifyContent: 'flex-start' }}>
          <Button onClick={handleDeleteConfirmClose} color="inherit" sx={{ ml: 2, mr: 0 }}>
            إلغاء
          </Button>
          <Button onClick={handleDeleteConfirm} variant="contained" color="error">
            حذف
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Members List Dialog */}
      <Dialog
        open={memberListOpen}
        onClose={handleMemberListClose}
        maxWidth="sm"
        fullWidth
        aria-labelledby="members-list-dialog-title"
      >
        <DialogTitle id="members-list-dialog-title">
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <GroupIcon sx={{ mr: 1, color: 'primary.main' }} />
            قائمة الأعضاء المشاركين في المصروف
          </Box>
        </DialogTitle>
        <DialogContent>
          <List sx={{ pt: 1 }}>
            {selectedExpenseMembers.map((member) => (
              <ListItem 
                key={member.id}
                onClick={() => {
                  handleMemberListClose();
                  handleViewMember(member.id);
                }}
                sx={{ 
                  borderRadius: 1,
                  mb: 1,
                  '&:hover': { bgcolor: 'rgba(25, 118, 210, 0.08)' },
                }}
              >
                <ListItemIcon>
                  <PersonIcon color="primary" />
                </ListItemIcon>
                <ListItemText 
                  primary={member.name}
                  secondary={
                    <Typography variant="body2" color={member.totalBalance < 0 ? 'error.main' : 'success.main'}>
                      الرصيد: {formatCurrencyLocal(member.totalBalance)}
                    </Typography>
                  }
                />
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <DialogActions sx={{ p: 2, justifyContent: 'flex-start' }}>
          <Button onClick={handleMemberListClose} color="primary">
            إغلاق
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ExpenseManagement;
