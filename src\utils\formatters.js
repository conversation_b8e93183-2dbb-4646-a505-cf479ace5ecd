// تنسيق العملة المحسن - إزالة الفواصل العشرية غير الضرورية واستخدام رمز الريال الجديد
export const formatCurrency = (amount) => {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return '0 ﷼';
  }

  const numAmount = parseFloat(amount);

  // تحديد عدد الخانات العشرية المطلوبة
  const hasDecimals = numAmount % 1 !== 0;
  const decimalPlaces = hasDecimals ? 2 : 0;

  // تنسيق الرقم بدون رمز العملة أولاً
  const formattedNumber = new Intl.NumberFormat('ar-SA', {
    minimumFractionDigits: decimalPlaces,
    maximumFractionDigits: 2
  }).format(Math.abs(numAmount));

  // إضافة رمز الريال الجديد
  return `${formattedNumber} ﷼`;
};

// تنسيق العملة مع إظهار السالب بوضوح
export const formatCurrencyWithSign = (amount) => {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return '0 ﷼';
  }

  const numAmount = parseFloat(amount);
  const hasDecimals = numAmount % 1 !== 0;
  const decimalPlaces = hasDecimals ? 2 : 0;

  // تنسيق الرقم بدون رمز العملة
  const formattedNumber = new Intl.NumberFormat('ar-SA', {
    minimumFractionDigits: decimalPlaces,
    maximumFractionDigits: 2
  }).format(Math.abs(numAmount));

  const formatted = `${formattedNumber} ﷼`;

  if (numAmount < 0) {
    return `- ${formatted} (مديون)`;
  }

  return formatted;
};

// تنسيق التاريخ
export const formatDate = (date) => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return '';
  
  return dateObj.toLocaleDateString('ar-SA', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

// حساب الوقت المتبقي للسلفة
export const calculateLoanTimeRemaining = (loanDate, repaymentPeriod) => {
  if (!loanDate || !repaymentPeriod) return '';
  
  const startDate = new Date(loanDate);
  const endDate = new Date(startDate);
  endDate.setMonth(endDate.getMonth() + parseInt(repaymentPeriod));
  
  const now = new Date();
  const timeDiff = endDate.getTime() - now.getTime();
  
  if (timeDiff <= 0) {
    return 'منتهية الصلاحية';
  }
  
  const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
  const monthsDiff = Math.floor(daysDiff / 30);
  const remainingDays = daysDiff % 30;
  
  if (monthsDiff > 0) {
    return `${monthsDiff} شهر و ${remainingDays} يوم`;
  } else {
    return `${daysDiff} يوم`;
  }
};

// إنشاء جدول دفعات السلفة
export const generateLoanPaymentSchedule = (loanAmount, startDate, paymentType, totalPeriod) => {
  const schedule = [];
  const amount = parseFloat(loanAmount);
  const start = new Date(startDate);
  
  if (paymentType === 'single') {
    // دفعة واحدة
    const dueDate = new Date(start);
    dueDate.setMonth(dueDate.getMonth() + parseInt(totalPeriod));
    
    schedule.push({
      id: 1,
      amount: amount,
      dueDate: dueDate.toISOString().split('T')[0],
      status: 'pending', // pending, paid, overdue
      paidDate: null,
      paidAmount: 0
    });
  } else {
    // دفعات شهرية
    const monthlyAmount = amount / parseInt(totalPeriod);
    
    for (let i = 1; i <= parseInt(totalPeriod); i++) {
      const dueDate = new Date(start);
      dueDate.setMonth(dueDate.getMonth() + i);
      
      schedule.push({
        id: i,
        amount: monthlyAmount,
        dueDate: dueDate.toISOString().split('T')[0],
        status: 'pending',
        paidDate: null,
        paidAmount: 0
      });
    }
  }
  
  return schedule;
};
