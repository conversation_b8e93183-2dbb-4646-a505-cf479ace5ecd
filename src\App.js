import React, { useEffect } from 'react';
import { Box, Typography } from '@mui/material';
import CssBaseline from '@mui/material/CssBaseline';
import moment from 'moment';
import 'moment/locale/ar';

// Import Context Providers
import { FundProvider } from './contexts/FundContext';
import { UserProvider } from './contexts/UserContext';
import { AuthProvider, useAuth } from './contexts/AuthContext';

// Components
import LoginPage from './components/auth/LoginPage';
import AuthenticatedApp from './components/layout/AuthenticatedApp';
import './App.css';

// Initialize moment with Arabic locale
moment.locale('ar');

// مسح جميع البيانات لبدء التطبيق من الصفر
const clearAllCaches = async () => {
  try {
    // مسح Service Worker caches
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );
      console.log('تم مسح جميع الكاشات');
    }

    // مسح localStorage بالكامل
    localStorage.clear();

    // مسح sessionStorage
    sessionStorage.clear();

    console.log('تم مسح جميع البيانات - التطبيق يبدأ من الصفر');
  } catch (error) {
    console.error('خطأ في مسح البيانات المخزنة:', error);
  }
};

// Main App component
function AppContent() {
  const { isAuthenticated, loading: authLoading } = useAuth();

  // مسح البيانات المخزنة عند تحميل التطبيق
  useEffect(() => {
    clearAllCaches();
  }, []);

  if (authLoading) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }}
      >
        <Typography variant="h6" color="white">
          جاري التحميل...
        </Typography>
      </Box>
    );
  }

  if (!isAuthenticated) {
    return <LoginPage />;
  }

  return <AuthenticatedApp />;
}

// Main App wrapper
function App() {
  return (
    <AuthProvider>
      <UserProvider>
        <FundProvider>
          <CssBaseline />
          <AppContent />
        </FundProvider>
      </UserProvider>
    </AuthProvider>
  );
}

export default App;
