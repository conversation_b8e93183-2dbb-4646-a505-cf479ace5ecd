import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  Tabs,
  Tab,
  Alert,
  List,
  ListItem,
  ListItemText,
  Chip,
  Paper,
  Grid
} from '@mui/material';
import {
  Upload as UploadIcon,
  ContentPaste as PasteIcon,
  FamilyRestroom as FamilyIcon,
  CheckCircle as CheckIcon
} from '@mui/icons-material';

const FamilyBulkImport = ({ open, onClose, onImport }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [textData, setTextData] = useState('');
  const [preview, setPreview] = useState([]);
  const [errors, setErrors] = useState([]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    setTextData('');
    setPreview([]);
    setErrors([]);
  };

  // معالجة النص المدخل
  const processTextData = (text) => {
    if (!text.trim()) {
      setPreview([]);
      setErrors([]);
      return;
    }

    const lines = text.split('\n').filter(line => line.trim());
    const processed = [];
    const newErrors = [];

    lines.forEach((line, index) => {
      const trimmedLine = line.trim();
      if (!trimmedLine) return;

      // تحليل البيانات (مفصولة بفاصلة أو تاب)
      const parts = trimmedLine.split(/[,\t]/).map(part => part.trim());
      
      if (parts.length < 1) {
        newErrors.push(`السطر ${index + 1}: بيانات غير كافية`);
        return;
      }

      const family = {
        name: parts[0] || '',
        headName: parts[1] || '',
        phone: parts[2] || '',
        address: parts[3] || '',
        notes: parts[4] || ''
      };

      if (!family.name) {
        newErrors.push(`السطر ${index + 1}: اسم العائلة مطلوب`);
        return;
      }

      if (family.name.length < 2) {
        newErrors.push(`السطر ${index + 1}: اسم العائلة قصير جداً`);
        return;
      }

      processed.push(family);
    });

    setPreview(processed);
    setErrors(newErrors);
  };

  // معالجة ملف CSV/TXT
  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target.result;
      setTextData(content);
      processTextData(content);
    };
    reader.readAsText(file, 'UTF-8');
  };

  // معالجة النص المدخل يدوياً
  const handleTextChange = (event) => {
    const text = event.target.value;
    setTextData(text);
    processTextData(text);
  };

  // لصق من الحافظة
  const handlePasteFromClipboard = async () => {
    try {
      const text = await navigator.clipboard.readText();
      setTextData(text);
      processTextData(text);
    } catch (err) {
      console.error('فشل في قراءة الحافظة:', err);
    }
  };

  // تأكيد الاستيراد
  const handleImport = () => {
    if (preview.length === 0) return;
    
    onImport(preview);
    handleClose();
  };

  // إغلاق النافذة
  const handleClose = () => {
    setTextData('');
    setPreview([]);
    setErrors([]);
    setActiveTab(0);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <FamilyIcon />
        استيراد عائلات متعددة
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Tabs value={activeTab} onChange={handleTabChange}>
            <Tab label="رفع ملف" icon={<UploadIcon />} />
            <Tab label="نسخ ولصق" icon={<PasteIcon />} />
          </Tabs>
        </Box>

        {/* تبويب رفع الملف */}
        {activeTab === 0 && (
          <Box>
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                ارفع ملف CSV أو نصي بالتنسيق: اسم العائلة، رب الأسرة، الهاتف، العنوان، ملاحظات
              </Typography>
              <Typography variant="body2" sx={{ mt: 1 }}>
                <strong>مثال:</strong> عائلة الأحمد، أحمد محمد، 0501234567، الرياض، عائلة كريمة
              </Typography>
            </Alert>
            
            <input
              accept=".txt,.csv"
              style={{ display: 'none' }}
              id="family-bulk-import-file"
              type="file"
              onChange={handleFileUpload}
            />
            <label htmlFor="family-bulk-import-file">
              <Button
                variant="outlined"
                component="span"
                startIcon={<UploadIcon />}
                fullWidth
                sx={{ py: 2 }}
              >
                اختيار ملف (.txt أو .csv)
              </Button>
            </label>
          </Box>
        )}

        {/* تبويب النسخ واللصق */}
        {activeTab === 1 && (
          <Box>
            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
              <Button
                variant="outlined"
                startIcon={<PasteIcon />}
                onClick={handlePasteFromClipboard}
                size="small"
              >
                لصق من الحافظة
              </Button>
            </Box>
            
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                اكتب أو الصق بيانات العائلات بالتنسيق: اسم العائلة، رب الأسرة، الهاتف، العنوان، ملاحظات
              </Typography>
              <Typography variant="body2" sx={{ mt: 1 }}>
                <strong>مثال:</strong>
              </Typography>
              <Typography variant="body2" component="div" sx={{ fontFamily: 'monospace', mt: 1 }}>
                عائلة الأحمد، أحمد محمد، 0501234567، الرياض، عائلة كريمة<br/>
                عائلة السعد، سعد علي، 0509876543، جدة، عائلة محترمة
              </Typography>
            </Alert>
            
            <TextField
              fullWidth
              multiline
              rows={8}
              placeholder="عائلة الأحمد، أحمد محمد، 0501234567، الرياض، عائلة كريمة
عائلة السعد، سعد علي، 0509876543، جدة، عائلة محترمة
عائلة الخالد، خالد أحمد، 0507654321، الدمام"
              value={textData}
              onChange={handleTextChange}
              variant="outlined"
            />
          </Box>
        )}

        {/* معاينة البيانات */}
        {preview.length > 0 && (
          <Box sx={{ mt: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <CheckIcon color="success" />
              معاينة البيانات ({preview.length} عائلة)
            </Typography>
            
            <Paper sx={{ maxHeight: 200, overflow: 'auto', p: 1 }}>
              <List dense>
                {preview.slice(0, 10).map((family, index) => (
                  <ListItem key={index}>
                    <ListItemText
                      primary={family.name}
                      secondary={
                        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mt: 0.5 }}>
                          {family.headName && <Chip label={`رب الأسرة: ${family.headName}`} size="small" />}
                          {family.phone && <Chip label={family.phone} size="small" />}
                          {family.address && <Chip label={family.address} size="small" color="primary" />}
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
                {preview.length > 10 && (
                  <ListItem>
                    <ListItemText
                      primary={`... و ${preview.length - 10} عائلة أخرى`}
                      sx={{ textAlign: 'center', fontStyle: 'italic' }}
                    />
                  </ListItem>
                )}
              </List>
            </Paper>
          </Box>
        )}

        {/* الأخطاء */}
        {errors.length > 0 && (
          <Box sx={{ mt: 2 }}>
            <Alert severity="warning">
              <Typography variant="subtitle2" gutterBottom>
                تم العثور على {errors.length} خطأ:
              </Typography>
              <List dense>
                {errors.slice(0, 5).map((error, index) => (
                  <ListItem key={index} sx={{ py: 0 }}>
                    <ListItemText primary={error} />
                  </ListItem>
                ))}
                {errors.length > 5 && (
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    ... و {errors.length - 5} خطأ آخر
                  </Typography>
                )}
              </List>
            </Alert>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose}>إلغاء</Button>
        <Button
          onClick={handleImport}
          variant="contained"
          disabled={preview.length === 0 || errors.length > 0}
          startIcon={<FamilyIcon />}
        >
          استيراد {preview.length} عائلة
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default FamilyBulkImport;
