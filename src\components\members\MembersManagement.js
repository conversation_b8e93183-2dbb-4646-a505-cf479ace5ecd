import React, { useState, useEffect } from 'react';
import { formatCurrency } from '../../utils/formatters';
import { 
  Box, 
  Grid, 
  Typography, 
  Button, 
  TextField, 
  Paper, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Divider,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Badge,
  Menu,
  ListItemIcon,
  DialogContentText,
  FormHelperText,
  Tooltip
} from '@mui/material';
import { 
  Add as AddIcon,
  PersonAdd as PersonAddIcon,
  Search as SearchIcon,
  Visibility as ViewIcon,
  FamilyRestroom as FamilyIcon,
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  AccountBalance as BalanceIcon,
  Person as PersonIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Upload as UploadIcon
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import moment from 'moment';
import momentHijri from 'moment-hijri';
import Copyright from '../common/Copyright';
import BulkImport from './BulkImport';

const MembersManagement = ({ members, addMember, updateMember, deleteMember, onSelectMember, dateType, families }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [open, setOpen] = useState(false);
  const [newMember, setNewMember] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    familyId: ''
  });
  const [search, setSearch] = useState('');
  const [errors, setErrors] = useState({});

  const [isEditing, setIsEditing] = useState(false);
  const [currentMemberId, setCurrentMemberId] = useState(null);
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [addToFamilyDialogOpen, setAddToFamilyDialogOpen] = useState(false);
  const [selectedFamilyId, setSelectedFamilyId] = useState('');
  const [selectedMemberId, setSelectedMemberId] = useState(null);
  const [editMember, setEditMember] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    familyId: ''
  });
  const [bulkImportOpen, setBulkImportOpen] = useState(false);

  // التحقق من وجود عضو محدد في state وفتح تفاصيله
  useEffect(() => {
    if (location.state?.selectedMemberId) {
      const memberId = location.state.selectedMemberId;
      const member = members.find(m => m.id === memberId);
      if (member) {
        // فتح تفاصيل العضو مباشرة
        navigate(`/member/${memberId}`);
      }
      // مسح state بعد الاستخدام
      navigate(location.pathname, { replace: true, state: {} });
    }
  }, [location.state, members, navigate, location.pathname]);



  // Handle opening add to family dialog
  const handleAddToFamilyOpen = (memberId) => {
    setSelectedMemberId(memberId);
    setSelectedFamilyId('');
    setAddToFamilyDialogOpen(true);
  };

  // Handle closing add to family dialog
  const handleAddToFamilyClose = () => {
    setAddToFamilyDialogOpen(false);
    setSelectedMemberId(null);
    setSelectedFamilyId('');
    setErrors({});
  };

  // Handle family selection change
  const handleFamilySelectionChange = (e) => {
    setSelectedFamilyId(e.target.value);
    // Clear error when user selects a family
    if (errors.selectedFamilyId) {
      setErrors({ ...errors, selectedFamilyId: undefined });
    }
  };

  // وظيفة لتحديث إحصائيات العائلة
  const updateFamilyStats = (familyId, fundId) => {
    try {
      // الحصول على معرف الصندوق الحالي إذا لم يتم تمريره
      const currentFundId = fundId || localStorage.getItem('current_fund_id') || 'default';
      
      // استخدام مفاتيح تخزين مرتبطة بالصندوق الحالي
      const membersKey = `savings_fund_members_${currentFundId}`;
      const familiesKey = `savings_fund_families_${currentFundId}`;
      
      // الحصول على البيانات من localStorage
      const storedMembers = JSON.parse(localStorage.getItem(membersKey) || '[]');
      const storedFamilies = JSON.parse(localStorage.getItem(familiesKey) || '[]');
      
      // التحقق من وجود العائلة في الصندوق الحالي
      const familyExists = storedFamilies.some(f => f.id === familyId);
      if (!familyExists) {
        console.warn(`Family with ID ${familyId} not found in fund ${currentFundId}`);
        return;
      }
      
      // فلترة الأعضاء حسب العائلة للصندوق الحالي فقط
      const familyMembers = storedMembers.filter(member => member.familyId === familyId);
      
      // حساب عدد الأعضاء والرصيد الإجمالي
      const membersCount = familyMembers.length;
      const totalBalance = familyMembers.reduce((sum, member) => sum + (parseFloat(member.totalBalance) || 0), 0);
      
      // تحديث إحصائيات العائلة
      const updatedFamilies = storedFamilies.map(f => {
        if (f.id === familyId) {
          return {
            ...f,
            membersCount,
            totalBalance
          };
        }
        return f;
      });
      
      // حفظ البيانات المحدثة في localStorage للصندوق الحالي فقط
      localStorage.setItem(familiesKey, JSON.stringify(updatedFamilies));
    } catch (error) {
      console.error("Error updating family stats:", error);
    }
  };

  // Handle adding member to family
  const handleAddMemberToFamily = () => {
    // Validate selection
    if (!selectedFamilyId) {
      setErrors({ ...errors, selectedFamilyId: 'الرجاء اختيار عائلة' });
      return;
    }

    // Update member with new family ID
    if (selectedMemberId && typeof updateMember === 'function') {
      // الحصول على معرف الصندوق الحالي
      const currentFundId = localStorage.getItem('current_fund_id') || 'default';
      
      // تحديث العضو في الصندوق الحالي فقط
      updateMember(selectedMemberId, { familyId: selectedFamilyId });
      
      // تحديث إحصائيات العائلة
      updateFamilyStats(selectedFamilyId, currentFundId);
      
      // إعادة تحميل البيانات لتحديث الواجهة
      if (typeof onSelectMember === 'function') {
        onSelectMember(selectedMemberId);
      }
      
      handleAddToFamilyClose();
    }
  };

  // Format date based on type (Gregorian or Hijri)
  const formatDate = (dateString) => {
    if (dateType === 'gregorian') {
      return moment(dateString).locale('ar').format('YYYY/MM/DD');
    } else {
      return momentHijri(dateString).locale('ar').format('iYYYY/iMM/iDD');
    }
  };

  // Format currency
  const formatCurrencyLocal = (amount) => {
    return formatCurrency(amount);
  };



  // Get family name by ID
  const getFamilyName = (familyId) => {
    if (!familyId) return '';
    const family = families.find(f => f.id === familyId);
    return family ? family.name : '';
  };

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setNewMember({ name: '', phone: '', email: '', address: '', familyId: '' });
    setErrors({});
  };

  const validateForm = () => {
    const newErrors = {};
    if (!newMember.name.trim()) {
      newErrors.name = 'اسم العضو مطلوب';
    }
    
    if (newMember.phone && !/^\d{10}$/.test(newMember.phone.trim())) {
      newErrors.phone = 'رقم الهاتف يجب أن يكون 10 أرقام';
    }
    
    if (newMember.email && !/\S+@\S+\.\S+/.test(newMember.email.trim())) {
      newErrors.email = 'صيغة البريد الإلكتروني غير صحيحة';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      // الحصول على معرف الصندوق الحالي
      const currentFundId = localStorage.getItem('current_fund_id') || 'default';
      
      // إضافة العضو للصندوق الحالي فقط
      addMember(newMember);
      
      // تحديث إحصائيات العائلة إذا كان العضو ينتمي لعائلة
      if (newMember.familyId) {
        updateFamilyStats(newMember.familyId, currentFundId);
      }
      
      handleClose();
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewMember({ ...newMember, [name]: value });
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({ ...errors, [name]: undefined });
    }
  };

  const handleViewMember = (id) => {
    try {
      console.log('=== Member View Debug ===');
      console.log('Member ID:', id, 'Type:', typeof id);
      console.log('Current location:', location.pathname);
      console.log('All members:', members.map(m => ({ id: m.id, idType: typeof m.id, name: m.name })));

      // التأكد من وجود العضو قبل التنقل
      const memberExists = members.find(m => m.id === id || m.id === String(id) || String(m.id) === id);
      console.log('Member exists:', !!memberExists);
      console.log('Member details:', memberExists ? { id: memberExists.id, name: memberExists.name } : 'Not found');

      if (memberExists) {
        console.log('Navigating to member details...');
        navigate(`/member/${id}`);

        // تأكيد التنقل
        setTimeout(() => {
          console.log('Current URL after navigate:', window.location.pathname);
        }, 100);
      } else {
        console.error('Member not found in members list');
        alert('العضو غير موجود في القائمة');
      }

    } catch (error) {
      console.error('Error navigating to member details:', error);
      alert('حدث خطأ أثناء فتح تفاصيل العضو');
    }
  };

  // Open menu
  const handleMenuOpen = (event, memberId) => {
    setMenuAnchorEl(event.currentTarget);
    setCurrentMemberId(memberId);
  };

  // Close menu
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
    // لا نقوم بإعادة تعيين معرف العضو هنا لضمان استمرار توفره للعمليات اللاحقة
    // setCurrentMemberId(null);
  };

  // Edit member
  const handleEditMember = () => {
    handleMenuClose();
    const member = members.find(m => m.id === currentMemberId);
    if (member) {
      setEditMember({
        name: member.name,
        phone: member.phone || '',
        email: member.email || '',
        address: member.address || '',
        familyId: member.familyId || ''
      });
      setIsEditing(true);
      setCurrentMemberId(member.id);
      setEditDialogOpen(true);
    }
  };

  // Open delete confirmation
  const handleDeleteConfirmOpen = () => {
    handleMenuClose();
    setConfirmDeleteOpen(true);
  };

  // Close delete confirmation
  const handleDeleteConfirmClose = () => {
    setConfirmDeleteOpen(false);
    setCurrentMemberId(null);
  };

  // Delete member
  const handleDeleteConfirm = () => {
    try {
      // نسخ معرف العضو إلى متغير محلي لضمان عدم فقدانه
      const memberId = currentMemberId;
      console.log('محاولة حذف العضو برقم:', memberId);
      
      if (memberId && typeof deleteMember === 'function') {
        deleteMember(memberId);
        console.log('تم استدعاء دالة حذف العضو بنجاح');
      } else {
        console.error('دالة حذف العضو غير معرفة أو غير صالحة');
      }
    } catch (error) {
      console.error('حدث خطأ أثناء محاولة حذف العضو:', error);
    } finally {
      setConfirmDeleteOpen(false);
      // إعادة تعيين معرف العضو الحالي - نؤخر هذه الخطوة لضمان اكتمال عملية الحذف
      setTimeout(() => {
        setCurrentMemberId(null);
        setMenuAnchorEl(null);
      }, 100);
    }
  };

  // Handle edit member submit
  const handleEditSubmit = () => {
    const newErrors = {};
    if (!editMember.name.trim()) {
      newErrors.name = 'اسم العضو مطلوب';
    }
    
    if (editMember.phone && !/^\d{10}$/.test(editMember.phone.trim())) {
      newErrors.phone = 'رقم الهاتف يجب أن يكون 10 أرقام';
    }
    
    if (editMember.email && !/\S+@\S+\.\S+/.test(editMember.email.trim())) {
      newErrors.email = 'صيغة البريد الإلكتروني غير صحيحة';
    }
    
    setErrors(newErrors);
    
    if (Object.keys(newErrors).length === 0) {
      updateMember(currentMemberId, editMember);
      setEditDialogOpen(false);
      setEditMember({
        name: '',
        phone: '',
        email: '',
        address: '',
        familyId: ''
      });
      setCurrentMemberId(null);
    }
  };

  // Handle edit member input change
  const handleEditInputChange = (e) => {
    const { name, value } = e.target;
    setEditMember({ ...editMember, [name]: value });

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({ ...errors, [name]: undefined });
    }
  };

  // Handle bulk import
  const handleBulkImport = (membersData) => {
    try {
      const currentFundId = localStorage.getItem('current_fund_id') || 'default';

      // إضافة كل عضو
      membersData.forEach(memberData => {
        addMember(memberData);

        // تحديث إحصائيات العائلة إذا كان العضو ينتمي لعائلة
        if (memberData.familyId) {
          updateFamilyStats(memberData.familyId, currentFundId);
        }
      });

      console.log(`تم استيراد ${membersData.length} عضو بنجاح`);
    } catch (error) {
      console.error('خطأ في استيراد الأعضاء:', error);
    }
  };



  return (
    <Box dir="rtl">
      <Paper elevation={3} sx={{ p: 3, borderRadius: '12px', mb: 4 }}>
        <Box sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          justifyContent: 'space-between',
          alignItems: { xs: 'flex-start', sm: 'center' },
          mb: 3,
          gap: { xs: 2, sm: 0 }
        }}>
          <Typography variant="h5" fontWeight="bold" sx={{ mb: { xs: 1, sm: 0 } }}>
            الأعضاء
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexDirection: { xs: 'column', sm: 'row' } }}>
            <Button
              variant="outlined"
              startIcon={<UploadIcon />}
              onClick={() => setBulkImportOpen(true)}
              sx={{
                borderRadius: '8px',
                px: { xs: 2, sm: 3 },
                py: 1,
                alignSelf: { xs: 'flex-start', sm: 'auto' }
              }}
            >
              استيراد متعدد
            </Button>
            <Button
              variant="contained"
              startIcon={<PersonAddIcon />}
              onClick={handleClickOpen}
              sx={{
                borderRadius: '8px',
                px: { xs: 2, sm: 3 },
                py: 1,
                alignSelf: { xs: 'flex-start', sm: 'auto' }
              }}
            >
              إضافة عضو
            </Button>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 3 }}>
          <TextField
            placeholder="بحث عن عضو..."
            variant="outlined"
            size="small"
            value={search}
            onChange={e => setSearch(e.target.value)}
            sx={{ width: { xs: '100%', sm: 300 } }}
            InputProps={{
              startAdornment: <SearchIcon color="action" sx={{ ml: 1 }} />,
            }}
          />
        </Box>
        
        {/* جدول جميع الأعضاء */}
        <TableContainer
          component={Paper}
          sx={{
            borderRadius: '12px',
            overflow: 'auto',
            maxWidth: '100%',
            overflowX: 'auto',
            '& .MuiTable-root': {
              minWidth: { xs: '320px', sm: '100%' },
              width: '100%'
            }
          }}
        >
          <Table dir="rtl" size="small" stickyHeader sx={{ tableLayout: 'fixed' }}>
            <TableHead sx={{ bgcolor: '#f5f5f5' }}>
              <TableRow>
                <TableCell align="center" sx={{
                  fontWeight: 'bold',
                  fontSize: { xs: '0.7rem', sm: '0.875rem' },
                  padding: { xs: '4px 2px', sm: '8px 8px' },
                  width: { xs: '30px', sm: '40px' }
                }}>#</TableCell>
                <TableCell align="right" sx={{
                  fontWeight: 'bold',
                  fontSize: { xs: '0.7rem', sm: '0.875rem' },
                  padding: { xs: '4px 2px', sm: '8px 8px' },
                  width: { xs: '80px', sm: '120px' }
                }}>الاسم</TableCell>
                <TableCell align="right" sx={{
                  fontWeight: 'bold',
                  display: { xs: 'none', md: 'table-cell' },
                  fontSize: { xs: '0.7rem', sm: '0.875rem' },
                  padding: { xs: '4px 2px', sm: '8px 8px' },
                  width: '100px'
                }}>رقم الهاتف</TableCell>
                <TableCell align="right" sx={{
                  fontWeight: 'bold',
                  display: { xs: 'none', lg: 'table-cell' },
                  fontSize: { xs: '0.7rem', sm: '0.875rem' },
                  padding: { xs: '4px 2px', sm: '8px 8px' },
                  width: '120px'
                }}>البريد الإلكتروني</TableCell>
                <TableCell align="center" sx={{
                  fontWeight: 'bold',
                  fontSize: { xs: '0.7rem', sm: '0.875rem' },
                  padding: { xs: '4px 2px', sm: '8px 8px' },
                  width: { xs: '40px', sm: '60px' }
                }}>العائلة</TableCell>
                <TableCell align="right" sx={{
                  fontWeight: 'bold',
                  display: { xs: 'none', sm: 'table-cell' },
                  fontSize: { xs: '0.7rem', sm: '0.875rem' },
                  padding: { xs: '4px 2px', sm: '8px 8px' },
                  width: '80px'
                }}>تاريخ الإضافة</TableCell>
                <TableCell align="right" sx={{
                  fontWeight: 'bold',
                  fontSize: { xs: '0.7rem', sm: '0.875rem' },
                  padding: { xs: '4px 2px', sm: '8px 8px' },
                  width: { xs: '60px', sm: '80px' }
                }}>الرصيد</TableCell>
                <TableCell align="center" sx={{
                  fontWeight: 'bold',
                  fontSize: { xs: '0.7rem', sm: '0.875rem' },
                  padding: { xs: '4px 2px', sm: '8px 8px' },
                  width: { xs: '60px', sm: '80px' }
                }}>الإجراءات</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {members.filter(member =>
                member.name.includes(search) ||
                member.phone?.includes(search) ||
                member.email?.includes(search)
              ).length > 0 ? (
                members.filter(member =>
                  member.name.includes(search) ||
                  member.phone?.includes(search) ||
                  member.email?.includes(search)
                ).map((member, index) => {
                  const memberFamily = families.find(f => f.id === member.familyId);
                  return (
                    <TableRow key={member.id} sx={{ '&:hover': { bgcolor: '#f9f9f9' } }}>
                      <TableCell align="center" sx={{
                        fontSize: { xs: '0.7rem', sm: '0.875rem' },
                        padding: { xs: '2px', sm: '4px' },
                        fontWeight: 'bold',
                        color: 'primary.main'
                      }}>
                        {index + 1}
                      </TableCell>
                      <TableCell align="right" sx={{
                        fontSize: { xs: '0.7rem', sm: '0.875rem' },
                        padding: { xs: '2px', sm: '4px' },
                        fontWeight: 'bold'
                      }}>
                        <Box sx={{
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          maxWidth: { xs: '70px', sm: '100px' }
                        }}>
                          {member.name}
                        </Box>
                      </TableCell>
                      <TableCell align="right" sx={{
                        display: { xs: 'none', md: 'table-cell' },
                        fontSize: { xs: '0.7rem', sm: '0.875rem' },
                        padding: { xs: '2px', sm: '4px' }
                      }}>
                        <Box sx={{
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          maxWidth: '90px'
                        }}>
                          {member.phone || 'غير متوفر'}
                        </Box>
                      </TableCell>
                      <TableCell align="right" sx={{
                        display: { xs: 'none', lg: 'table-cell' },
                        fontSize: { xs: '0.7rem', sm: '0.875rem' },
                        padding: { xs: '2px', sm: '4px' }
                      }}>
                        <Box sx={{
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          maxWidth: '110px'
                        }}>
                          {member.email || 'غير متوفر'}
                        </Box>
                      </TableCell>
                      <TableCell align="center" sx={{
                        fontSize: { xs: '0.7rem', sm: '0.875rem' },
                        padding: { xs: '2px', sm: '4px' }
                      }}>
                        {memberFamily ? (
                          <Tooltip title={memberFamily.name} arrow>
                            <IconButton size="small" sx={{ color: 'primary.main' }}>
                              <FamilyIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        ) : (
                          <Tooltip title="غير مرتبط بعائلة" arrow>
                            <IconButton size="small" sx={{ color: 'text.secondary' }}>
                              <PersonIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                      </TableCell>
                      <TableCell align="right" sx={{
                        display: { xs: 'none', sm: 'table-cell' },
                        fontSize: { xs: '0.7rem', sm: '0.875rem' },
                        padding: { xs: '2px', sm: '4px' }
                      }}>
                        <Box sx={{
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          maxWidth: '70px'
                        }}>
                          {formatDate(member.createdAt)}
                        </Box>
                      </TableCell>
                      <TableCell align="right" sx={{
                        fontSize: { xs: '0.7rem', sm: '0.875rem' },
                        padding: { xs: '2px', sm: '4px' }
                      }}>
                        <Box sx={{
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          maxWidth: { xs: '50px', sm: '70px' }
                        }}>
                          {(() => {
                            // حساب الرصيد الفعلي من localStorage
                            const currentFundId = localStorage.getItem('current_fund_id') || 'default';
                            const income = JSON.parse(localStorage.getItem(`savings_fund_income_${currentFundId}`) || '[]');
                            const expenses = JSON.parse(localStorage.getItem(`savings_fund_expenses_${currentFundId}`) || '[]');
                            const withdrawals = JSON.parse(localStorage.getItem(`savings_fund_withdrawals_${currentFundId}`) || '[]');
                            const loans = JSON.parse(localStorage.getItem(`savings_fund_loans_${currentFundId}`) || '[]');

                            // حساب الإيرادات للعضو (فردي + نصيبه من الجماعي)
                            let memberIncome = 0;

                            // الإيرادات الفردية
                            memberIncome += income.filter(i => i.memberId === member.id).reduce((sum, i) => sum + (parseFloat(i.amount) || 0), 0);

                            // الإيرادات الجماعية (جميع الأعضاء)
                            const allMembersIncome = income.filter(i => i.incomeType === 'all');
                            memberIncome += allMembersIncome.reduce((sum, i) => sum + (parseFloat(i.amount) || 0) / members.length, 0);

                            // الإيرادات المتعددة (أعضاء محددين)
                            const multipleIncome = income.filter(i => i.incomeType === 'multiple' && i.memberIds && i.memberIds.includes(member.id));
                            memberIncome += multipleIncome.reduce((sum, i) => sum + (parseFloat(i.amount) || 0) / (i.memberIds ? i.memberIds.length : 1), 0);

                            // حساب المصروفات للعضو
                            let memberExpenses = 0;

                            // المصروفات الفردية
                            memberExpenses += expenses.filter(e => e.memberId === member.id).reduce((sum, e) => sum + (parseFloat(e.amount) || 0), 0);

                            // المصروفات الجماعية
                            const allMembersExpenses = expenses.filter(e => e.expenseType === 'all');
                            memberExpenses += allMembersExpenses.reduce((sum, e) => sum + (parseFloat(e.amount) || 0) / members.length, 0);

                            const deductSameExpenses = expenses.filter(e => e.expenseType === 'deduct_same_amount');
                            memberExpenses += deductSameExpenses.reduce((sum, e) => sum + (parseFloat(e.amount) || 0), 0);

                            // المصروفات المتعددة
                            const multipleExpenses = expenses.filter(e => e.expenseType === 'multiple' && e.memberIds && e.memberIds.includes(member.id));
                            memberExpenses += multipleExpenses.reduce((sum, e) => sum + (parseFloat(e.amount) || 0) / (e.memberIds ? e.memberIds.length : 1), 0);

                            const memberWithdrawals = withdrawals.filter(w => w.memberId === member.id).reduce((sum, w) => sum + (parseFloat(w.amount) || 0), 0);
                            const memberLoans = loans.filter(l => l.memberId === member.id && l.status !== 'paid').reduce((sum, l) => sum + (parseFloat(l.remainingAmount || l.amount) || 0), 0);

                            const actualBalance = memberIncome - memberExpenses - memberWithdrawals - memberLoans;

                            return (
                              <Typography
                                variant="body2"
                                fontWeight="bold"
                                color={
                                  actualBalance > 0 ? 'success.main' :
                                  actualBalance < 0 ? 'error.main' :
                                  'text.primary'
                                }
                                sx={{
                                  fontSize: { xs: '0.7rem', sm: '0.8rem' },
                                  textShadow: '0 1px 2px rgba(0,0,0,0.1)'
                                }}
                              >
                                {formatCurrencyLocal(actualBalance)}
                              </Typography>
                            );
                          })()}
                        </Box>
                      </TableCell>
                      <TableCell align="center" sx={{
                        padding: { xs: '2px', sm: '4px' }
                      }}>
                        <Box sx={{
                          display: 'flex',
                          justifyContent: 'center',
                          gap: 0.2
                        }}>
                          <IconButton
                            color="primary"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              console.log('Button clicked for member:', member.id);
                              handleViewMember(member.id);
                            }}
                            sx={{
                              '&:hover': { transform: 'scale(1.1)' },
                              padding: { xs: '2px', sm: '4px' }
                            }}
                            title="عرض تفاصيل العضو"
                            size="small"
                          >
                            <ViewIcon sx={{ fontSize: { xs: '16px', sm: '18px' } }} />
                          </IconButton>
                          <IconButton
                            color="default"
                            onClick={(event) => handleMenuOpen(event, member.id)}
                            sx={{
                              '&:hover': { transform: 'scale(1.1)' },
                              padding: { xs: '2px', sm: '4px' }
                            }}
                            title="المزيد من الخيارات"
                            size="small"
                          >
                            <MoreVertIcon sx={{ fontSize: { xs: '16px', sm: '18px' } }} />
                          </IconButton>
                        </Box>
                      </TableCell>
                    </TableRow>
                  );
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={8} align="center" sx={{ py: 3 }}>
                    <Typography variant="body1" color="text.secondary">
                      {search ? 'لا توجد نتائج للبحث' : 'لا يوجد أعضاء'}
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>

      </Paper>

      {/* Menu for member actions */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem onClick={handleEditMember}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <Typography variant="inherit">تعديل</Typography>
        </MenuItem>
        <MenuItem onClick={handleDeleteConfirmOpen}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <Typography variant="inherit" color="error">حذف</Typography>
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={confirmDeleteOpen}
        onClose={handleDeleteConfirmClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        dir="rtl"
      >
        <DialogTitle id="alert-dialog-title" sx={{ fontWeight: 'bold', color: 'error.main' }}>
          تأكيد حذف العضو
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            هل أنت متأكد من رغبتك في حذف هذا العضو؟ هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع بيانات العضو.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteConfirmClose} color="inherit">
            إلغاء
          </Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained" autoFocus>
            حذف
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Member Dialog */}
      <Dialog 
        open={editDialogOpen} 
        onClose={() => setEditDialogOpen(false)} 
        fullWidth 
        maxWidth="md" 
        dir="rtl"
      >
        <DialogTitle sx={{ 
          bgcolor: 'primary.main', 
          color: 'white', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          borderBottom: '1px solid',
          borderColor: 'divider' 
        }}>
          <IconButton 
            aria-label="close" 
            onClick={() => setEditDialogOpen(false)} 
            sx={{ color: 'white' }}
          >
            <CloseIcon />
          </IconButton>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <EditIcon sx={{ mr: 1 }} />
            <Typography variant="h6" fontWeight="bold">
              تعديل بيانات العضو
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ mt: 2, pt: 2 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={12} md={6}>
              <TextField
                fullWidth
                autoFocus
                name="name"
                label="اسم العضو"
                variant="outlined"
                value={editMember.name}
                onChange={handleEditInputChange}
                error={!!errors.name}
                helperText={errors.name}
                inputProps={{ style: { textAlign: 'right' } }}
                InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
              />
            </Grid>
            <Grid item xs={12} sm={12} md={6}>
              <TextField
                fullWidth
                name="phone"
                label="رقم الهاتف"
                variant="outlined"
                value={editMember.phone}
                onChange={handleEditInputChange}
                error={!!errors.phone}
                helperText={errors.phone}
                inputProps={{ style: { textAlign: 'right' } }}
                InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
              />
            </Grid>
            <Grid item xs={12} sm={12} md={6}>
              <TextField
                fullWidth
                name="email"
                label="البريد الإلكتروني"
                variant="outlined"
                value={editMember.email}
                onChange={handleEditInputChange}
                error={!!errors.email}
                helperText={errors.email}
                inputProps={{ style: { textAlign: 'right' } }}
                InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
              />
            </Grid>
            <Grid item xs={12} sm={12} md={6}>
              <FormControl fullWidth>
                <InputLabel id="family-label" sx={{ right: 15, left: 'auto', transformOrigin: 'right' }}>العائلة</InputLabel>
                <Select
                  labelId="family-label"
                  name="familyId"
                  value={editMember.familyId}
                  onChange={handleEditInputChange}
                  label="العائلة"
                  sx={{ textAlign: 'right' }}
                >
                  <MenuItem value="">لا توجد عائلة</MenuItem>
                  {families.map((family) => (
                    <MenuItem key={family.id} value={family.id}>{family.name}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                name="address"
                label="العنوان"
                variant="outlined"
                multiline
                rows={3}
                value={editMember.address}
                onChange={handleEditInputChange}
                inputProps={{ style: { textAlign: 'right' } }}
                InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ 
          p: { xs: 1, sm: 2 }, 
          justifyContent: { xs: 'center', sm: 'flex-start' },
          flexDirection: { xs: 'column', sm: 'row' },
          gap: { xs: 1, sm: 0 }
        }}>
          <Button 
            onClick={() => setEditDialogOpen(false)} 
            color="inherit" 
            sx={{ 
              ml: { xs: 0, sm: 2 }, 
              mr: { xs: 0, sm: 0 },
              width: { xs: '100%', sm: 'auto' }
            }}
          >
            إلغاء
          </Button>
          <Button 
            onClick={handleEditSubmit} 
            variant="contained" 
            color="primary"
            sx={{ 
              width: { xs: '100%', sm: 'auto' }
            }}
          >
            حفظ التغييرات
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog open={open} onClose={handleClose} fullWidth maxWidth="md" dir="rtl" sx={{
        '& .MuiDialog-paper': {
          width: { xs: '95%', sm: '80%', md: '70%' },
          margin: { xs: '16px', sm: '32px' },
          maxHeight: { xs: 'calc(100% - 32px)', sm: 'calc(100% - 64px)' }
        }
      }}>
        <DialogTitle sx={{ 
          bgcolor: 'primary.main', 
          color: 'white', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          borderBottom: '1px solid',
          borderColor: 'divider' 
        }}>
          <IconButton 
            aria-label="close" 
            onClick={handleClose} 
            sx={{ color: 'white' }}
          >
            <CloseIcon />
          </IconButton>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <PersonAddIcon sx={{ mr: 1 }} />
            <Typography variant="h6" fontWeight="bold">
              إضافة عضو جديد
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ mt: 2, pt: 2 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={12} md={6}>
              <TextField
                fullWidth
                autoFocus
                name="name"
                label="اسم العضو"
                variant="outlined"
                value={newMember.name}
                onChange={handleInputChange}
                error={!!errors.name}
                helperText={errors.name}
                inputProps={{ style: { textAlign: 'right' } }}
                InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
              />
            </Grid>
            <Grid item xs={12} sm={12} md={6}>
              <TextField
                fullWidth
                name="phone"
                label="رقم الهاتف"
                variant="outlined"
                value={newMember.phone}
                onChange={handleInputChange}
                error={!!errors.phone}
                helperText={errors.phone}
                inputProps={{ style: { textAlign: 'right' } }}
                InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
              />
            </Grid>
            <Grid item xs={12} sm={12} md={6}>
              <TextField
                fullWidth
                name="email"
                label="البريد الإلكتروني"
                variant="outlined"
                value={newMember.email}
                onChange={handleInputChange}
                error={!!errors.email}
                helperText={errors.email}
                inputProps={{ style: { textAlign: 'right' } }}
                InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
              />
            </Grid>
            <Grid item xs={12} sm={12} md={6}>
              <FormControl fullWidth>
                <InputLabel id="family-label" sx={{ right: 15, left: 'auto', transformOrigin: 'right' }}>العائلة</InputLabel>
                <Select
                  labelId="family-label"
                  name="familyId"
                  value={newMember.familyId}
                  onChange={handleInputChange}
                  label="العائلة"
                  sx={{ textAlign: 'right' }}
                >
                  <MenuItem value="">لا توجد عائلة</MenuItem>
                  {families.map((family) => (
                    <MenuItem key={family.id} value={family.id}>{family.name}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                name="address"
                label="العنوان"
                variant="outlined"
                multiline
                rows={3}
                value={newMember.address}
                onChange={handleInputChange}
                inputProps={{ style: { textAlign: 'right' } }}
                InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ 
          p: { xs: 1, sm: 2 }, 
          justifyContent: { xs: 'center', sm: 'flex-start' },
          flexDirection: { xs: 'column', sm: 'row' },
          gap: { xs: 1, sm: 0 }
        }}>
          <Button 
            onClick={handleClose} 
            color="inherit" 
            sx={{ 
              ml: { xs: 0, sm: 2 }, 
              mr: { xs: 0, sm: 0 },
              width: { xs: '100%', sm: 'auto' }
            }}
          >
            إلغاء
          </Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained" 
            color="primary"
            sx={{ 
              width: { xs: '100%', sm: 'auto' }
            }}
          >
            إضافة
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add to Family Dialog */}
      <Dialog
        open={addToFamilyDialogOpen}
        onClose={handleAddToFamilyClose}
        aria-labelledby="add-to-family-dialog-title"
        aria-describedby="add-to-family-dialog-description"
        dir="rtl"
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle id="add-to-family-dialog-title" sx={{ 
          bgcolor: 'primary.main', 
          color: 'white', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          borderBottom: '1px solid',
          borderColor: 'divider' 
        }}>
          <IconButton 
            aria-label="close" 
            onClick={handleAddToFamilyClose} 
            sx={{ color: 'white' }}
          >
            <CloseIcon />
          </IconButton>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <FamilyIcon sx={{ mr: 1 }} />
            <Typography variant="h6" fontWeight="bold">
              إضافة العضو إلى عائلة
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ mt: 2, pt: 2 }}>
          <Typography variant="body1" sx={{ mb: 2 }}>
            اختر العائلة التي تريد إضافة العضو إليها:
          </Typography>
          <FormControl fullWidth error={!!errors.selectedFamilyId}>
            <InputLabel id="family-selection-label" sx={{ right: 15, left: 'auto', transformOrigin: 'right' }}>العائلة</InputLabel>
            <Select
              labelId="family-selection-label"
              value={selectedFamilyId}
              onChange={handleFamilySelectionChange}
              label="العائلة"
              sx={{ textAlign: 'right' }}
            >
              <MenuItem value="">اختر عائلة</MenuItem>
              {families.map((family) => (
                <MenuItem key={family.id} value={family.id}>{family.name}</MenuItem>
              ))}
            </Select>
            {errors.selectedFamilyId && <FormHelperText>{errors.selectedFamilyId}</FormHelperText>}
          </FormControl>
        </DialogContent>
        <DialogActions sx={{ 
          p: { xs: 1, sm: 2 }, 
          justifyContent: { xs: 'center', sm: 'flex-start' },
          flexDirection: { xs: 'column', sm: 'row' },
          gap: { xs: 1, sm: 0 }
        }}>
          <Button 
            onClick={handleAddToFamilyClose} 
            color="inherit" 
            sx={{ 
              ml: { xs: 0, sm: 2 }, 
              mr: { xs: 0, sm: 0 },
              width: { xs: '100%', sm: 'auto' }
            }}
          >
            إلغاء
          </Button>
          <Button 
            onClick={handleAddMemberToFamily} 
            variant="contained" 
            color="primary"
            sx={{ 
              width: { xs: '100%', sm: 'auto' }
            }}
          >
            إضافة إلى العائلة
          </Button>
        </DialogActions>
      </Dialog>

      {/* Bulk Import Dialog */}
      <BulkImport
        open={bulkImportOpen}
        onClose={() => setBulkImportOpen(false)}
        onImport={handleBulkImport}
        families={families}
      />

      {/* Copyright */}
      <Copyright />
    </Box>
  );
};


export default MembersManagement;
