@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

html {
  direction: rtl;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  font-family: '<PERSON><PERSON><PERSON>', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  direction: rtl;
  text-align: right;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* MUI adjustments for RTL */
.MuiFormControl-root {
  text-align: right;
}

.MuiInputBase-input {
  text-align: right;
}

.MuiTableCell-root {
  text-align: right;
}

/* Arabic number class */
.arabic-number {
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Animation effects */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Mobile Responsive Global Styles */
@media (max-width: 768px) {
  /* Reduce font sizes for mobile */
  .MuiTypography-h4 {
    font-size: 1.5rem !important;
  }

  .MuiTypography-h5 {
    font-size: 1.25rem !important;
  }

  .MuiTypography-h6 {
    font-size: 1.1rem !important;
  }

  /* Reduce button sizes */
  .MuiButton-root {
    padding: 6px 12px !important;
    font-size: 0.8rem !important;
    min-height: 36px !important;
  }

  /* Reduce card padding */
  .MuiCard-root {
    margin: 4px !important;
  }

  .MuiCardContent-root {
    padding: 12px !important;
  }

  /* Reduce table cell padding */
  .MuiTableCell-root {
    padding: 8px !important;
    font-size: 0.8rem !important;
  }

  /* Reduce form field sizes */
  .MuiTextField-root {
    margin: 4px 0 !important;
  }

  .MuiInputBase-root {
    font-size: 0.9rem !important;
  }
}

@media (max-width: 480px) {
  /* Extra small screens */
  .MuiTypography-h4 {
    font-size: 1.3rem !important;
  }

  .MuiTypography-h5 {
    font-size: 1.1rem !important;
  }

  .MuiButton-root {
    padding: 4px 8px !important;
    font-size: 0.75rem !important;
    min-height: 32px !important;
  }

  .MuiCardContent-root {
    padding: 8px !important;
  }

  .MuiTableCell-root {
    padding: 4px !important;
    font-size: 0.75rem !important;
  }

  /* Ensure tables are fully responsive */
  .MuiTableContainer-root {
    overflow-x: auto !important;
    max-width: 100% !important;
  }

  .MuiTable-root {
    min-width: 300px !important;
  }

  /* Fix button positioning */
  .MuiButton-root {
    z-index: 1 !important;
    position: relative !important;
  }

  /* Ensure proper spacing for mobile sidebar */
  .MuiBox-root {
    box-sizing: border-box !important;
  }
}

/* Print Styles */
@media print {
  @page {
    size: A4;
    margin: 0.5in;
  }

  body {
    font-size: 12px !important;
    line-height: 1.3 !important;
  }

  /* Hide non-essential elements during print */
  .no-print {
    display: none !important;
  }

  /* Compact spacing for print */
  .MuiPaper-root {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
  }

  .MuiGrid-container {
    margin: 0 !important;
  }

  .MuiGrid-item {
    padding: 2px !important;
  }

  /* Table optimizations */
  .MuiTable-root {
    font-size: 10px !important;
  }

  .MuiTableCell-root {
    padding: 2px 4px !important;
    border: 1px solid #ccc !important;
  }

  /* Prevent page breaks inside tables */
  .MuiTableContainer-root {
    page-break-inside: avoid;
  }

  .MuiTableRow-root {
    page-break-inside: avoid;
    page-break-after: auto;
  }
}
