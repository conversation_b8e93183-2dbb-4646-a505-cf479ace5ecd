param([string]$Description = "update")

$DateTime = Get-Date -Format "yyyy-MM-dd_HH-mm"
$SourceAPK = "android\app\build\outputs\apk\debug\app-debug.apk"

if (!(Test-Path "releases")) {
    New-Item -ItemType Directory -Path "releases"
}

$ExistingAPKs = Get-ChildItem "releases" -Filter "savings-fund-v*.apk" | Measure-Object
$VersionNumber = $ExistingAPKs.Count + 1

$NewName = "savings-fund-v1.$VersionNumber.0-$DateTime-$Description.apk"
$TargetAPK = "releases\$NewName"

Copy-Item $SourceAPK $TargetAPK

Write-Host "تم إنشاء APK جديد: $NewName" -ForegroundColor Green
Write-Host "المسار: $TargetAPK" -ForegroundColor Yellow

Get-ChildItem "releases" -Filter "*.apk" | Sort-Object LastWriteTime -Descending
