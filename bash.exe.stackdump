Stack trace:
Frame         Function      Args
0007FFFF75F0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF64F0) msys-2.0.dll+0x1FE8E
0007FFFF75F0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF78C8) msys-2.0.dll+0x67F9
0007FFFF75F0  000210046832 (000210286019, 0007FFFF74A8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF75F0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF75F0  000210068E24 (0007FFFF7600, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF78D0  00021006A225 (0007FFFF7600, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA7DDE0000 ntdll.dll
7FFA7BDC0000 KERNEL32.DLL
7FFA7B410000 KERNELBASE.dll
7FFA7C070000 USER32.dll
7FFA7B200000 win32u.dll
7FFA7D530000 GDI32.dll
7FFA7B800000 gdi32full.dll
7FFA7B150000 msvcp_win.dll
7FFA7B2C0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA7CA30000 advapi32.dll
7FFA7BB80000 msvcrt.dll
7FFA7D5B0000 sechost.dll
7FFA7BCA0000 RPCRT4.dll
7FFA7A650000 CRYPTBASE.DLL
7FFA7B0B0000 bcryptPrimitives.dll
7FFA7C030000 IMM32.DLL
