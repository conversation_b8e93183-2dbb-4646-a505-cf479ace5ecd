import React, { useState, useRef, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Box, 
  Paper, 
  Typography, 
  Divider, 
  Grid, 
  Button, 
  Tabs, 
  Tab, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow,
  Card,
  CardContent,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment,
  FormHelperText,
  Tooltip,
  Chip,
  Collapse
} from '@mui/material';
import { 
  Person as PersonIcon,
  ArrowBack as ArrowBackIcon,
  Print as PrintIcon,
  TrendingUp as IncomeIcon,
  TrendingDown as ExpenseIcon,
  Receipt as ReceiptIcon,
  CalendarToday as DateIcon,
  AccountBalance as BankIcon,
  EventNote as EventIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  History as HistoryIcon,
  MoneyOff as WithdrawIcon,
  CreditCard as LoanIcon,
  AttachMoney as MoneyIcon,
  Schedule as ScheduleIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationOnIcon,
  FamilyRestroom as FamilyIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material';
import { FormLabel, RadioGroup, FormControlLabel, Radio } from '@mui/material';
import { useReactToPrint } from 'react-to-print';
import moment from 'moment';
import momentHijri from 'moment-hijri';
import Copyright from '../common/Copyright';
import { formatCurrency as formatCurrencyUtil, formatCurrencyWithSign, formatDate as formatDateUtil, calculateLoanTimeRemaining, generateLoanPaymentSchedule } from '../../utils/formatters';

const MemberDetails = ({ members, income, expenses, dateType, addIncome, addExpense, updateIncome, deleteIncome, updateExpense, deleteExpense }) => {
  const { id } = useParams();
  // تعريف متغيرات للسحوبات والسلف
  const [withdrawals, setWithdrawals] = useState(() => {
    const withdrawalsKey = `savings_fund_withdrawals_${localStorage.getItem('current_fund_id') || 'default'}`;
    const storedWithdrawals = JSON.parse(localStorage.getItem(withdrawalsKey) || '[]');
    return storedWithdrawals.filter(w => w.memberId === id);
  });
  const [loans, setLoans] = useState(() => {
    const loansKey = `savings_fund_loans_${localStorage.getItem('current_fund_id') || 'default'}`;
    const storedLoans = JSON.parse(localStorage.getItem(loansKey) || '[]');
    return storedLoans.filter(l => l.memberId === id);
  });
  const navigate = useNavigate();
  const [member, setMember] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [openIncomeDialog, setOpenIncomeDialog] = useState(false);
  const [openExpenseDialog, setOpenExpenseDialog] = useState(false);
  const [openWithdrawalDialog, setOpenWithdrawalDialog] = useState(false);
  const [openLoanDialog, setOpenLoanDialog] = useState(false);
  const [financialSummaryOpen, setFinancialSummaryOpen] = useState(false);
  const [newIncome, setNewIncome] = useState({
    amount: '',
    date: new Date().toISOString().split('T')[0],
    notes: ''
  });
  const [newExpense, setNewExpense] = useState({
    amount: '',
    date: new Date().toISOString().split('T')[0],
    category: '',
    notes: ''
  });
  const [newWithdrawal, setNewWithdrawal] = useState({
    amount: '',
    date: new Date().toISOString().split('T')[0],
    notes: ''
  });
  const [newLoan, setNewLoan] = useState({
    amount: '',
    date: new Date().toISOString().split('T')[0],
    repaymentPeriod: '12', // بالأشهر
    paymentType: 'monthly', // monthly أو single
    notes: '',
    status: 'active' // active, paid, overdue
  });
  const [errors, setErrors] = useState({});
  const [confirmDeleteDialog, setConfirmDeleteDialog] = useState({
    open: false,
    type: '',
    id: null
  });
  const printRef = useRef();

  // الطباعة
  const handlePrint = useReactToPrint({
    content: () => printRef.current,
  });

  // تحميل بيانات العضو
  useEffect(() => {
    console.log('=== MemberDetails Loading ===');
    console.log('Member ID from params:', id, 'Type:', typeof id);
    console.log('Available members:', members.length);
    console.log('Members details:', members.map(m => ({ id: m.id, idType: typeof m.id, name: m.name })));

    if (members.length === 0) {
      console.log('Members not loaded yet, waiting...');
      return; // انتظار تحميل الأعضاء
    }

    // البحث بمقارنة النص والرقم
    const foundMember = members.find(m => m.id === id || m.id === String(id) || String(m.id) === id);
    console.log('Found member:', foundMember);

    if (foundMember) {
      setMember(foundMember);
      console.log('Member set successfully:', foundMember.name);
    } else {
      console.error('Member not found with ID:', id);
      console.log('Available member IDs:', members.map(m => ({ id: m.id, type: typeof m.id })));

      // محاولة أخيرة بالبحث في localStorage مباشرة
      const currentFundId = localStorage.getItem('current_fund_id') || 'default';
      const membersKey = `savings_fund_members_${currentFundId}`;
      const storedMembers = JSON.parse(localStorage.getItem(membersKey) || '[]');
      console.log('Stored members from localStorage:', storedMembers.map(m => ({ id: m.id, name: m.name })));

      const storedMember = storedMembers.find(m => m.id === id || m.id === String(id) || String(m.id) === id);
      if (storedMember) {
        console.log('Found member in localStorage:', storedMember);
        setMember(storedMember);
      } else {
        console.log('Member not found anywhere, redirecting to /members');
        navigate('/members');
      }
    }
  }, [id, members, navigate]);
  
  // تحديث بيانات العضو عند تغيير السحوبات أو السلف أو الإيرادات أو المصروفات
  useEffect(() => {
    // تحديث واجهة المستخدم بعد تغيير البيانات المالية
    if (member) {
      // الحصول على أحدث بيانات العضو من localStorage
      const currentFundId = localStorage.getItem('current_fund_id') || 'default';
      const membersKey = `savings_fund_members_${currentFundId}`;
      const storedMembers = JSON.parse(localStorage.getItem(membersKey) || '[]');
      const updatedMember = storedMembers.find(m => m.id === id);

      if (updatedMember) {
        setMember(updatedMember);
      }
    }
  }, [withdrawals, loans, id, income, expenses]);

  // تحديث الرصيد عند تغيير أي من المعاملات المالية
  useEffect(() => {
    if (member) {
      updateMemberTotalBalance();
    }
  }, [income, expenses, withdrawals, loans]);

  // مراقبة تغييرات localStorage وتحديث البيانات
  useEffect(() => {
    const updateDataFromStorage = () => {
      const currentFundId = localStorage.getItem('current_fund_id') || 'default';

      // تحديث السحوبات
      const withdrawalsKey = `savings_fund_withdrawals_${currentFundId}`;
      const latestWithdrawals = JSON.parse(localStorage.getItem(withdrawalsKey) || '[]');
      const memberWithdrawals = latestWithdrawals.filter(w => w.memberId === id);
      setWithdrawals(memberWithdrawals);

      // تحديث السلف
      const loansKey = `savings_fund_loans_${currentFundId}`;
      const latestLoans = JSON.parse(localStorage.getItem(loansKey) || '[]');
      const memberLoans = latestLoans.filter(l => l.memberId === id);
      setLoans(memberLoans);
    };

    // تحديث فوري
    updateDataFromStorage();

    // تحديث دوري كل ثانيتين
    const interval = setInterval(updateDataFromStorage, 2000);

    return () => clearInterval(interval);
  }, [id]);


  // تنسيق التاريخ
  const formatDate = (dateString) => {
    if (dateType === 'gregorian') {
      return moment(dateString).locale('ar').format('YYYY/MM/DD');
    } else {
      return momentHijri(dateString).locale('ar').format('iYYYY/iMM/iDD');
    }
  };

  // تنسيق العملة - استخدام الدالة المحسنة
  const formatCurrency = (amount) => {
    return formatCurrencyUtil(amount);
  };

  // تغيير التبويب
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // فتح نافذة الإيرادات
  const handleOpenIncomeDialog = () => {
    setOpenIncomeDialog(true);
    setErrors({});
  };

  // إغلاق نافذة الإيرادات
  const handleCloseIncomeDialog = () => {
    setOpenIncomeDialog(false);
    setNewIncome({
      amount: '',
      date: new Date().toISOString().split('T')[0],
      notes: ''
    });
  };

  // فتح نافذة المصروفات
  const handleOpenExpenseDialog = () => {
    setOpenExpenseDialog(true);
    setErrors({});
  };

  // إغلاق نافذة المصروفات
  const handleCloseExpenseDialog = () => {
    setOpenExpenseDialog(false);
    setNewExpense({
      amount: '',
      date: new Date().toISOString().split('T')[0],
      category: '',
      notes: ''
    });
  };

  // فتح نافذة السحوبات
  const handleOpenWithdrawalDialog = () => {
    setOpenWithdrawalDialog(true);
    setErrors({});
  };

  // إغلاق نافذة السحوبات
  const handleCloseWithdrawalDialog = () => {
    setOpenWithdrawalDialog(false);
    setNewWithdrawal({
      amount: '',
      date: new Date().toISOString().split('T')[0],
      notes: ''
    });
  };

  // فتح نافذة السلف
  const handleOpenLoanDialog = () => {
    setOpenLoanDialog(true);
    setErrors({});
  };

  // إغلاق نافذة السلف
  const handleCloseLoanDialog = () => {
    setOpenLoanDialog(false);
    setNewLoan({
      amount: '',
      date: new Date().toISOString().split('T')[0],
      repaymentPeriod: '12',
      paymentType: 'monthly',
      notes: '',
      status: 'active'
    });
  };

  // التحقق من صحة نموذج السحوبات
  const validateWithdrawalForm = () => {
    const newErrors = {};
    if (!newWithdrawal.amount) {
      newErrors.amount = 'يرجى إدخال المبلغ';
    } else if (isNaN(newWithdrawal.amount) || parseFloat(newWithdrawal.amount) <= 0) {
      newErrors.amount = 'يرجى إدخال مبلغ صحيح';
    }
    if (!newWithdrawal.date) {
      newErrors.date = 'يرجى إدخال التاريخ';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // إضافة سحب جديد
  const handleAddWithdrawal = () => {
    if (validateWithdrawalForm()) {
      const withdrawalData = {
        id: `withdrawal_${Date.now()}`,
        memberId: id,
        amount: parseFloat(newWithdrawal.amount),
        date: newWithdrawal.date,
        notes: newWithdrawal.notes,
        createdAt: new Date().toISOString()
      };

      // إضافة السحب إلى التخزين المحلي
      const withdrawalsKey = `savings_fund_withdrawals_${localStorage.getItem('current_fund_id') || 'default'}`;
      const storedWithdrawals = JSON.parse(localStorage.getItem(withdrawalsKey) || '[]');
      const updatedWithdrawals = [...storedWithdrawals, withdrawalData];
      localStorage.setItem(withdrawalsKey, JSON.stringify(updatedWithdrawals));
      
      // تحديث حالة السحوبات
      setWithdrawals(prev => [...prev, withdrawalData]);
      
      // تحديث بيانات العضو في واجهة المستخدم
      updateMemberTotalBalance();
      
      handleCloseWithdrawalDialog();
    }
  };

  // التحقق من صحة نموذج السلف
  const validateLoanForm = () => {
    const newErrors = {};
    if (!newLoan.amount) {
      newErrors.amount = 'يرجى إدخال المبلغ';
    } else if (isNaN(newLoan.amount) || parseFloat(newLoan.amount) <= 0) {
      newErrors.amount = 'يرجى إدخال مبلغ صحيح';
    }
    if (!newLoan.date) {
      newErrors.date = 'يرجى إدخال التاريخ';
    }
    if (!newLoan.repaymentPeriod) {
      newErrors.repaymentPeriod = 'يرجى إدخال فترة السداد';
    } else if (isNaN(newLoan.repaymentPeriod) || parseInt(newLoan.repaymentPeriod) <= 0) {
      newErrors.repaymentPeriod = 'يرجى إدخال فترة سداد صحيحة';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // إضافة سلفة جديدة
  const handleAddLoan = () => {
    if (validateLoanForm()) {
      // إنشاء جدول الدفعات
      const paymentSchedule = generateLoanPaymentSchedule(
        newLoan.amount,
        newLoan.date,
        newLoan.paymentType,
        newLoan.repaymentPeriod
      );

      const loanData = {
        id: `loan_${Date.now()}`,
        memberId: id,
        amount: parseFloat(newLoan.amount),
        originalAmount: parseFloat(newLoan.amount),
        remainingAmount: parseFloat(newLoan.amount),
        date: newLoan.date,
        repaymentPeriod: parseInt(newLoan.repaymentPeriod),
        paymentType: newLoan.paymentType,
        notes: newLoan.notes,
        status: newLoan.status,
        paymentSchedule: paymentSchedule,
        createdAt: new Date().toISOString()
      };

      // إضافة السلفة إلى التخزين المحلي
      const loansKey = `savings_fund_loans_${localStorage.getItem('current_fund_id') || 'default'}`;
      const storedLoans = JSON.parse(localStorage.getItem(loansKey) || '[]');
      const updatedLoans = [...storedLoans, loanData];
      localStorage.setItem(loansKey, JSON.stringify(updatedLoans));

      // تحديث حالة السلف
      setLoans(prev => [...prev, loanData]);

      // تحديث بيانات العضو في واجهة المستخدم
      updateMemberTotalBalance();

      handleCloseLoanDialog();
    }
  };

  // حذف سحب
  const handleDeleteWithdrawal = (withdrawalId) => {
    // فتح مربع حوار التأكيد
    setConfirmDeleteDialog({
      open: true,
      type: 'withdrawal',
      id: withdrawalId
    });
  };

  // تسديد السلفة
  const handleLoanPayment = (loanId, paymentType = 'full') => {
    const currentFundId = localStorage.getItem('current_fund_id') || 'default';
    const loansKey = `savings_fund_loans_${currentFundId}`;
    const storedLoans = JSON.parse(localStorage.getItem(loansKey) || '[]');
    const loan = storedLoans.find(l => l.id === loanId);

    if (!loan) return;

    let paymentAmount = 0;

    if (paymentType === 'full') {
      // تسديد كامل
      paymentAmount = loan.remainingAmount || loan.amount;
    } else if (paymentType === 'partial') {
      // دفع دفعة واحدة
      if (loan.paymentType === 'monthly') {
        const monthlyPayment = (loan.originalAmount || loan.amount) / (loan.repaymentPeriod || 12);
        paymentAmount = Math.min(monthlyPayment, loan.remainingAmount || loan.amount);
      } else {
        paymentAmount = loan.remainingAmount || loan.amount;
      }
    }

    // العثور على السلفة وتحديث حالتها
    const updatedLoans = storedLoans.map(loanItem => {
      if (loanItem.id === loanId) {
        const newRemainingAmount = (loanItem.remainingAmount || loanItem.amount) - paymentAmount;
        const isFullyPaid = newRemainingAmount <= 0;

        return {
          ...loanItem,
          remainingAmount: Math.max(0, newRemainingAmount),
          status: isFullyPaid ? 'paid' : 'active',
          lastPaymentDate: new Date().toISOString(),
          ...(isFullyPaid && { paidDate: new Date().toISOString().split('T')[0] }),
          payments: [
            ...(loanItem.payments || []),
            {
              id: `payment_${Date.now()}`,
              amount: paymentAmount,
              date: new Date().toISOString(),
              type: paymentType
            }
          ]
        };
      }
      return loanItem;
    });

    // حفظ التحديث في localStorage
    localStorage.setItem(loansKey, JSON.stringify(updatedLoans));

    // تحديث حالة السلف في الواجهة
    setLoans(prev => prev.map(loanItem =>
      loanItem.id === loanId
        ? updatedLoans.find(l => l.id === loanId)
        : loanItem
    ));

    // تحديث رصيد العضو (إضافة مبلغ السلفة المسددة إلى الرصيد)
    updateMemberTotalBalance();
  };

  // حذف سلفة
  const handleDeleteLoan = (loanId) => {
    // فتح مربع حوار التأكيد
    setConfirmDeleteDialog({
      open: true,
      type: 'loan',
      id: loanId
    });
  };

  // تأكيد الحذف
  const handleConfirmDelete = () => {
    const { type, id } = confirmDeleteDialog;
    const currentFundId = localStorage.getItem('current_fund_id') || 'default';
    
    if (type === 'withdrawal') {
      // البحث عن السحب المراد حذفه للحصول على المبلغ
      const withdrawalsKey = `savings_fund_withdrawals_${currentFundId}`;
      const storedWithdrawals = JSON.parse(localStorage.getItem(withdrawalsKey) || '[]');
      
      // حذف السحب من التخزين المحلي
      const updatedWithdrawals = storedWithdrawals.filter(item => item.id !== id);
      localStorage.setItem(withdrawalsKey, JSON.stringify(updatedWithdrawals));
      
      // تحديث حالة السحوبات
      setWithdrawals(prev => prev.filter(item => item.id !== id));
    } else if (type === 'loan') {
      // البحث عن السلفة المراد حذفها للحصول على المبلغ
      const loansKey = `savings_fund_loans_${currentFundId}`;
      const storedLoans = JSON.parse(localStorage.getItem(loansKey) || '[]');
      
      // حذف السلفة من التخزين المحلي
      const updatedLoans = storedLoans.filter(item => item.id !== id);
      localStorage.setItem(loansKey, JSON.stringify(updatedLoans));
      
      // تحديث حالة السلف
      setLoans(prev => prev.filter(item => item.id !== id));
    }
    
    // تحديث بيانات العضو في واجهة المستخدم بما في ذلك الرصيد
    updateMemberTotalBalance();
    
    // إغلاق مربع حوار التأكيد
    setConfirmDeleteDialog({
      open: false,
      type: '',
      id: null
    });
  };

  // التحقق من صحة نموذج الإيرادات
  const validateIncomeForm = () => {
    const newErrors = {};
    if (!newIncome.amount) {
      newErrors.amount = 'يرجى إدخال المبلغ';
    } else if (isNaN(newIncome.amount) || parseFloat(newIncome.amount) <= 0) {
      newErrors.amount = 'يرجى إدخال مبلغ صحيح';
    }
    if (!newIncome.date) {
      newErrors.date = 'يرجى إدخال التاريخ';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  


  // تحديث الرصيد الإجمالي للعضو في localStorage
  const updateMemberTotalBalance = () => {
    if (member) {
      // الحصول على أحدث بيانات العضو من localStorage
      const currentFundId = localStorage.getItem('current_fund_id') || 'default';
      const membersKey = `savings_fund_members_${currentFundId}`;
      
      // حساب الرصيد الإجمالي من جميع المعاملات المالية
      const memberIncomeData = income.filter(item => item.memberId === id);
      const memberExpensesData = expenses.filter(item => item.memberId === id);
      const memberWithdrawals = withdrawals.filter(item => item.memberId === id);
      const memberLoans = loans.filter(item => item.memberId === id);

      const totalIncomeAmount = memberIncomeData.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0);
      const totalExpensesAmount = memberExpensesData.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0);
      const totalWithdrawalsAmount = memberWithdrawals.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0);

      // حساب السلف: فقط السلف غير المسددة تؤثر على الرصيد
      const totalActiveLoansAmount = memberLoans
        .filter(loan => loan.status !== 'paid')
        .reduce((sum, item) => sum + (parseFloat(item.remainingAmount || item.amount) || 0), 0);

      // حساب الرصيد النهائي
      const calculatedBalance = totalIncomeAmount - totalExpensesAmount - totalWithdrawalsAmount - totalActiveLoansAmount;
      
      // تحديث الرصيد في localStorage
      const storedMembers = JSON.parse(localStorage.getItem(membersKey) || '[]');
      const updatedMembers = storedMembers.map(m => {
        if (m.id === id) {
          return {
            ...m,
            totalBalance: calculatedBalance
          };
        }
        return m;
      });
      
      localStorage.setItem(membersKey, JSON.stringify(updatedMembers));
      
      // تحديث حالة العضو في الواجهة
      const updatedMember = updatedMembers.find(m => m.id === id);
      if (updatedMember) {
        setMember(updatedMember);
      }
    }
  };

  // إضافة إيراد جديد
  const handleAddIncome = () => {
    if (validateIncomeForm()) {
      const incomeData = {
        id: `income_${Date.now()}`,
        memberId: id,
        amount: parseFloat(newIncome.amount),
        date: newIncome.date,
        notes: newIncome.notes,
        createdAt: new Date().toISOString()
      };

      // إضافة الإيراد باستخدام الدالة المستلمة من الأب
      addIncome(incomeData);
      
      // تحديث بيانات العضو في واجهة المستخدم
      updateMemberTotalBalance();
      
      handleCloseIncomeDialog();
    }
  };

  // التحقق من صحة نموذج المصروفات
  const validateExpenseForm = () => {
    const newErrors = {};
    if (!newExpense.amount) {
      newErrors.amount = 'يرجى إدخال المبلغ';
    } else if (isNaN(newExpense.amount) || parseFloat(newExpense.amount) <= 0) {
      newErrors.amount = 'يرجى إدخال مبلغ صحيح';
    }
    if (!newExpense.date) {
      newErrors.date = 'يرجى إدخال التاريخ';
    }
    if (!newExpense.category) {
      newErrors.category = 'يرجى اختيار الفئة';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // إضافة مصروف جديد
  const handleAddExpense = () => {
    if (validateExpenseForm()) {
      const expenseData = {
        id: `expense_${Date.now()}`,
        memberId: id,
        amount: parseFloat(newExpense.amount),
        date: newExpense.date,
        category: newExpense.category,
        notes: newExpense.notes,
        createdAt: new Date().toISOString()
      };

      // إضافة المصروف باستخدام الدالة المستلمة من الأب
      addExpense(expenseData);
      
      // تحديث بيانات العضو في واجهة المستخدم
      updateMemberTotalBalance();
      
      handleCloseExpenseDialog();
    }
  };

  // عرض واجهة المستخدم
  if (!member) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography>جاري تحميل بيانات العضو...</Typography>
      </Box>
    );
  }

  // حساب البيانات المالية من localStorage مباشرة للحصول على أحدث البيانات
  const calculateCurrentFinancials = () => {
    const currentFundId = localStorage.getItem('current_fund_id') || 'default';

    // الحصول على أحدث البيانات من localStorage
    const latestIncome = JSON.parse(localStorage.getItem(`savings_fund_income_${currentFundId}`) || '[]');
    const latestExpenses = JSON.parse(localStorage.getItem(`savings_fund_expenses_${currentFundId}`) || '[]');
    const latestWithdrawals = JSON.parse(localStorage.getItem(`savings_fund_withdrawals_${currentFundId}`) || '[]');
    const latestLoans = JSON.parse(localStorage.getItem(`savings_fund_loans_${currentFundId}`) || '[]');

    // فلترة البيانات للعضو الحالي
    const memberIncome = latestIncome.filter(item => item.memberId === id);
    const memberExpenses = latestExpenses.filter(item => item.memberId === id);
    const memberWithdrawals = latestWithdrawals.filter(item => item.memberId === id);
    const memberLoans = latestLoans.filter(item => item.memberId === id);

    // حساب الإجماليات
    const totalIncome = memberIncome.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0);
    const totalExpenses = memberExpenses.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0);
    const totalWithdrawals = memberWithdrawals.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0);
    const totalLoans = memberLoans
      .filter(loan => loan.status !== 'paid')
      .reduce((sum, item) => sum + (parseFloat(item.remainingAmount || item.amount) || 0), 0);

    return {
      memberIncome,
      memberExpenses,
      memberWithdrawals,
      memberLoans,
      totalIncome,
      totalExpenses,
      totalWithdrawals,
      totalLoans
    };
  };

  // الحصول على البيانات المالية الحالية
  const currentFinancials = calculateCurrentFinancials();
  const { memberIncome, memberExpenses, memberWithdrawals, memberLoans, totalIncome, totalExpenses, totalWithdrawals, totalLoans } = currentFinancials;

  // حساب الرصيد الفعلي
  const balance = totalIncome - totalExpenses - totalWithdrawals - totalLoans;
  
  // حساب النسب المئوية للمعاملات المالية
  const totalTransactions = totalIncome + totalExpenses + totalWithdrawals + totalLoans;
  const incomePercentage = totalTransactions > 0 ? Math.round((totalIncome / totalTransactions) * 100) : 0;
  const expensesPercentage = totalTransactions > 0 ? Math.round((totalExpenses / totalTransactions) * 100) : 0;
  const withdrawalsPercentage = totalTransactions > 0 ? Math.round((totalWithdrawals / totalTransactions) * 100) : 0;
  const loansPercentage = totalTransactions > 0 ? Math.round((totalLoans / totalTransactions) * 100) : 0;

  // إذا لم يتم العثور على العضو، عرض رسالة تحميل
  if (!member) {
    return (
      <Box sx={{ p: 3, textAlign: 'center', direction: 'rtl' }}>
        <Typography variant="h6" mb={2}>جاري تحميل بيانات العضو...</Typography>
        <Typography variant="body2" color="text.secondary" mb={2}>
          معرف العضو: {id}
        </Typography>
        <Button
          variant="outlined"
          onClick={() => navigate('/members')}
          sx={{ mt: 2 }}
        >
          العودة لقائمة الأعضاء
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, direction: 'rtl' }}>
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: { xs: 'stretch', sm: 'center' },
        mb: 3,
        flexDirection: { xs: 'column', sm: 'row' },
        gap: { xs: 2, sm: 0 }
      }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/members')}
          variant="outlined"
          sx={{
            borderRadius: '8px',
            px: 2,
            fontSize: { xs: '0.8rem', sm: '0.875rem' },
            minHeight: { xs: '40px', sm: '36px' }
          }}
          fullWidth={{ xs: true, sm: false }}
        >
          العودة للقائمة
        </Button>
        <Box sx={{ display: 'flex', gap: 1, flexDirection: { xs: 'column', sm: 'row' } }}>
          <Button
            onClick={() => window.location.reload()}
            variant="outlined"
            sx={{
              borderRadius: '8px',
              px: 2,
              fontSize: { xs: '0.7rem', sm: '0.8rem' },
              minHeight: { xs: '36px', sm: '32px' }
            }}
          >
            تحديث البيانات
          </Button>
          <Button
            startIcon={<PrintIcon />}
            onClick={handlePrint}
            variant="contained"
            color="primary"
            sx={{
              borderRadius: '8px',
              px: 2,
              boxShadow: '0 4px 10px rgba(0,0,0,0.15)',
              fontSize: { xs: '0.8rem', sm: '0.875rem' },
              minHeight: { xs: '40px', sm: '36px' }
            }}
            fullWidth={{ xs: true, sm: false }}
          >
            طباعة
          </Button>
        </Box>
      </Box>

      <div ref={printRef}>
        <Paper elevation={3} sx={{
          p: { xs: 2, sm: 3 },
          mb: 4,
          borderRadius: '16px',
          overflow: 'hidden',
          boxShadow: '0 6px 20px rgba(0,0,0,0.1)'
        }}>
          <Grid container spacing={{ xs: 2, sm: 3 }}>
            <Grid item xs={12} md={6}>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                mb: 2
              }}>
                <Box sx={{
                  background: 'linear-gradient(45deg, #2196f3, #1565c0)',
                  borderRadius: '50%',
                  p: 0.8,
                  mr: 2,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: '0 2px 4px rgba(33,150,243,0.3)',
                  width: 40,
                  height: 40
                }}>
                  <PersonIcon sx={{ fontSize: 24, color: 'white' }} />
                </Box>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="h5" component="h1" sx={{
                    fontWeight: 'bold',
                    mb: 0.5
                  }}>
                    {member.name}
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    {member.familyName ? `عائلة ${member.familyName}` : 'عضو مستقل'} •
                    انضم {formatDate(member.joinDate || member.createdAt)}
                  </Typography>
                </Box>
              </Box>
              <Divider sx={{ mb: 2 }} />
              <Grid container spacing={2}>
                {member.phone && (
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      mb: 1, 
                      p: 1, 
                      borderRadius: '8px', 
                      bgcolor: 'rgba(0,0,0,0.02)' 
                    }}>
                      <PhoneIcon sx={{ mr: 1, color: 'primary.main' }} />
                      <Typography variant="body1">
                        الهاتف: <strong>{member.phone}</strong>
                      </Typography>
                    </Box>
                  </Grid>
                )}
                {member.email && (
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      mb: 1, 
                      p: 1, 
                      borderRadius: '8px', 
                      bgcolor: 'rgba(0,0,0,0.02)' 
                    }}>
                      <EmailIcon sx={{ mr: 1, color: 'primary.main' }} />
                      <Typography variant="body1">
                        البريد: <strong>{member.email}</strong>
                      </Typography>
                    </Box>
                  </Grid>
                )}
                {member.address && (
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      mb: 1, 
                      p: 1, 
                      borderRadius: '8px', 
                      bgcolor: 'rgba(0,0,0,0.02)' 
                    }}>
                      <LocationOnIcon sx={{ mr: 1, color: 'primary.main' }} />
                      <Typography variant="body1">
                        العنوان: <strong>{member.address}</strong>
                      </Typography>
                    </Box>
                  </Grid>
                )}
              </Grid>
              
              {/* ملخص مالي مضغوط */}
              <Paper elevation={2} sx={{ borderRadius: '8px', mb: 2, overflow: 'hidden' }}>
                <Box
                  sx={{
                    p: 1.5,
                    bgcolor: 'primary.main',
                    color: 'white',
                    cursor: 'pointer',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}
                  onClick={() => setFinancialSummaryOpen(!financialSummaryOpen)}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <BankIcon sx={{ mr: 1, fontSize: 20 }} />
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                      ملخص الحساب المالي
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="h6" sx={{
                      fontWeight: 'bold',
                      color: balance < 0 ? '#d32f2f' : 'white',
                      bgcolor: balance < 0 ? 'rgba(255,255,255,0.9)' : 'transparent',
                      px: balance < 0 ? 1 : 0,
                      py: balance < 0 ? 0.5 : 0,
                      borderRadius: balance < 0 ? '8px' : 0
                    }}>
                      {formatCurrencyWithSign(balance)}
                    </Typography>
                    {financialSummaryOpen ?
                      <ExpandLessIcon sx={{ fontSize: 20 }} /> :
                      <ExpandMoreIcon sx={{ fontSize: 20 }} />
                    }
                  </Box>
                </Box>

                <Collapse in={financialSummaryOpen}>
                  <Box sx={{ p: 1.5 }}>
                    <Grid container spacing={1}>
                      <Grid item xs={6} sm={3}>
                        <Box sx={{
                          bgcolor: 'rgba(33, 150, 243, 0.1)',
                          p: 1,
                          borderRadius: '8px',
                          border: '1px solid rgba(33, 150, 243, 0.2)',
                          textAlign: 'center',
                          minHeight: '60px',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center'
                        }}>
                          <IncomeIcon sx={{ color: 'primary.main', fontSize: 16, mb: 0.5 }} />
                          <Typography variant="caption" sx={{
                            color: 'primary.main',
                            fontWeight: 'medium',
                            fontSize: '0.7rem',
                            display: 'block'
                          }}>
                            الإيرادات
                          </Typography>
                          <Typography variant="subtitle1" sx={{
                            fontWeight: 'bold',
                            color: 'primary.main',
                            fontSize: '0.9rem'
                          }}>
                            {formatCurrency(totalIncome)}
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={6} sm={3}>
                        <Box sx={{
                          bgcolor: 'rgba(244, 67, 54, 0.1)',
                          p: 1,
                          borderRadius: '8px',
                          border: '1px solid rgba(244, 67, 54, 0.2)',
                          textAlign: 'center',
                          minHeight: '60px',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center'
                        }}>
                          <ExpenseIcon sx={{ color: 'error.main', fontSize: 16, mb: 0.5 }} />
                          <Typography variant="caption" sx={{
                            color: 'error.main',
                            fontWeight: 'medium',
                            fontSize: '0.7rem',
                            display: 'block'
                          }}>
                            المصروفات
                          </Typography>
                          <Typography variant="subtitle1" sx={{
                            fontWeight: 'bold',
                            color: 'error.main',
                            fontSize: '0.9rem'
                          }}>
                            {formatCurrency(totalExpenses)}
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={6} sm={3}>
                        <Box sx={{
                          bgcolor: 'rgba(255, 152, 0, 0.1)',
                          p: 1,
                          borderRadius: '8px',
                          border: '1px solid rgba(255, 152, 0, 0.2)',
                          textAlign: 'center',
                          minHeight: '60px',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center'
                        }}>
                          <WithdrawIcon sx={{ color: 'warning.main', fontSize: 16, mb: 0.5 }} />
                          <Typography variant="caption" sx={{
                            color: 'warning.main',
                            fontWeight: 'medium',
                            fontSize: '0.7rem',
                            display: 'block'
                          }}>
                            السحوبات
                          </Typography>
                          <Typography variant="subtitle1" sx={{
                            fontWeight: 'bold',
                            color: 'warning.main',
                            fontSize: '0.9rem'
                          }}>
                            {formatCurrency(totalWithdrawals)}
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={6} sm={3}>
                        <Box sx={{
                          bgcolor: 'rgba(3, 169, 244, 0.1)',
                          p: 1,
                          borderRadius: '8px',
                          border: '1px solid rgba(3, 169, 244, 0.2)',
                          textAlign: 'center',
                          minHeight: '60px',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center'
                        }}>
                          <LoanIcon sx={{ color: 'info.main', fontSize: 16, mb: 0.5 }} />
                          <Typography variant="caption" sx={{
                            color: 'info.main',
                            fontWeight: 'medium',
                            fontSize: '0.7rem',
                            display: 'block'
                          }}>
                            السلف
                          </Typography>
                          <Typography variant="subtitle1" sx={{
                            fontWeight: 'bold',
                            color: 'info.main',
                            fontSize: '0.9rem'
                          }}>
                            {formatCurrency(totalLoans)}
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                </Collapse>
              </Paper>
            </Grid>
          </Grid>
        </Paper>

        <Box sx={{ mb: 2 }}>
          <Paper elevation={2} sx={{ borderRadius: '8px', overflow: 'hidden', mb: 2 }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              aria-label="member details tabs"
              sx={{
                bgcolor: 'background.paper',
                minHeight: '48px',
                '& .MuiTab-root': {
                  minHeight: '48px',
                  fontSize: '0.875rem',
                  fontWeight: 'medium',
                  px: 1.5,
                  py: 0.5
                }
              }}
            >
              <Tab
                label="الإيرادات"
                icon={<IncomeIcon />}
                iconPosition="start"
                sx={{
                  color: tabValue === 0 ? 'white !important' : 'text.primary',
                  bgcolor: tabValue === 0 ? 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)' : 'rgba(33, 150, 243, 0.05)',
                  background: tabValue === 0 ? 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)' : 'rgba(33, 150, 243, 0.05)',
                  border: tabValue === 0 ? '2px solid #1976d2' : '2px solid rgba(33, 150, 243, 0.2)',
                  '& .MuiTab-iconWrapper': {
                    color: tabValue === 0 ? 'white !important' : 'inherit'
                  },
                  '&:hover': {
                    bgcolor: tabValue === 0 ? 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)' : 'rgba(33, 150, 243, 0.1)',
                    background: tabValue === 0 ? 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)' : 'rgba(33, 150, 243, 0.1)',
                    color: 'white !important'
                  }
                }}
              />
              <Tab
                label="المصروفات"
                icon={<ExpenseIcon />}
                iconPosition="start"
                sx={{
                  color: tabValue === 1 ? 'white !important' : 'text.primary',
                  bgcolor: tabValue === 1 ? 'linear-gradient(135deg, #f44336 0%, #d32f2f 100%)' : 'rgba(244, 67, 54, 0.05)',
                  background: tabValue === 1 ? 'linear-gradient(135deg, #f44336 0%, #d32f2f 100%)' : 'rgba(244, 67, 54, 0.05)',
                  border: tabValue === 1 ? '2px solid #d32f2f' : '2px solid rgba(244, 67, 54, 0.2)',
                  '& .MuiTab-iconWrapper': {
                    color: tabValue === 1 ? 'white !important' : 'inherit'
                  },
                  '&:hover': {
                    bgcolor: tabValue === 1 ? 'linear-gradient(135deg, #d32f2f 0%, #c62828 100%)' : 'rgba(244, 67, 54, 0.1)',
                    background: tabValue === 1 ? 'linear-gradient(135deg, #d32f2f 0%, #c62828 100%)' : 'rgba(244, 67, 54, 0.1)',
                    color: 'white !important'
                  }
                }}
              />
              <Tab
                label="السحوبات"
                icon={<WithdrawIcon />}
                iconPosition="start"
                sx={{
                  color: tabValue === 2 ? 'white !important' : 'text.primary',
                  bgcolor: tabValue === 2 ? 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)' : 'rgba(255, 152, 0, 0.05)',
                  background: tabValue === 2 ? 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)' : 'rgba(255, 152, 0, 0.05)',
                  border: tabValue === 2 ? '2px solid #f57c00' : '2px solid rgba(255, 152, 0, 0.2)',
                  '& .MuiTab-iconWrapper': {
                    color: tabValue === 2 ? 'white !important' : 'inherit'
                  },
                  '&:hover': {
                    bgcolor: tabValue === 2 ? 'linear-gradient(135deg, #f57c00 0%, #ef6c00 100%)' : 'rgba(255, 152, 0, 0.1)',
                    background: tabValue === 2 ? 'linear-gradient(135deg, #f57c00 0%, #ef6c00 100%)' : 'rgba(255, 152, 0, 0.1)',
                    color: 'white !important'
                  }
                }}
              />
              <Tab
                label="السلف"
                icon={<LoanIcon />}
                iconPosition="start"
                sx={{
                  color: tabValue === 3 ? 'white !important' : 'text.primary',
                  bgcolor: tabValue === 3 ? 'linear-gradient(135deg, #03a9f4 0%, #0288d1 100%)' : 'rgba(3, 169, 244, 0.05)',
                  background: tabValue === 3 ? 'linear-gradient(135deg, #03a9f4 0%, #0288d1 100%)' : 'rgba(3, 169, 244, 0.05)',
                  border: tabValue === 3 ? '2px solid #0288d1' : '2px solid rgba(3, 169, 244, 0.2)',
                  '& .MuiTab-iconWrapper': {
                    color: tabValue === 3 ? 'white !important' : 'inherit'
                  },
                  '&:hover': {
                    bgcolor: tabValue === 3 ? 'linear-gradient(135deg, #0288d1 0%, #0277bd 100%)' : 'rgba(3, 169, 244, 0.1)',
                    background: tabValue === 3 ? 'linear-gradient(135deg, #0288d1 0%, #0277bd 100%)' : 'rgba(3, 169, 244, 0.1)',
                    color: 'white !important'
                  }
                }}
              />
            </Tabs>
          </Paper>
          
          {/* شرح أنواع المعاملات */}
          <Paper elevation={2} sx={{
            p: 2,
            mb: 3,
            borderRadius: '12px',
            bgcolor: tabValue === 0 ? 'rgba(33, 150, 243, 0.05)' :
                     tabValue === 1 ? 'rgba(244, 67, 54, 0.05)' :
                     tabValue === 2 ? 'rgba(255, 152, 0, 0.05)' :
                     'rgba(3, 169, 244, 0.05)',
            border: `1px solid ${
              tabValue === 0 ? 'rgba(33, 150, 243, 0.2)' :
              tabValue === 1 ? 'rgba(244, 67, 54, 0.2)' :
              tabValue === 2 ? 'rgba(255, 152, 0, 0.2)' :
              'rgba(3, 169, 244, 0.2)'
            }`
          }}>
            {tabValue === 0 && (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <IncomeIcon color="primary" sx={{ mr: 1, fontSize: 24 }} />
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  <strong style={{ color: '#1976d2' }}>الإيرادات:</strong> مبالغ يتم إيداعها في الصندوق وتضاف إلى رصيد العضو.
                </Typography>
              </Box>
            )}
            {tabValue === 1 && (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <ExpenseIcon color="error" sx={{ mr: 1, fontSize: 24 }} />
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  <strong style={{ color: '#d32f2f' }}>المصروفات:</strong> مبالغ يتم خصمها من رصيد العضو لتغطية نفقات الصندوق.
                </Typography>
              </Box>
            )}
            {tabValue === 2 && (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <WithdrawIcon color="warning" sx={{ mr: 1, fontSize: 24 }} />
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  <strong style={{ color: '#ed6c02' }}>السحوبات:</strong> مبالغ يسحبها العضو من رصيده الشخصي في الصندوق.
                </Typography>
              </Box>
            )}
            {tabValue === 3 && (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <LoanIcon color="info" sx={{ mr: 1, fontSize: 24 }} />
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  <strong style={{ color: '#0288d1' }}>السلف:</strong> مبالغ يقترضها العضو من الصندوق ويلتزم بسدادها في تاريخ محدد.
                </Typography>
              </Box>
            )}
          </Paper>

          {/* تبويب الإيرادات */}
          <TabPanel value={tabValue} index={0}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main', display: 'flex', alignItems: 'center' }}>
                <IncomeIcon sx={{ mr: 1 }} /> سجل الإيرادات
              </Typography>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={handleOpenIncomeDialog}
                sx={{ 
                  borderRadius: '8px', 
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                  '&:hover': { boxShadow: '0 4px 12px rgba(0,0,0,0.2)' }
                }}
              >
                إضافة إيراد
              </Button>
            </Box>
            <TableContainer component={Paper} sx={{
              borderRadius: '12px',
              overflow: 'auto',
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              maxWidth: '100%'
            }}>
              <Table size="small" sx={{ minWidth: { xs: '100%', sm: 650 } }}>
                <TableHead sx={{
                  bgcolor: 'primary.main',
                  '& .MuiTableCell-root': {
                    fontSize: { xs: '0.8rem', sm: '0.875rem' },
                    padding: { xs: '8px 4px', sm: '16px' }
                  }
                }}>
                  <TableRow>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>التاريخ</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>المبلغ</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>ملاحظات</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>الإجراءات</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {memberIncome.length > 0 ? (
                    memberIncome.map((item, index) => (
                      <TableRow key={item.id} sx={{ bgcolor: index % 2 === 0 ? 'white' : '#f5f9ff' }}>
                        <TableCell sx={{
                          fontSize: { xs: '0.7rem', sm: '0.875rem' },
                          padding: { xs: '4px 2px', sm: '8px 16px' }
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <DateIcon sx={{ mr: 1, color: 'primary.main', fontSize: { xs: 14, sm: 18 } }} />
                            <Typography sx={{ fontSize: { xs: '0.7rem', sm: '0.875rem' } }}>
                              {formatDate(item.date)}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell sx={{
                          fontSize: { xs: '0.7rem', sm: '0.875rem' },
                          padding: { xs: '4px 2px', sm: '8px 16px' }
                        }}>
                          <Typography sx={{
                            fontWeight: 'bold',
                            color: 'primary.main',
                            fontSize: { xs: '0.7rem', sm: '0.875rem' }
                          }}>
                            {formatCurrency(item.amount)}
                          </Typography>
                        </TableCell>
                        <TableCell sx={{
                          fontSize: { xs: '0.7rem', sm: '0.875rem' },
                          padding: { xs: '4px 2px', sm: '8px 16px' },
                          maxWidth: { xs: '80px', sm: 'none' },
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}>
                          {item.notes || '-'}
                        </TableCell>
                        <TableCell sx={{
                          padding: { xs: '4px 2px', sm: '8px 16px' }
                        }}>
                          <IconButton 
                            size="small" 
                            color="error"
                            sx={{ 
                              bgcolor: 'rgba(244, 67, 54, 0.1)', 
                              '&:hover': { bgcolor: 'rgba(244, 67, 54, 0.2)' } 
                            }}
                            onClick={() => {
                              // تحديث الرصيد الإجمالي للعضو في localStorage قبل حذف الإيراد
                              const currentFundId = localStorage.getItem('current_fund_id') || 'default';
                              const membersKey = `savings_fund_members_${currentFundId}`;
                              const storedMembers = JSON.parse(localStorage.getItem(membersKey) || '[]');
                              const updatedMembers = storedMembers.map(member => {
                                if (member.id === id) {
                                  return {
                                    ...member,
                                    totalBalance: (member.totalBalance || 0) - item.amount
                                  };
                                }
                                return member;
                              });
                              localStorage.setItem(membersKey, JSON.stringify(updatedMembers));
                              
                              // حذف الإيراد
                              deleteIncome(item.id);
                              
                              // تحديث بيانات العضو
                              updateMemberTotalBalance();
                            }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={4} align="center" sx={{ py: 4 }}>
                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', p: 2 }}>
                          <IncomeIcon sx={{ fontSize: 40, color: 'text.secondary', mb: 1 }} />
                          <Typography color="text.secondary">لا توجد إيرادات مسجلة</Typography>
                        </Box>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </TabPanel>

          {/* تبويب المصروفات */}
          <TabPanel value={tabValue} index={1}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'error.main', display: 'flex', alignItems: 'center' }}>
                <ExpenseIcon sx={{ mr: 1 }} /> سجل المصروفات
              </Typography>
              <Button
                variant="contained"
                color="error"
                startIcon={<AddIcon />}
                onClick={handleOpenExpenseDialog}
                sx={{ 
                  borderRadius: '8px', 
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                  '&:hover': { boxShadow: '0 4px 12px rgba(0,0,0,0.2)' }
                }}
              >
                إضافة مصروف
              </Button>
            </Box>
            <TableContainer component={Paper} sx={{ borderRadius: '12px', overflow: 'hidden', boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
              <Table>
                <TableHead sx={{ bgcolor: 'error.main' }}>
                  <TableRow>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>التاريخ</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>المبلغ</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>الفئة</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>ملاحظات</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>الإجراءات</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {memberExpenses.length > 0 ? (
                    memberExpenses.map((item, index) => (
                      <TableRow key={item.id} sx={{ bgcolor: index % 2 === 0 ? 'white' : '#fff5f5' }}>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <DateIcon sx={{ mr: 1, color: 'error.main', fontSize: 18 }} />
                            {formatDate(item.date)}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography sx={{ fontWeight: 'bold', color: 'error.main' }}>
                            {formatCurrency(item.amount)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={item.category} 
                            size="small" 
                            color="error" 
                            variant="outlined" 
                          />
                        </TableCell>
                        <TableCell>{item.notes || '-'}</TableCell>
                        <TableCell>
                          <IconButton 
                            size="small" 
                            color="error"
                            sx={{ 
                              bgcolor: 'rgba(244, 67, 54, 0.1)', 
                              '&:hover': { bgcolor: 'rgba(244, 67, 54, 0.2)' } 
                            }}
                            onClick={() => {
                              // تحديث الرصيد الإجمالي للعضو في localStorage قبل حذف المصروف
                              const currentFundId = localStorage.getItem('current_fund_id') || 'default';
                              const membersKey = `savings_fund_members_${currentFundId}`;
                              const storedMembers = JSON.parse(localStorage.getItem(membersKey) || '[]');
                              const updatedMembers = storedMembers.map(member => {
                                if (member.id === id) {
                                  return {
                                    ...member,
                                    totalBalance: (member.totalBalance || 0) + item.amount
                                  };
                                }
                                return member;
                              });
                              localStorage.setItem(membersKey, JSON.stringify(updatedMembers));
                              
                              // حذف المصروف
                              deleteExpense(item.id);
                              
                              // تحديث بيانات العضو
                              updateMemberTotalBalance();
                            }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} align="center" sx={{ py: 4 }}>
                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', p: 2 }}>
                          <ExpenseIcon sx={{ fontSize: 40, color: 'text.secondary', mb: 1 }} />
                          <Typography color="text.secondary">لا توجد مصروفات مسجلة</Typography>
                        </Box>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </TabPanel>

          {/* تبويب السحوبات */}
          <TabPanel value={tabValue} index={2}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'warning.main', display: 'flex', alignItems: 'center' }}>
                <WithdrawIcon sx={{ mr: 1 }} /> سجل السحوبات
              </Typography>
              <Button
                variant="contained"
                color="warning"
                startIcon={<AddIcon />}
                onClick={handleOpenWithdrawalDialog}
                sx={{ 
                  borderRadius: '8px', 
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                  '&:hover': { boxShadow: '0 4px 12px rgba(0,0,0,0.2)' }
                }}
              >
                إضافة سحب
              </Button>
            </Box>
            <TableContainer component={Paper} sx={{ borderRadius: '12px', overflow: 'hidden', boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
              <Table>
                <TableHead sx={{ bgcolor: 'warning.main' }}>
                  <TableRow>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>التاريخ</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>المبلغ</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>النسبة من الرصيد</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>ملاحظات</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>الإجراءات</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {memberWithdrawals.length > 0 ? (
                    memberWithdrawals.map((item, index) => {
                      // حساب نسبة السحب من إجمالي الرصيد
                      const withdrawalPercentage = totalIncome > 0 ? (item.amount / totalIncome) * 100 : 0;
                      const isHighPercentage = withdrawalPercentage > 50;
                      const isMediumPercentage = withdrawalPercentage > 25 && withdrawalPercentage <= 50;
                      
                      return (
                        <TableRow key={item.id} sx={{ 
                          bgcolor: index % 2 === 0 ? 'white' : '#fffbf0',
                          ...(isHighPercentage && { borderLeft: '4px solid #f44336' }),
                          ...(isMediumPercentage && { borderLeft: '4px solid #ff9800' })
                        }}>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <DateIcon sx={{ mr: 1, color: 'warning.main', fontSize: 18 }} />
                              {formatDate(item.date)}
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                              {formatCurrency(item.amount)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Box sx={{ 
                                width: '100%', 
                                mr: 1, 
                                bgcolor: '#f5f5f5', 
                                borderRadius: 5, 
                                height: 8,
                                overflow: 'hidden'
                              }}>
                                <Box sx={{ 
                                  width: `${Math.min(withdrawalPercentage, 100)}%`, 
                                  bgcolor: isHighPercentage ? 'error.main' : (isMediumPercentage ? 'warning.main' : 'success.main'),
                                  height: '100%',
                                  borderRadius: 5
                                }} />
                              </Box>
                              <Typography variant="caption" color="text.secondary">
                                {withdrawalPercentage.toFixed(1)}%
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>{item.notes || '-'}</TableCell>
                          <TableCell>
                            <IconButton 
                              size="small" 
                              color="error"
                              sx={{ 
                                bgcolor: 'rgba(244, 67, 54, 0.1)', 
                                '&:hover': { bgcolor: 'rgba(244, 67, 54, 0.2)' } 
                              }}
                              onClick={() => handleDeleteWithdrawal(item.id)}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} align="center" sx={{ py: 4 }}>
                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', p: 2 }}>
                          <WithdrawIcon sx={{ fontSize: 40, color: 'text.secondary', mb: 1 }} />
                          <Typography color="text.secondary">لا توجد سحوبات مسجلة</Typography>
                        </Box>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </TabPanel>

          {/* تبويب السلف */}
          <TabPanel value={tabValue} index={3}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'info.main', display: 'flex', alignItems: 'center' }}>
                <LoanIcon sx={{ mr: 1 }} /> سجل السلف
              </Typography>
              <Button
                variant="contained"
                color="info"
                startIcon={<AddIcon />}
                onClick={handleOpenLoanDialog}
                sx={{ 
                  borderRadius: '8px', 
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                  '&:hover': { boxShadow: '0 4px 12px rgba(0,0,0,0.2)' }
                }}
              >
                إضافة سلفة
              </Button>
            </Box>
            <TableContainer component={Paper} sx={{ borderRadius: '12px', overflow: 'hidden', boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
              <Table>
                <TableHead sx={{ bgcolor: 'info.main' }}>
                  <TableRow>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>التاريخ</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>المبلغ الإجمالي</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>نوع التسديد</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>الدفعة الشهرية</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>المدفوع/المتبقي</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>الحالة</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>الإجراءات</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {memberLoans.length > 0 ? (
                    memberLoans.map((item, index) => {
                      // حساب الدفعة الشهرية
                      const monthlyPayment = item.paymentType === 'monthly'
                        ? (item.originalAmount || item.amount) / (item.repaymentPeriod || 12)
                        : (item.originalAmount || item.amount);

                      // حساب عدد الدفعات المدفوعة والمتبقية
                      const totalPayments = item.paymentType === 'monthly' ? (item.repaymentPeriod || 12) : 1;
                      const paidAmount = (item.originalAmount || item.amount) - (item.remainingAmount || 0);
                      const paidPayments = item.paymentType === 'monthly'
                        ? Math.floor(paidAmount / monthlyPayment)
                        : (item.status === 'paid' ? 1 : 0);
                      const remainingPayments = totalPayments - paidPayments;

                      // حساب الوقت المتبقي للسلفة
                      const timeRemaining = calculateLoanTimeRemaining(item.date, item.repaymentPeriod || 12);
                      const isOverdue = timeRemaining === 'منتهية الصلاحية';
                      const isDueSoon = timeRemaining.includes('يوم') && !timeRemaining.includes('شهر');

                      return (
                        <TableRow key={item.id} sx={{
                          bgcolor: index % 2 === 0 ? 'white' : '#f0f7ff',
                          ...(isOverdue && { borderLeft: '4px solid #f44336' }),
                          ...(isDueSoon && !isOverdue && { borderLeft: '4px solid #ff9800' })
                        }}>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <DateIcon sx={{ mr: 1, color: 'info.main', fontSize: 18 }} />
                              {formatDate(item.date)}
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography sx={{ fontWeight: 'bold', color: 'info.main' }}>
                              {formatCurrency(item.originalAmount || item.amount)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={item.paymentType === 'single' ? 'دفعة واحدة' : `${totalPayments} دفعات شهرية`}
                              size="small"
                              color={item.paymentType === 'single' ? 'primary' : 'secondary'}
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell>
                            <Box>
                              <Typography sx={{ fontWeight: 'bold', color: 'success.main' }}>
                                {formatCurrency(monthlyPayment)}
                              </Typography>
                              {item.paymentType === 'monthly' && (
                                <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                                  لكل شهر
                                </Typography>
                              )}
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Box>
                              <Typography sx={{ fontWeight: 'bold', color: 'success.main' }}>
                                مدفوع: {formatCurrency(paidAmount)}
                              </Typography>
                              <Typography sx={{ fontWeight: 'bold', color: 'error.main' }}>
                                متبقي: {formatCurrency(item.remainingAmount || 0)}
                              </Typography>
                              {item.paymentType === 'monthly' && (
                                <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                                  {paidPayments} من {totalPayments} دفعات
                                </Typography>
                              )}
                            </Box>
                          </TableCell>
                          <TableCell>
                            {isOverdue ? (
                              <Chip
                                label="منتهية الصلاحية"
                                size="small"
                                color="error"
                                variant="filled"
                              />
                            ) : isDueSoon ? (
                              <Chip
                                label="تستحق قريباً"
                                size="small"
                                color="warning"
                                variant="outlined"
                              />
                            ) : (
                              <Chip
                                label={item.status === 'paid' ? 'مسددة' : 'نشطة'}
                                size="small"
                                color={item.status === 'paid' ? 'success' : 'info'}
                                variant="outlined"
                              />
                            )}
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                              {item.status !== 'paid' && remainingPayments > 0 && (
                                <>
                                  <Button
                                    size="small"
                                    variant="outlined"
                                    color="success"
                                    onClick={() => handleLoanPayment(item.id, 'partial')}
                                    sx={{ minWidth: 'auto', px: 1, fontSize: '0.7rem' }}
                                  >
                                    دفع دفعة
                                  </Button>
                                  <Button
                                    size="small"
                                    variant="contained"
                                    color="success"
                                    onClick={() => handleLoanPayment(item.id, 'full')}
                                    sx={{ minWidth: 'auto', px: 1, fontSize: '0.7rem' }}
                                  >
                                    تسديد كامل
                                  </Button>
                                </>
                              )}
                              <IconButton
                                size="small"
                                color="error"
                                sx={{
                                  bgcolor: 'rgba(244, 67, 54, 0.1)',
                                  '&:hover': { bgcolor: 'rgba(244, 67, 54, 0.2)' }
                                }}
                                onClick={() => handleDeleteLoan(item.id)}
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Box>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', p: 2 }}>
                          <LoanIcon sx={{ fontSize: 40, color: 'text.secondary', mb: 1 }} />
                          <Typography color="text.secondary">لا توجد سلف مسجلة</Typography>
                        </Box>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </TabPanel>
        </Box>

        {/* Copyright */}
        <Copyright variant="compact" />
      </div>

      {/* نافذة إضافة إيراد */}
      <Dialog 
        open={openIncomeDialog} 
        onClose={handleCloseIncomeDialog} 
        maxWidth="sm" 
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: '12px',
            boxShadow: '0 8px 24px rgba(0,0,0,0.15)'
          }
        }}
      >
        <DialogTitle sx={{ 
          bgcolor: 'primary.light', 
          color: 'primary.dark',
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          pb: 2
        }}>
          <IncomeIcon /> إضافة إيراد جديد
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Box sx={{ mb: 2, p: 2, bgcolor: '#f5f9ff', borderRadius: '8px', display: 'flex', alignItems: 'center' }}>
            <InfoIcon sx={{ color: 'primary.main', mr: 1 }} />
            <Typography variant="body2">الإيرادات هي مبالغ يتم إيداعها في الصندوق وتضاف إلى رصيد العضو.</Typography>
          </Box>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                label="المبلغ"
                fullWidth
                type="number"
                name="amount"
                value={newIncome.amount}
                onChange={(e) => setNewIncome({ ...newIncome, amount: e.target.value })}
                error={!!errors.amount}
                helperText={errors.amount}
                InputProps={{
                  startAdornment: <InputAdornment position="start">ر.س</InputAdornment>,
                }}
                sx={{ '& .MuiOutlinedInput-root': { borderRadius: '8px' } }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="التاريخ"
                fullWidth
                type="date"
                name="date"
                value={newIncome.date}
                onChange={(e) => setNewIncome({ ...newIncome, date: e.target.value })}
                error={!!errors.date}
                helperText={errors.date}
                InputLabelProps={{ shrink: true }}
                sx={{ '& .MuiOutlinedInput-root': { borderRadius: '8px' } }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="ملاحظات"
                fullWidth
                multiline
                rows={3}
                name="notes"
                value={newIncome.notes}
                onChange={(e) => setNewIncome({ ...newIncome, notes: e.target.value })}
                sx={{ '& .MuiOutlinedInput-root': { borderRadius: '8px' } }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid', borderColor: 'divider' }}>
          <Button 
            onClick={handleCloseIncomeDialog}
            variant="outlined"
            sx={{ borderRadius: '8px' }}
          >
            إلغاء
          </Button>
          <Button 
            onClick={handleAddIncome} 
            variant="contained" 
            color="primary"
            startIcon={<AddIcon />}
            sx={{ borderRadius: '8px' }}
          >
            إضافة
          </Button>
        </DialogActions>
      </Dialog>

      {/* نافذة إضافة مصروف */}
      <Dialog open={openExpenseDialog} onClose={handleCloseExpenseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>إضافة مصروف جديد</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                label="المبلغ"
                fullWidth
                type="number"
                name="amount"
                value={newExpense.amount}
                onChange={(e) => setNewExpense({ ...newExpense, amount: e.target.value })}
                error={!!errors.amount}
                helperText={errors.amount}
                InputProps={{
                  startAdornment: <InputAdornment position="start">ر.س</InputAdornment>,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="التاريخ"
                fullWidth
                type="date"
                name="date"
                value={newExpense.date}
                onChange={(e) => setNewExpense({ ...newExpense, date: e.target.value })}
                error={!!errors.date}
                helperText={errors.date}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth error={!!errors.category}>
                <InputLabel>الفئة</InputLabel>
                <Select
                  value={newExpense.category}
                  label="الفئة"
                  onChange={(e) => setNewExpense({ ...newExpense, category: e.target.value })}
                >
                  <MenuItem value="إيجار">إيجار</MenuItem>
                  <MenuItem value="رواتب">رواتب</MenuItem>
                  <MenuItem value="مرافق">مرافق</MenuItem>
                  <MenuItem value="صيانة">صيانة</MenuItem>
                  <MenuItem value="أخرى">أخرى</MenuItem>
                </Select>
                {errors.category && <FormHelperText>{errors.category}</FormHelperText>}
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="ملاحظات"
                fullWidth
                multiline
                rows={3}
                name="notes"
                value={newExpense.notes}
                onChange={(e) => setNewExpense({ ...newExpense, notes: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseExpenseDialog}>إلغاء</Button>
          <Button onClick={handleAddExpense} variant="contained" color="primary">إضافة</Button>
        </DialogActions>
      </Dialog>

      {/* نافذة إضافة سحب */}
      <Dialog open={openWithdrawalDialog} onClose={handleCloseWithdrawalDialog} maxWidth="sm" fullWidth>
        <DialogTitle>إضافة سحب جديد</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                label="المبلغ"
                fullWidth
                type="number"
                name="amount"
                value={newWithdrawal.amount}
                onChange={(e) => setNewWithdrawal({ ...newWithdrawal, amount: e.target.value })}
                error={!!errors.amount}
                helperText={errors.amount}
                InputProps={{
                  startAdornment: <InputAdornment position="start">ر.س</InputAdornment>,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="التاريخ"
                fullWidth
                type="date"
                name="date"
                value={newWithdrawal.date}
                onChange={(e) => setNewWithdrawal({ ...newWithdrawal, date: e.target.value })}
                error={!!errors.date}
                helperText={errors.date}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="ملاحظات"
                fullWidth
                multiline
                rows={3}
                name="notes"
                value={newWithdrawal.notes}
                onChange={(e) => setNewWithdrawal({ ...newWithdrawal, notes: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseWithdrawalDialog}>إلغاء</Button>
          <Button onClick={handleAddWithdrawal} variant="contained" color="primary">إضافة</Button>
        </DialogActions>
      </Dialog>

      {/* نافذة إضافة سلفة */}
      <Dialog open={openLoanDialog} onClose={handleCloseLoanDialog} maxWidth="sm" fullWidth>
        <DialogTitle>إضافة سلفة جديدة</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                label="المبلغ"
                fullWidth
                type="number"
                name="amount"
                value={newLoan.amount}
                onChange={(e) => setNewLoan({ ...newLoan, amount: e.target.value })}
                error={!!errors.amount}
                helperText={errors.amount}
                InputProps={{
                  startAdornment: <InputAdornment position="start">ر.س</InputAdornment>,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="التاريخ"
                fullWidth
                type="date"
                name="date"
                value={newLoan.date}
                onChange={(e) => setNewLoan({ ...newLoan, date: e.target.value })}
                error={!!errors.date}
                helperText={errors.date}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="فترة السداد (بالأشهر)"
                fullWidth
                type="number"
                name="repaymentPeriod"
                value={newLoan.repaymentPeriod}
                onChange={(e) => setNewLoan({ ...newLoan, repaymentPeriod: e.target.value })}
                error={!!errors.repaymentPeriod}
                helperText={errors.repaymentPeriod}
                InputProps={{
                  endAdornment: <InputAdornment position="end">شهر</InputAdornment>,
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>نوع التسديد</InputLabel>
                <Select
                  value={newLoan.paymentType}
                  label="نوع التسديد"
                  onChange={(e) => setNewLoan({ ...newLoan, paymentType: e.target.value })}
                >
                  <MenuItem value="monthly">دفعات شهرية</MenuItem>
                  <MenuItem value="single">دفعة واحدة</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            {/* عرض الدفعة الشهرية */}
            {newLoan.amount && newLoan.repaymentPeriod && newLoan.paymentType === 'monthly' && (
              <Grid item xs={12}>
                <Paper sx={{ p: 2, bgcolor: 'rgba(33, 150, 243, 0.05)', border: '1px solid rgba(33, 150, 243, 0.2)' }}>
                  <Typography variant="subtitle2" sx={{ mb: 1, color: 'primary.main', fontWeight: 'bold' }}>
                    تفاصيل الدفعات الشهرية:
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="body2">
                      الدفعة الشهرية: <strong>{formatCurrency(parseFloat(newLoan.amount) / parseInt(newLoan.repaymentPeriod || 12))}</strong>
                    </Typography>
                    <Typography variant="body2">
                      عدد الدفعات: <strong>{newLoan.repaymentPeriod} دفعة</strong>
                    </Typography>
                  </Box>
                </Paper>
              </Grid>
            )}
            <Grid item xs={12}>
              <TextField
                label="ملاحظات"
                fullWidth
                multiline
                rows={3}
                name="notes"
                value={newLoan.notes}
                onChange={(e) => setNewLoan({ ...newLoan, notes: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseLoanDialog}>إلغاء</Button>
          <Button onClick={handleAddLoan} variant="contained" color="primary">إضافة</Button>
        </DialogActions>
      </Dialog>

      {/* مربع حوار تأكيد الحذف */}
      <Dialog
        open={confirmDeleteDialog.open}
        onClose={() => setConfirmDeleteDialog({ open: false, type: '', id: null })}
      >
        <DialogTitle>
          تأكيد الحذف
        </DialogTitle>
        <DialogContent>
          <Typography>
            هل أنت متأكد من رغبتك في حذف هذا {confirmDeleteDialog.type === 'withdrawal' ? 'السحب' : 'السلفة'}؟
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDeleteDialog({ open: false, type: '', id: null })}>
            إلغاء
          </Button>
          <Button onClick={handleConfirmDelete} color="error" variant="contained">
            حذف
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

// تعريف TabPanel
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`member-tabpanel-${index}`}
      aria-labelledby={`member-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

export default MemberDetails;
