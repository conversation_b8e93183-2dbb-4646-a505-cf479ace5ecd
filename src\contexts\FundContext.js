import React, { createContext, useState, useEffect, useContext } from 'react';

// Local storage keys
const FUNDS_LIST_KEY = 'savings_funds_list';
const CURRENT_FUND_KEY = 'current_fund_id';

// Helper functions for localStorage
const safelyParseJSON = (key, defaultValue = []) => {
  try {
    const storedData = localStorage.getItem(key);
    return storedData ? JSON.parse(storedData) : defaultValue;
  } catch (error) {
    console.error(`Error parsing data for key ${key}:`, error);
    return defaultValue;
  }
};

const safelySaveJSON = (key, data) => {
  try {
    localStorage.setItem(key, JSON.stringify(data));
    return true;
  } catch (error) {
    console.error(`Error saving data for key ${key}:`, error);
    return false;
  }
};

// Create the context
const FundContext = createContext();

// Create a provider component
export const FundProvider = ({ children }) => {
  // State for funds list and current fund - بدء من الصفر
  const [funds, setFunds] = useState(() => {
    const existingFunds = safelyParseJSON(FUNDS_LIST_KEY, []);
    return existingFunds; // لا ننشئ أي صندوق افتراضي
  });

  const [currentFundId, setCurrentFundId] = useState(() => {
    const savedId = localStorage.getItem(CURRENT_FUND_KEY);
    const existingFunds = safelyParseJSON(FUNDS_LIST_KEY, []);

    // If there's a saved ID and it exists in funds, use it
    if (savedId && existingFunds.some(fund => fund.id === savedId)) {
      return savedId;
    }

    // If there are funds but no valid saved ID, use the first fund
    if (existingFunds.length > 0) {
      const firstFundId = existingFunds[0].id;
      localStorage.setItem(CURRENT_FUND_KEY, firstFundId);
      return firstFundId;
    }

    // No funds exist, return null
    return null;
  });

  // Save funds to localStorage when they change
  useEffect(() => {
    safelySaveJSON(FUNDS_LIST_KEY, funds);
  }, [funds]);

  // Save current fund ID to localStorage when it changes
  useEffect(() => {
    localStorage.setItem(CURRENT_FUND_KEY, currentFundId);
  }, [currentFundId]);

  // Get current fund object
  const currentFund = funds.find(fund => fund.id === currentFundId) || 
    (funds.length > 0 ? funds[0] : null);

  // Function to switch to a different fund
  const switchFund = (fundId) => {
    if (funds.some(fund => fund.id === fundId)) {
      // تغيير الصندوق الحالي
      setCurrentFundId(fundId);
      
      // حفظ معرف الصندوق الحالي في localStorage
      localStorage.setItem(CURRENT_FUND_KEY, fundId);
      
      // إعادة تحميل البيانات من localStorage باستخدام المفاتيح المرتبطة بالصندوق الجديد
      // سيتم تنفيذ ذلك تلقائيًا من خلال useEffect في AppContent
      
      return true;
    }
    return false;
  };

  // Function to add a new fund
  const addFund = (fundData) => {
    const newFund = {
      id: `fund_${Date.now()}`,
      ...fundData,
      createdAt: new Date().toISOString()
    };

    // Add to funds list
    setFunds(prev => [...prev, newFund]);

    // Set the new fund as the current fund
    setCurrentFundId(newFund.id);

    // Create fund-specific storage keys with empty data
    const fundKeys = {
      [`savings_fund_members_${newFund.id}`]: [],
      [`savings_fund_income_${newFund.id}`]: [],
      [`savings_fund_expenses_${newFund.id}`]: [],
      [`savings_fund_families_${newFund.id}`]: [],
      [`savings_fund_withdrawals_${newFund.id}`]: [],
      [`savings_fund_loans_${newFund.id}`]: [],
      [`savings_fund_settings_${newFund.id}`]: newFund
    };

    // Save empty data for each key
    Object.entries(fundKeys).forEach(([key, value]) => {
      safelySaveJSON(key, value);
    });

    return newFund;
  };

  // Function to update an existing fund
  const updateFund = (fundId, fundData) => {
    // Update the fund in the funds list
    setFunds(prev => prev.map(fund =>
      fund.id === fundId
        ? { ...fund, ...fundData, updatedAt: new Date().toISOString() }
        : fund
    ));

    // Update the fund settings in localStorage
    const updatedFund = {
      ...funds.find(f => f.id === fundId),
      ...fundData,
      updatedAt: new Date().toISOString()
    };
    localStorage.setItem(`savings_fund_settings_${fundId}`, JSON.stringify(updatedFund));

    return updatedFund;
  };

  // Function to delete a fund
  const deleteFund = (fundId) => {
    // Remove fund from list
    const updatedFunds = funds.filter(fund => fund.id !== fundId);
    setFunds(updatedFunds);

    // If deleting the current fund, switch to another fund or null
    if (currentFundId === fundId) {
      if (updatedFunds.length > 0) {
        // Switch to the first available fund
        setCurrentFundId(updatedFunds[0].id);
      } else {
        // No funds left, set to null
        setCurrentFundId(null);
      }
    }

    // Remove fund-specific data from localStorage
    const keysToRemove = [
      `savings_fund_members_${fundId}`,
      `savings_fund_income_${fundId}`,
      `savings_fund_expenses_${fundId}`,
      `savings_fund_families_${fundId}`,
      `savings_fund_withdrawals_${fundId}`,
      `savings_fund_loans_${fundId}`,
      `savings_fund_settings_${fundId}`
    ];

    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });

    return true;
  };

  // Function to get storage key with fund prefix
  const getFundStorageKey = (baseKey) => {
    // Check if the key already has a fund ID suffix
    if (baseKey.endsWith('_' + currentFundId)) {
      return baseKey;
    }
    
    // Check if the key already has another fund ID suffix
    const keyParts = baseKey.split('_');
    const lastPart = keyParts[keyParts.length - 1];
    const hasFundId = lastPart === 'default' || lastPart.startsWith('fund_');
    
    if (hasFundId) {
      // Replace the existing fund ID with the current one
      const baseKeyWithoutFundId = keyParts.slice(0, -1).join('_');
      return `${baseKeyWithoutFundId}_${currentFundId}`;
    }
    
    // Make sure the base key starts with 'savings_fund_'
    let normalizedBaseKey = baseKey;
    if (!normalizedBaseKey.startsWith('savings_fund_')) {
      normalizedBaseKey = `savings_fund_${normalizedBaseKey}`;
    }
    
    // Add the current fund ID as suffix
    return `${normalizedBaseKey}_${currentFundId}`;
  };

  // Context value
  const contextValue = {
    funds,
    currentFundId,
    currentFund,
    switchFund,
    addFund,
    updateFund,
    deleteFund,
    getFundStorageKey
  };

  return (
    <FundContext.Provider value={contextValue}>
      {children}
    </FundContext.Provider>
  );
};

// Custom hook to use the fund context
export const useFund = () => {
  const context = useContext(FundContext);
  if (!context) {
    throw new Error('useFund must be used within a FundProvider');
  }
  return context;
};

export default FundContext;