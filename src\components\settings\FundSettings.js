import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Divider,
  Alert,
  Snackbar,
  Card,
  CardContent,
  IconButton
} from '@mui/material';
import {
  Save as SaveIcon,
  Settings as SettingsIcon,
  Edit as EditIcon
} from '@mui/icons-material';

// Helper function to safely save settings
const saveSettingsSafely = (settings, setFundSettings, showNotification) => {
  try {
    // Update the global state which triggers the useEffect in App.js to save to localStorage
    setFundSettings(settings);
    showNotification('تم حفظ إعدادات الصندوق بنجاح!', 'success');
    return true;
  } catch (error) {
    console.error('Error saving fund settings:', error);
    showNotification('حدث خطأ أثناء حفظ الإعدادات', 'error');
    return false;
  }
};

const FundSettings = ({ fundSettings, setFundSettings }) => {
  const [localSettings, setLocalSettings] = useState(fundSettings);
  const [editing, setEditing] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });

  // Update local settings when props change to ensure sync
  useEffect(() => {
    setLocalSettings(fundSettings);
  }, [fundSettings]);

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Show notification
  const showNotification = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setLocalSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Save settings
  const handleSaveSettings = () => {
    saveSettingsSafely(localSettings, setFundSettings, showNotification);
    setEditing(false);
  };

  return (
    <Box sx={{ mb: 4 }} dir="rtl">
      <Typography variant="h5" fontWeight="bold" gutterBottom>
        <SettingsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
        إعدادات الصندوق
      </Typography>

      <Card elevation={3} sx={{ mt: 2, borderRadius: '12px' }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" fontWeight="bold">
              معلومات الصندوق الأساسية
            </Typography>
            <IconButton 
              color="primary" 
              onClick={() => setEditing(!editing)}
              size="small"
            >
              <EditIcon />
            </IconButton>
          </Box>
          
          <Divider sx={{ mb: 3 }} />
          
          {editing ? (
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="اسم الصندوق"
                  name="fundName"
                  value={localSettings.fundName}
                  onChange={handleChange}
                  variant="outlined"
                  size="small"
                  inputProps={{ dir: 'rtl' }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="اسم العائلة/المجموعة"
                  name="fundFamily"
                  value={localSettings.fundFamily}
                  onChange={handleChange}
                  variant="outlined"
                  size="small"
                  placeholder="مثال: عائلة آل حويل"
                  inputProps={{ dir: 'rtl' }}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="المشرف العام"
                  name="generalManager"
                  value={localSettings.generalManager}
                  onChange={handleChange}
                  variant="outlined"
                  size="small"
                  inputProps={{ dir: 'rtl' }}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="أمين الصندوق"
                  name="treasurer"
                  value={localSettings.treasurer}
                  onChange={handleChange}
                  variant="outlined"
                  size="small"
                  inputProps={{ dir: 'rtl' }}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="المحصل"
                  name="collector"
                  value={localSettings.collector}
                  onChange={handleChange}
                  variant="outlined"
                  size="small"
                  inputProps={{ dir: 'rtl' }}
                />
              </Grid>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'flex-start', mt: 2 }}>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<SaveIcon />}
                    onClick={handleSaveSettings}
                  >
                    حفظ الإعدادات
                  </Button>
                  <Button
                    variant="outlined"
                    sx={{ mr: 1 }}
                    onClick={() => {
                      setLocalSettings(fundSettings);
                      setEditing(false);
                    }}
                  >
                    إلغاء
                  </Button>
                </Box>
              </Grid>
            </Grid>
          ) : (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      اسم الصندوق
                    </Typography>
                    <Typography variant="h6" fontWeight="bold">
                      {localSettings.fundName}
                    </Typography>
                  </Box>
                  
                  {localSettings.fundFamily && (
                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        اسم العائلة/المجموعة
                      </Typography>
                      <Typography variant="body1">
                        {localSettings.fundFamily}
                      </Typography>
                    </Box>
                  )}
                  
                  <Divider sx={{ my: 1 }} />
                  
                  <Grid container spacing={{ xs: 1, sm: 2 }}>
                    {localSettings.generalManager && (
                      <Grid item xs={12} sm={6} md={4}>
                        <Box sx={{
                          p: { xs: 1.5, sm: 2 },
                          borderRadius: '12px',
                          bgcolor: 'rgba(255, 193, 7, 0.1)',
                          border: '2px solid rgba(255, 193, 7, 0.3)',
                          boxShadow: '0 4px 15px rgba(255, 193, 7, 0.2)',
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: '0 6px 20px rgba(255, 193, 7, 0.3)'
                          }
                        }}>
                          <Typography variant="subtitle2" sx={{
                            color: '#f57c00',
                            fontWeight: 'bold',
                            mb: 1,
                            fontSize: { xs: '0.75rem', sm: '0.875rem' }
                          }}>
                            المدير العام
                          </Typography>
                          <Typography variant="h6" sx={{
                            color: '#e65100',
                            fontWeight: 'bold',
                            fontSize: { xs: '1rem', sm: '1.25rem' },
                            lineHeight: 1.2,
                            wordBreak: 'break-word'
                          }}>
                            {localSettings.generalManager}
                          </Typography>
                        </Box>
                      </Grid>
                    )}

                    {localSettings.treasurer && (
                      <Grid item xs={12} sm={6} md={4}>
                        <Box sx={{
                          p: { xs: 1.5, sm: 2 },
                          borderRadius: '12px',
                          bgcolor: 'rgba(76, 175, 80, 0.1)',
                          border: '2px solid rgba(76, 175, 80, 0.3)',
                          boxShadow: '0 4px 15px rgba(76, 175, 80, 0.2)',
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: '0 6px 20px rgba(76, 175, 80, 0.3)'
                          }
                        }}>
                          <Typography variant="subtitle2" sx={{
                            color: '#388e3c',
                            fontWeight: 'bold',
                            mb: 1,
                            fontSize: { xs: '0.75rem', sm: '0.875rem' }
                          }}>
                            أمين الصندوق
                          </Typography>
                          <Typography variant="h6" sx={{
                            color: '#2e7d32',
                            fontWeight: 'bold',
                            fontSize: { xs: '1rem', sm: '1.25rem' },
                            lineHeight: 1.2,
                            wordBreak: 'break-word'
                          }}>
                            {localSettings.treasurer}
                          </Typography>
                        </Box>
                      </Grid>
                    )}

                    {localSettings.collector && (
                      <Grid item xs={12} sm={6} md={4}>
                        <Box sx={{
                          p: { xs: 1.5, sm: 2 },
                          borderRadius: '12px',
                          bgcolor: 'rgba(33, 150, 243, 0.1)',
                          border: '2px solid rgba(33, 150, 243, 0.3)',
                          boxShadow: '0 4px 15px rgba(33, 150, 243, 0.2)',
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: '0 6px 20px rgba(33, 150, 243, 0.3)'
                          }
                        }}>
                          <Typography variant="subtitle2" sx={{
                            color: '#1976d2',
                            fontWeight: 'bold',
                            mb: 1,
                            fontSize: { xs: '0.75rem', sm: '0.875rem' }
                          }}>
                            المحصل
                          </Typography>
                          <Typography variant="h6" sx={{
                            color: '#1565c0',
                            fontWeight: 'bold',
                            fontSize: { xs: '1rem', sm: '1.25rem' },
                            lineHeight: 1.2,
                            wordBreak: 'break-word'
                          }}>
                            {localSettings.collector}
                          </Typography>
                        </Box>
                      </Grid>
                    )}
                  </Grid>
                </Box>
              </Grid>
            </Grid>
          )}
        </CardContent>
      </Card>

      {/* Notification Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity} variant="filled" sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default FundSettings;
