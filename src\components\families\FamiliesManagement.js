import React, { useState, useEffect, useMemo } from 'react';
import { formatCurrency } from '../../utils/formatters';
import {
  Box,
  Grid,
  Typography,
  Button,
  TextField,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Divider,
  InputAdornment,
  Card,
  CardContent,
  CardActions,
  Collapse,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Badge,
  Chip,
  Menu,
  MenuItem,
  Tooltip,
  DialogContentText,
  Avatar,
  Container,
  Fade
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Add as AddIcon,
  Search as SearchIcon,
  ExpandMore as ExpandMoreIcon,
  Visibility as ViewIcon,
  Person as PersonIcon,
  Close as CloseIcon,
  AccountBalance as BalanceIcon,
  FamilyRestroom as FamilyIcon,
  Phone as PhoneIcon,
  NavigateNext as NavigateNextIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  DragIndicator as DragIcon,
  Groups as GroupsIcon,
  People as PeopleIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';
// import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { useNavigate } from 'react-router-dom';
import moment from 'moment';
import momentHijri from 'moment-hijri';
import FamilyDetails from './FamilyDetails';
import Copyright from '../common/Copyright';

// Styled components
const StatsCard = styled(Card)(({ theme }) => ({
  background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%)',
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: '20px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: '0 15px 40px rgba(0, 0, 0, 0.15)',
  },
}));



const FamiliesManagement = ({ families, addFamily, updateFamily, deleteFamily, dateType, addMember, updateMember, deleteMember, members, updateFamilyStats }) => {
  const navigate = useNavigate();
  const [open, setOpen] = useState(false);
  const [openFamilyDetails, setOpenFamilyDetails] = useState(false);
  const [selectedFamily, setSelectedFamily] = useState(null);
  const [newFamily, setNewFamily] = useState({
    name: '',
    headName: '',
    phone: '',
    address: '',
    notes: ''
  });
  const [search, setSearch] = useState('');
  const [errors, setErrors] = useState({});
  const [isEditing, setIsEditing] = useState(false);
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const [currentFamilyId, setCurrentFamilyId] = useState(null);
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [orderedFamilies, setOrderedFamilies] = useState([]);

  // Initialize ordered families when families prop changes
  useEffect(() => {
    if (families && families.length > 0) {
      // Check if families have order property, if not assign default order
      const familiesWithOrder = families.map((family, index) => ({
        ...family,
        order: family.order !== undefined ? family.order : index
      }));

      // Sort by order
      const sorted = familiesWithOrder.sort((a, b) => a.order - b.order);
      setOrderedFamilies(sorted);
    } else {
      setOrderedFamilies([]);
    }
  }, [families]);

  // Calculate statistics using real-time data
  const statistics = useMemo(() => {
    if (!families || !members) {
      return {
        totalFamilies: 0,
        totalMembers: 0,
        totalBalance: 0,
        averageMembersPerFamily: 0
      };
    }

    // Calculate real-time statistics
    const totalFamilies = families.length;

    // Count members that belong to families
    const familyMemberIds = new Set(families.map(f => f.id));
    const familyMembers = members.filter(member => member.familyId && familyMemberIds.has(member.familyId));
    const totalMembers = familyMembers.length;

    // Calculate total balance from actual member balances
    const totalBalance = familyMembers.reduce((sum, member) => sum + (member.totalBalance || 0), 0);

    // Calculate average members per family
    const averageMembersPerFamily = totalFamilies > 0
      ? Math.round((totalMembers / totalFamilies) * 10) / 10
      : 0;

    return {
      totalFamilies,
      totalMembers,
      totalBalance,
      averageMembersPerFamily
    };
  }, [families, members]);

  // Format date based on type (Gregorian or Hijri)
  const formatDate = (dateString) => {
    if (dateType === 'gregorian') {
      return moment(dateString).locale('ar').format('YYYY/MM/DD');
    } else {
      return momentHijri(dateString).locale('ar').format('iYYYY/iMM/iDD');
    }
  };

  // Format currency
  const formatCurrencyLocal = (amount) => {
    return formatCurrency(amount);
  };

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewFamily({
      ...newFamily,
      [name]: value
    });
  };



  // Filter families based on search input
  const filteredFamilies = orderedFamilies.filter(family =>
    family.name.includes(search) ||
    family.headName?.includes(search) ||
    family.phone?.includes(search)
  );

  // Open dialog
  const handleClickOpen = () => {
    setIsEditing(false);
    setNewFamily({
      name: '',
      headName: '',
      phone: '',
      address: '',
      notes: ''
    });
    setOpen(true);
  };

  // Close dialog
  const handleClose = () => {
    setOpen(false);
    setNewFamily({
      name: '',
      headName: '',
      phone: '',
      address: '',
      notes: ''
    });
    setErrors({});
    setIsEditing(false);
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};
    if (!newFamily.name) {
      newErrors.name = 'الرجاء إدخال اسم العائلة';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Submit form
  const handleSubmit = () => {
    if (validateForm()) {
      // الحصول على معرف الصندوق الحالي
      const currentFundId = localStorage.getItem('current_fund_id') || 'default';
      
      if (isEditing) {
        // تحديث العائلة في الصندوق الحالي فقط
        updateFamily(currentFamilyId, newFamily);
      } else {
        // إضافة العائلة للصندوق الحالي فقط
        addFamily(newFamily);
      }
      handleClose();
    }
  };

  // View family details
  const handleViewFamily = (id) => {
    console.log('محاولة عرض تفاصيل العائلة برقم:', id);
    console.log('قائمة العائلات المتاحة:', families);

    const family = families.find(f => f.id === id);
    console.log('العائلة الموجودة:', family);

    if (family) {
      setSelectedFamily(family);
      setOpenFamilyDetails(true);
      console.log('تم تعيين العائلة المحددة وفتح النافذة');
    } else {
      console.error('لم يتم العثور على العائلة برقم:', id);
    }
  };

  // Close family details dialog
  const handleCloseFamilyDetails = () => {
    setOpenFamilyDetails(false);
    setSelectedFamily(null);
  };

  // Open menu
  const handleMenuOpen = (event, familyId) => {
    setMenuAnchorEl(event.currentTarget);
    setCurrentFamilyId(familyId);
  };

  // Close menu
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
    // لا نقوم بإعادة تعيين معرف العائلة هنا لضمان استمرار توفره للعمليات اللاحقة
    // setCurrentFamilyId(null);
  };

  // Edit family
  const handleEditFamily = () => {
    handleMenuClose();
    const family = families.find(f => f.id === currentFamilyId);
    if (family) {
      setNewFamily({
        name: family.name,
        headName: family.headName || '',
        phone: family.phone || '',
        address: family.address || '',
        notes: family.notes || ''
      });
      setIsEditing(true);
      setOpen(true);
    }
  };

  // Open delete confirmation
  const handleDeleteConfirmOpen = () => {
    handleMenuClose();
    setConfirmDeleteOpen(true);
  };

  // Close delete confirmation
  const handleDeleteConfirmClose = () => {
    setConfirmDeleteOpen(false);
  };

  // Delete family
  const handleDeleteFamily = () => {
    try {
      // نسخ معرف العائلة إلى متغير محلي لضمان عدم فقدانه
      const familyId = currentFamilyId;
      console.log('محاولة حذف العائلة برقم:', familyId);
      
      if (familyId && typeof deleteFamily === 'function') {
        deleteFamily(familyId);
        console.log('تم استدعاء دالة حذف العائلة بنجاح');
      } else {
        console.error('دالة حذف العائلة غير معرفة أو غير صالحة');
      }
    } catch (error) {
      console.error('حدث خطأ أثناء محاولة حذف العائلة:', error);
    } finally {
      // إغلاق نافذة التأكيد
      handleDeleteConfirmClose();
      // إعادة تعيين معرف العائلة الحالي - نؤخر هذه الخطوة لضمان اكتمال عملية الحذف
      setTimeout(() => {
        setCurrentFamilyId(null);
        setMenuAnchorEl(null);
      }, 100);
    }
  };


  return (
    <Box dir="rtl" sx={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      minHeight: '100vh',
      py: 4
    }}>
      <Container maxWidth="xl">
        {/* Header */}
        <Fade in timeout={600}>
          <Box sx={{ mb: 4, textAlign: 'center' }}>
            <Typography
              variant="h3"
              component="h1"
              fontWeight="bold"
              sx={{
                color: 'white',
                mb: 2,
                textShadow: '0 2px 4px rgba(0,0,0,0.3)'
              }}
            >
              إدارة العائلات
            </Typography>
            <Typography variant="h6" sx={{ color: 'rgba(255,255,255,0.8)' }}>
              إدارة وتنظيم العائلات المشاركة في الصندوق
            </Typography>
          </Box>
        </Fade>

        {/* Statistics Cards */}
        <Fade in timeout={800}>
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <StatsCard>
                <CardContent sx={{ textAlign: 'center', py: 3 }}>
                  <Avatar sx={{
                    width: 60,
                    height: 60,
                    mx: 'auto',
                    mb: 2,
                    background: 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)',
                    boxShadow: '0 8px 25px rgba(76, 175, 80, 0.3)'
                  }}>
                    <GroupsIcon sx={{ fontSize: 30, color: 'white' }} />
                  </Avatar>
                  <Typography variant="h4" fontWeight="bold" color="primary">
                    {statistics.totalFamilies}
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    إجمالي العائلات
                  </Typography>
                </CardContent>
              </StatsCard>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <StatsCard>
                <CardContent sx={{ textAlign: 'center', py: 3 }}>
                  <Avatar sx={{
                    width: 60,
                    height: 60,
                    mx: 'auto',
                    mb: 2,
                    background: 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)',
                    boxShadow: '0 8px 25px rgba(33, 150, 243, 0.3)'
                  }}>
                    <PeopleIcon sx={{ fontSize: 30, color: 'white' }} />
                  </Avatar>
                  <Typography variant="h4" fontWeight="bold" color="primary">
                    {statistics.totalMembers}
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    إجمالي الأفراد
                  </Typography>
                </CardContent>
              </StatsCard>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <StatsCard>
                <CardContent sx={{ textAlign: 'center', py: 3 }}>
                  <Avatar sx={{
                    width: 60,
                    height: 60,
                    mx: 'auto',
                    mb: 2,
                    background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
                    boxShadow: '0 8px 25px rgba(255, 152, 0, 0.3)'
                  }}>
                    <TrendingUpIcon sx={{ fontSize: 30, color: 'white' }} />
                  </Avatar>
                  <Typography variant="h4" fontWeight="bold" color="primary">
                    {statistics.averageMembersPerFamily}
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    متوسط الأفراد/عائلة
                  </Typography>
                </CardContent>
              </StatsCard>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <StatsCard>
                <CardContent sx={{ textAlign: 'center', py: 3 }}>
                  <Avatar sx={{
                    width: 60,
                    height: 60,
                    mx: 'auto',
                    mb: 2,
                    background: statistics.totalBalance >= 0
                      ? 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)'
                      : 'linear-gradient(135deg, #f44336 0%, #d32f2f 100%)',
                    boxShadow: statistics.totalBalance >= 0
                      ? '0 8px 25px rgba(76, 175, 80, 0.3)'
                      : '0 8px 25px rgba(244, 67, 54, 0.3)'
                  }}>
                    <BalanceIcon sx={{ fontSize: 30, color: 'white' }} />
                  </Avatar>
                  <Typography
                    variant="h5"
                    fontWeight="bold"
                    color={statistics.totalBalance >= 0 ? 'success.main' : 'error.main'}
                    sx={{ fontSize: { xs: '1.5rem', sm: '2rem' } }}
                  >
                    {formatCurrencyLocal(statistics.totalBalance)}
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    إجمالي الرصيد
                  </Typography>
                </CardContent>
              </StatsCard>
            </Grid>
          </Grid>
        </Fade>

        {/* Controls */}
        <Fade in timeout={1000}>
          <Paper elevation={0} sx={{
            p: 3,
            borderRadius: '20px',
            mb: 4,
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.2)'
          }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h5" fontWeight="bold">
                قائمة العائلات
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleClickOpen}
                sx={{
                  borderRadius: '15px',
                  px: 3,
                  py: 1.5,
                  background: 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)',
                  boxShadow: '0 6px 20px rgba(76, 175, 80, 0.3)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #45a049 0%, #4caf50 100%)',
                    boxShadow: '0 8px 25px rgba(76, 175, 80, 0.4)',
                  }
                }}
              >
                إضافة عائلة جديدة
              </Button>
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 3 }}>
              <TextField
                placeholder="بحث عن عائلة..."
                variant="outlined"
                size="small"
                value={search}
                onChange={e => setSearch(e.target.value)}
                sx={{
                  width: 350,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '15px',
                  }
                }}
                InputProps={{
                  startAdornment: <SearchIcon color="action" sx={{ ml: 1 }} />,
                }}
              />
            </Box>

            {/* Families Grid */}
            <Grid container spacing={3}>
              {filteredFamilies.length > 0 ? (
                filteredFamilies.map((family, index) => (
                  <Grid item xs={12} sm={6} md={4} lg={3} key={family.id}>
                    <Card
                      elevation={2}
                      sx={{
                        height: '100%',
                        borderRadius: '15px',
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                        border: '1px solid rgba(0,0,0,0.05)',
                        background: 'linear-gradient(to bottom, #ffffff, #f9f9f9)',
                        '&:hover': {
                          transform: 'translateY(-5px)',
                          boxShadow: '0 10px 20px rgba(25, 118, 210, 0.15)',
                          borderColor: 'primary.light'
                        }
                      }}
                    >
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, justifyContent: 'space-between' }}>
                          <IconButton
                            size="small"
                            onClick={(e) => handleMenuOpen(e, family.id)}
                            sx={{ order: 3 }}
                          >
                            <MoreVertIcon />
                          </IconButton>

                          <Box sx={{ display: 'flex', alignItems: 'center', flex: 1, order: 1 }}>
                            <Typography
                              variant="h6"
                              component="div"
                              sx={{
                                fontWeight: 'bold',
                                textAlign: 'right',
                                fontSize: '1.1rem',
                                flex: 1,
                                mr: 1
                              }}
                            >
                              {family.name}
                            </Typography>
                            <Avatar sx={{
                              width: 40,
                              height: 40,
                              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                              boxShadow: '0 4px 15px rgba(102, 126, 234, 0.3)',
                              order: 2
                            }}>
                              <FamilyIcon sx={{ color: 'white', fontSize: '1.2rem' }} />
                            </Avatar>
                          </Box>
                        </Box>

                        <Divider sx={{ my: 2 }} />

                        <Box sx={{ textAlign: 'right' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5, justifyContent: 'flex-end' }}>
                            <Typography variant="body2" fontWeight="medium">
                              {family.headName || 'غير محدد'}
                            </Typography>
                            <PersonIcon fontSize="small" sx={{ ml: 1, mr: 'auto', color: 'text.secondary' }} />
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5, justifyContent: 'flex-end' }}>
                            <Typography variant="body2" color="text.secondary">
                              {family.phone || 'لا يوجد رقم هاتف'}
                            </Typography>
                            <PhoneIcon fontSize="small" sx={{ ml: 1, mr: 'auto', color: 'text.secondary' }} />
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, justifyContent: 'flex-end' }}>
                            <Chip
                              label={`${members ? members.filter(m => m.familyId === family.id).length : 0} أفراد`}
                              size="small"
                              icon={<PeopleIcon />}
                              sx={{
                                background: 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)',
                                color: 'white',
                                '& .MuiChip-icon': { color: 'white' }
                              }}
                            />
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: 2 }}>
                            <Button
                              size="small"
                              variant="outlined"
                              startIcon={<NavigateNextIcon />}
                              onClick={() => handleViewFamily(family.id)}
                              sx={{
                                borderRadius: '15px',
                                borderColor: '#667eea',
                                color: '#667eea',
                                '&:hover': {
                                  borderColor: '#667eea',
                                  backgroundColor: 'rgba(102, 126, 234, 0.1)'
                                }
                              }}
                            >
                              التفاصيل
                            </Button>

                            <Typography
                              variant="body1"
                              fontWeight="bold"
                              color={(() => {
                                const familyBalance = members
                                  ? members.filter(m => m.familyId === family.id).reduce((sum, m) => sum + (m.totalBalance || 0), 0)
                                  : 0;
                                return familyBalance >= 0 ? 'success.main' : 'error.main';
                              })()}
                              sx={{ fontSize: '0.9rem' }}
                            >
                              {formatCurrencyLocal(members
                                ? members.filter(m => m.familyId === family.id).reduce((sum, m) => sum + (m.totalBalance || 0), 0)
                                : 0
                              )}
                            </Typography>
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))
              ) : (
                <Grid item xs={12}>
                  <Paper sx={{
                    p: 4,
                    textAlign: 'center',
                    borderRadius: '20px',
                    background: 'rgba(255, 255, 255, 0.7)',
                    backdropFilter: 'blur(10px)'
                  }}>
                    <Avatar sx={{
                      width: 80,
                      height: 80,
                      mx: 'auto',
                      mb: 2,
                      background: 'linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%)'
                    }}>
                      <GroupsIcon sx={{ fontSize: 40, color: '#999' }} />
                    </Avatar>
                    <Typography variant="h6" color="text.secondary" mb={1}>
                      {search ? 'لا توجد نتائج للبحث' : 'لا توجد عائلات حتى الآن'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {search ? 'جرب البحث بكلمات مختلفة' : 'ابدأ بإضافة عائلة جديدة'}
                    </Typography>
                  </Paper>
                </Grid>
              )}
            </Grid>
          </Paper>
        </Fade>
      </Container>

      {/* Action Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        slotProps={{
          paper: {
            sx: {
              mt: 0.5,
              ml: 0.5,
              '& .MuiMenuItem-root': {
                px: 2,
                py: 1,
                my: 0.5,
                borderRadius: 1,
              },
            },
          },
        }}
      >
        <MenuItem onClick={handleEditFamily}>
          <EditIcon fontSize="small" sx={{ ml: 1 }} />
          تعديل
        </MenuItem>
        <MenuItem onClick={handleDeleteConfirmOpen} sx={{ color: 'error.main' }}>
          <DeleteIcon fontSize="small" sx={{ ml: 1 }} />
          حذف
        </MenuItem>
      </Menu>

      {/* Add/Edit Family Dialog */}
      <Dialog 
        open={open} 
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
        dir="rtl"
      >
        <DialogTitle sx={{ 
          bgcolor: 'primary.main', 
          color: 'white', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          borderBottom: '1px solid',
          borderColor: 'divider' 
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <FamilyIcon sx={{ mr: 1 }} />
            <Typography variant="h6" fontWeight="bold">
              {isEditing ? 'تعديل العائلة' : 'إضافة عائلة جديدة'}
            </Typography>
          </Box>
          <IconButton 
            aria-label="close" 
            onClick={handleClose} 
            sx={{ color: 'white' }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ mt: 2, pt: 2 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                autoFocus
                name="name"
                label="اسم العائلة"
                variant="outlined"
                value={newFamily.name}
                onChange={handleInputChange}
                error={!!errors.name}
                helperText={errors.name}
                inputProps={{ style: { textAlign: 'right' } }}
                InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                name="headName"
                label="اسم رب الأسرة"
                variant="outlined"
                value={newFamily.headName}
                onChange={handleInputChange}
                error={!!errors.headName}
                helperText={errors.headName}
                inputProps={{ style: { textAlign: 'right' } }}
                InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                name="phone"
                label="رقم الهاتف"
                variant="outlined"
                value={newFamily.phone}
                onChange={handleInputChange}
                error={!!errors.phone}
                helperText={errors.phone}
                inputProps={{ style: { textAlign: 'right' } }}
                InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                name="address"
                label="العنوان"
                variant="outlined"
                multiline
                rows={2}
                value={newFamily.address}
                onChange={handleInputChange}
                inputProps={{ style: { textAlign: 'right' } }}
                InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                name="notes"
                label="ملاحظات"
                variant="outlined"
                multiline
                rows={3}
                value={newFamily.notes}
                onChange={handleInputChange}
                inputProps={{ style: { textAlign: 'right' } }}
                InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 2, justifyContent: 'flex-start' }}>
          <Button 
            onClick={handleClose} 
            color="inherit" 
            sx={{ ml: 2, mr: 0 }}
          >
            إلغاء
          </Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained" 
            color="primary"
            startIcon={isEditing ? <EditIcon /> : <AddIcon />}
          >
            {isEditing ? 'حفظ التغييرات' : 'إضافة العائلة'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={confirmDeleteOpen}
        onClose={handleDeleteConfirmClose}
        dir="rtl"
      >
        <DialogTitle sx={{ bgcolor: 'error.main', color: 'white' }}>
          تأكيد حذف العائلة
        </DialogTitle>
        <DialogContent sx={{ mt: 2 }}>
          <DialogContentText>
            هل أنت متأكد من رغبتك في حذف هذه العائلة؟ في حال وجود أعضاء في هذه العائلة، سيتم إلغاء ارتباطهم بها.
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ p: 2, justifyContent: 'flex-start' }}>
          <Button onClick={handleDeleteConfirmClose} color="inherit">
            إلغاء
          </Button>
          <Button 
            onClick={handleDeleteFamily} 
            variant="contained" 
            color="error"
            startIcon={<DeleteIcon />}
          >
            حذف
          </Button>
        </DialogActions>
      </Dialog>

      {/* Family Details Dialog */}
      <FamilyDetails
        family={selectedFamily}
        open={openFamilyDetails && selectedFamily !== null}
        onClose={handleCloseFamilyDetails}
        dateType={dateType}
        updateFamily={updateFamily}
        deleteFamily={deleteFamily}
        addMember={addMember}
        updateMember={updateMember}
        deleteMember={deleteMember}
      />

      {/* Copyright */}
      <Copyright variant="footer" showDivider={false} />
    </Box>
  );
};

export default FamiliesManagement;
