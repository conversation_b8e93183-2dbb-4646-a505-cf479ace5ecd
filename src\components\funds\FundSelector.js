import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActionArea,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Divider,
  IconButton,
  Snackbar,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Menu
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Add as AddIcon,
  ArrowForward as ArrowForwardIcon,
  FamilyRestroom as FamilyIcon,
  Close as CloseIcon,
  Delete as DeleteIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

// Styled components
const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: '12px',
  transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: theme.shadows[10],
  },
}));

const IconWrapper = styled(Box)(({ theme, bgcolor }) => ({
  width: 80,
  height: 80,
  borderRadius: '50%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: bgcolor || theme.palette.primary.main,
  color: 'white',
  margin: '0 auto 24px auto'
}));

// Local storage key for funds list
const FUNDS_LIST_KEY = 'savings_funds_list';

// Helper function to safely parse JSON from localStorage
const safelyParseJSON = (key, defaultValue = []) => {
  try {
    const storedData = localStorage.getItem(key);
    return storedData ? JSON.parse(storedData) : defaultValue;
  } catch (error) {
    console.error(`Error parsing data for key ${key}:`, error);
    return defaultValue;
  }
};

// Helper function to safely save JSON to localStorage
const safelySaveJSON = (key, data) => {
  try {
    localStorage.setItem(key, JSON.stringify(data));
    return true;
  } catch (error) {
    console.error(`Error saving data for key ${key}:`, error);
    return false;
  }
};

const FundSelector = ({ currentFundId, onSelectFund, onCreateFund }) => {
  const navigate = useNavigate();
  const [funds, setFunds] = useState(() => safelyParseJSON(FUNDS_LIST_KEY, []));
  const [openDialog, setOpenDialog] = useState(false);
  const [newFund, setNewFund] = useState({
    fundName: '',
    fundFamily: '',
    generalManager: '',
    treasurer: '',
    collector: ''
  });
  const [errors, setErrors] = useState({});
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [confirmDeleteDialog, setConfirmDeleteDialog] = useState({ open: false, fundId: null });

  // Load funds on mount - no default fund creation

  // Save funds to localStorage when they change
  useEffect(() => {
    safelySaveJSON(FUNDS_LIST_KEY, funds);
  }, [funds]);

  const handleOpenDialog = () => {
    setOpenDialog(true);
    setErrors({});
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setNewFund({
      fundName: '',
      fundFamily: '',
      generalManager: '',
      treasurer: '',
      collector: ''
    });
    setErrors({});
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewFund(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error for this field if it exists
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    if (!newFund.fundName) {
      newErrors.fundName = 'يرجى إدخال اسم الصندوق';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCreateFund = () => {
    if (validateForm()) {
      const newFundData = {
        id: `fund_${Date.now()}`,
        ...newFund,
        createdAt: new Date().toISOString()
      };

      // Add to funds list
      setFunds(prev => [...prev, newFundData]);

      // Create fund-specific storage keys with empty data
      const fundKeys = {
        [`savings_fund_members_${newFundData.id}`]: [],
        [`savings_fund_income_${newFundData.id}`]: [],
        [`savings_fund_expenses_${newFundData.id}`]: [],
        [`savings_fund_families_${newFundData.id}`]: [],
        [`savings_fund_settings_${newFundData.id}`]: newFundData
      };

      // Save empty data for each key
      Object.entries(fundKeys).forEach(([key, value]) => {
        safelySaveJSON(key, value);
      });

      // Call the onCreateFund callback if provided
      if (onCreateFund) {
        onCreateFund(newFundData);
      }

      // Show success message
      setSnackbar({
        open: true,
        message: 'تم إنشاء الصندوق الجديد بنجاح',
        severity: 'success'
      });

      handleCloseDialog();
    }
  };

  const handleSelectFund = (fundId) => {
    // تخزين معرف الصندوق الحالي في localStorage
    localStorage.setItem('current_fund_id', fundId);
    
    // استدعاء الدالة المقدمة من الأب إذا كانت موجودة
    if (onSelectFund) {
      onSelectFund(fundId);
    }
    
    // الانتقال إلى صفحة الرئيسية
    navigate('/home');
    
    // عرض رسالة نجاح
    setSnackbar({
      open: true,
      message: 'تم تبديل الصندوق بنجاح',
      severity: 'success'
    });
    // Find the selected fund
    const selectedFund = funds.find(fund => fund.id === fundId);
    if (selectedFund && onSelectFund) {
      onSelectFund(selectedFund);
    }
    navigate('/dashboard');
  };

  const handleDeleteFund = (e, fundId) => {
    e.stopPropagation(); // Prevent card click event
    setConfirmDeleteDialog({ open: true, fundId });
  };

  const confirmDeleteFund = () => {
    const fundId = confirmDeleteDialog.fundId;
    if (!fundId) {
      setConfirmDeleteDialog({ open: false, fundId: null });
      return;
    }

    // Remove fund from list
    setFunds(prev => prev.filter(fund => fund.id !== fundId));

    // Remove fund-specific data from localStorage
    const keysToRemove = [
      `savings_fund_members_${fundId}`,
      `savings_fund_income_${fundId}`,
      `savings_fund_expenses_${fundId}`,
      `savings_fund_families_${fundId}`,
      `savings_fund_withdrawals_${fundId}`,
      `savings_fund_loans_${fundId}`,
      `savings_fund_settings_${fundId}`
    ];

    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });

    // Show success message
    setSnackbar({
      open: true,
      message: 'تم حذف الصندوق بنجاح',
      severity: 'success'
    });

    setConfirmDeleteDialog({ open: false, fundId: null });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <Box sx={{ direction: 'rtl', py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
        الصناديق المتاحة
      </Typography>
      <Divider sx={{ mb: 6 }} />

      <Grid container spacing={4}>
        {funds.map((fund) => (
          <Grid item xs={12} sm={6} md={4} key={fund.id}>
            <StyledCard elevation={3}>
              <CardActionArea onClick={() => handleSelectFund(fund.id)}>
                <CardContent sx={{ textAlign: 'center', py: 4, position: 'relative' }}>
                  <IconButton
                    size="small"
                    color="error"
                    sx={{ position: 'absolute', top: 8, left: 8 }}
                    onClick={(e) => handleDeleteFund(e, fund.id)}
                  >
                    <DeleteIcon />
                  </IconButton>
                  <IconWrapper bgcolor={fund.id === currentFundId ? "#4caf50" : "#1976d2"}>
                    <FamilyIcon sx={{ fontSize: 40 }} />
                  </IconWrapper>
                  <Typography variant="h5" fontWeight="bold" mb={2}>
                    {fund.fundName}
                  </Typography>
                  {fund.fundFamily && (
                    <Typography variant="subtitle1" color="text.secondary" mb={3}>
                      {fund.fundFamily}
                    </Typography>
                  )}
                  <Button
                    variant="contained"
                    color={fund.id === currentFundId ? "success" : "primary"}
                    endIcon={<ArrowForwardIcon />}
                    sx={{ mt: 2 }}
                  >
                    {fund.id === currentFundId ? "الصندوق الحالي" : "اختيار الصندوق"}
                  </Button>
                </CardContent>
              </CardActionArea>
            </StyledCard>
          </Grid>
        ))}

        {/* إضافة صندوق جديد */}
        <Grid item xs={12} sm={6} md={4}>
          <StyledCard elevation={3}>
            <CardActionArea onClick={handleOpenDialog}>
              <CardContent sx={{ textAlign: 'center', py: 4 }}>
                <IconWrapper bgcolor="#4caf50">
                  <AddIcon sx={{ fontSize: 40 }} />
                </IconWrapper>
                <Typography variant="h5" fontWeight="bold" mb={2}>
                  إضافة صندوق جديد
                </Typography>
                <Typography variant="subtitle1" color="text.secondary" mb={3}>
                  إنشاء صندوق توفير جديد لأعضاء آخرين أو لغرض آخر
                </Typography>
                <Button
                  variant="contained"
                  color="success"
                  startIcon={<AddIcon />}
                  sx={{ mt: 2 }}
                >
                  إنشاء صندوق جديد
                </Button>
              </CardContent>
            </CardActionArea>
          </StyledCard>
        </Grid>
      </Grid>

      {/* Dialog for creating a new fund */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth dir="rtl">
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">إنشاء صندوق جديد</Typography>
            <IconButton onClick={handleCloseDialog} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="اسم الصندوق"
                name="fundName"
                value={newFund.fundName}
                onChange={handleInputChange}
                error={!!errors.fundName}
                helperText={errors.fundName}
                required
                inputProps={{ dir: 'rtl' }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="اسم العائلة/المجموعة"
                name="fundFamily"
                value={newFund.fundFamily}
                onChange={handleInputChange}
                inputProps={{ dir: 'rtl' }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="المشرف العام"
                name="generalManager"
                value={newFund.generalManager}
                onChange={handleInputChange}
                inputProps={{ dir: 'rtl' }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="أمين الصندوق"
                name="treasurer"
                value={newFund.treasurer}
                onChange={handleInputChange}
                inputProps={{ dir: 'rtl' }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="المحصل"
                name="collector"
                value={newFund.collector}
                onChange={handleInputChange}
                inputProps={{ dir: 'rtl' }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button variant="outlined" onClick={handleCloseDialog}>
            إلغاء
          </Button>
          <Button variant="contained" color="primary" onClick={handleCreateFund}>
            إنشاء الصندوق
          </Button>
        </DialogActions>
      </Dialog>

      {/* Confirmation dialog for deleting a fund */}
      <Dialog
        open={confirmDeleteDialog.open}
        onClose={() => setConfirmDeleteDialog({ open: false, fundId: null })}
        maxWidth="xs"
        fullWidth
        dir="rtl"
      >
        <DialogTitle>
          <Typography variant="h6">تأكيد حذف الصندوق</Typography>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1">
            هل أنت متأكد من رغبتك في حذف هذا الصندوق؟ سيتم حذف جميع البيانات المرتبطة به بشكل نهائي.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button
            variant="outlined"
            onClick={() => setConfirmDeleteDialog({ open: false, fundId: null })}
          >
            إلغاء
          </Button>
          <Button variant="contained" color="error" onClick={confirmDeleteFund}>
            حذف
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default FundSelector;