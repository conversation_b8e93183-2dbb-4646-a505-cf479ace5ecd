import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Divider,
  IconButton,
  Snackbar,
  Alert,
  Paper
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Add as AddIcon,
  Close as CloseIcon,
  FamilyRestroom as FamilyIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useFund } from '../../contexts/FundContext';
import Copyright from '../common/Copyright';

// Styled components
const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: '12px',
  transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: theme.shadows[10],
  },
}));

const IconWrapper = styled(Box)(({ theme, bgcolor }) => ({
  width: 120,
  height: 120,
  borderRadius: '50%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: bgcolor || theme.palette.primary.main,
  color: 'white',
  margin: '0 auto 24px auto'
}));

const CreateFund = () => {
  const navigate = useNavigate();
  const { addFund } = useFund();
  const [openDialog, setOpenDialog] = useState(false);
  const [newFund, setNewFund] = useState({
    fundName: '',
    fundFamily: '',
    generalManager: '',
    treasurer: '',
    collector: ''
  });
  const [errors, setErrors] = useState({});
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  const handleOpenDialog = () => {
    setOpenDialog(true);
    setErrors({});
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setNewFund({
      fundName: '',
      fundFamily: '',
      generalManager: '',
      treasurer: '',
      collector: ''
    });
    setErrors({});
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewFund(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error for this field if it exists
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    if (!newFund.fundName) {
      newErrors.fundName = 'يرجى إدخال اسم الصندوق';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCreateFund = () => {
    if (validateForm()) {
      try {
        const createdFund = addFund(newFund);
        
        // Show success message
        setSnackbar({
          open: true,
          message: 'تم إنشاء الصندوق الجديد بنجاح',
          severity: 'success'
        });

        handleCloseDialog();
        
        // Navigate to home page after a short delay
        setTimeout(() => {
          navigate('/');
        }, 1500);
      } catch (error) {
        setSnackbar({
          open: true,
          message: 'حدث خطأ أثناء إنشاء الصندوق',
          severity: 'error'
        });
      }
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <Box sx={{ direction: 'rtl', py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
        إنشاء صندوق جديد
      </Typography>
      <Divider sx={{ mb: 6 }} />

      <Grid container justifyContent="center">
        <Grid item xs={12} md={8} lg={6}>
          <Paper elevation={0} sx={{ p: 4, mb: 4, bgcolor: 'rgba(25, 118, 210, 0.05)', borderRadius: '12px' }}>
            <Typography variant="body1" color="text.secondary" textAlign="center">
              يمكنك إنشاء صندوق توفير جديد لإدارة مجموعة مختلفة من الأعضاء أو لغرض مختلف
            </Typography>
          </Paper>
          
          <StyledCard elevation={3}>
            <CardContent sx={{ textAlign: 'center', py: 6 }}>
              <IconWrapper bgcolor="#4caf50">
                <AddIcon sx={{ fontSize: 60 }} />
              </IconWrapper>
              <Typography variant="h5" fontWeight="bold" mb={2}>
                إنشاء صندوق توفير جديد
              </Typography>
              <Typography variant="subtitle1" color="text.secondary" mb={4}>
                ابدأ بإنشاء صندوق جديد لإدارة مدخرات مجموعة من الأعضاء
              </Typography>
              <Button
                variant="contained"
                color="success"
                size="large"
                startIcon={<AddIcon />}
                onClick={handleOpenDialog}
                sx={{ mt: 2, px: 4, py: 1.5 }}
              >
                إنشاء صندوق جديد
              </Button>
            </CardContent>
          </StyledCard>
        </Grid>
      </Grid>

      {/* Dialog for creating a new fund */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth dir="rtl">
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">إنشاء صندوق جديد</Typography>
            <IconButton onClick={handleCloseDialog} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="اسم الصندوق"
                name="fundName"
                value={newFund.fundName}
                onChange={handleInputChange}
                error={!!errors.fundName}
                helperText={errors.fundName}
                required
                inputProps={{ dir: 'rtl' }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="اسم العائلة/المجموعة"
                name="fundFamily"
                value={newFund.fundFamily}
                onChange={handleInputChange}
                inputProps={{ dir: 'rtl' }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="المشرف العام"
                name="generalManager"
                value={newFund.generalManager}
                onChange={handleInputChange}
                inputProps={{ dir: 'rtl' }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="أمين الصندوق"
                name="treasurer"
                value={newFund.treasurer}
                onChange={handleInputChange}
                inputProps={{ dir: 'rtl' }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="المحصل"
                name="collector"
                value={newFund.collector}
                onChange={handleInputChange}
                inputProps={{ dir: 'rtl' }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button variant="outlined" onClick={handleCloseDialog}>
            إلغاء
          </Button>
          <Button variant="contained" color="primary" onClick={handleCreateFund}>
            إنشاء الصندوق
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Copyright */}
      <Copyright />
    </Box>
  );
};

export default CreateFund;
