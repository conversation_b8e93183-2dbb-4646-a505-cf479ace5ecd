// Service Worker لمنع التخزين المؤقت تماماً
const CACHE_NAME = 'no-cache-' + Date.now();

// تثبيت Service Worker
self.addEventListener('install', (event) => {
  console.log('Service Worker: تم التثبيت');
  // تخطي مرحلة الانتظار والتفعيل فوراً
  self.skipWaiting();
});

// تفعيل Service Worker
self.addEventListener('activate', (event) => {
  console.log('Service Worker: تم التفعيل');
  event.waitUntil(
    // مسح جميع الكاشات الموجودة
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          console.log('حذف الكاش:', cacheName);
          return caches.delete(cacheName);
        })
      );
    }).then(() => {
      // السيطرة على جميع العملاء فوراً
      return self.clients.claim();
    })
  );
});

// اعتراض الطلبات - عدم استخدام الكاش نهائياً
self.addEventListener('fetch', (event) => {
  event.respondWith(
    // جلب المحتوى من الشبكة دائماً مع منع التخزين المؤقت
    fetch(event.request, {
      cache: 'no-store',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    }).catch(() => {
      // في حالة فشل الشبكة، إرجاع استجابة فارغة
      return new Response('', {
        status: 200,
        statusText: 'OK'
      });
    })
  );
});
