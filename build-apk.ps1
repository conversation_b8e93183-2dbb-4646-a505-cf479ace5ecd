# سكريبت بناء APK مع ترقيم تلقائي
# Build APK Script with Auto Versioning

param(
    [string]$VersionName = "",
    [string]$Description = ""
)

Write-Host "🚀 بدء عملية بناء APK مع ترقيم..." -ForegroundColor Green

# الحصول على التاريخ والوقت الحالي
$DateTime = Get-Date -Format "yyyy-MM-dd_HH-mm"
$DateOnly = Get-Date -Format "yyyy-MM-dd"

# قراءة رقم الإصدار الحالي من build.gradle
$BuildGradlePath = "android\app\build.gradle"
$BuildGradleContent = Get-Content $BuildGradlePath
$VersionCodeLine = $BuildGradleContent | Where-Object { $_ -match "versionCode\s+(\d+)" }
$VersionNameLine = $BuildGradleContent | Where-Object { $_ -match 'versionName\s+"([^"]+)"' }

if ($VersionCodeLine -match "versionCode\s+(\d+)") {
    $CurrentVersionCode = [int]$Matches[1]
    $NewVersionCode = $CurrentVersionCode + 1
} else {
    $NewVersionCode = 1
}

if ($VersionNameLine -match 'versionName\s+"([^"]+)"') {
    $CurrentVersionName = $Matches[1]
} else {
    $CurrentVersionName = "1.0.0"
}

# تحديد اسم الإصدار الجديد
if ($VersionName -eq "") {
    $VersionParts = $CurrentVersionName.Split('.')
    $MajorVersion = [int]$VersionParts[0]
    $MinorVersion = [int]$VersionParts[1]
    $PatchVersion = [int]$VersionParts[2]
    
    $PatchVersion++
    $NewVersionName = "$MajorVersion.$MinorVersion.$PatchVersion"
} else {
    $NewVersionName = $VersionName
}

Write-Host "📊 معلومات الإصدار:" -ForegroundColor Yellow
Write-Host "   الإصدار الحالي: $CurrentVersionName (Code: $CurrentVersionCode)" -ForegroundColor White
Write-Host "   الإصدار الجديد: $NewVersionName (Code: $NewVersionCode)" -ForegroundColor White

# تحديث build.gradle
Write-Host "🔧 تحديث ملف build.gradle..." -ForegroundColor Blue
$UpdatedContent = $BuildGradleContent -replace "versionCode\s+\d+", "versionCode $NewVersionCode"
$UpdatedContent = $UpdatedContent -replace 'versionName\s+"[^"]+"', "versionName `"$NewVersionName`""
$UpdatedContent | Set-Content $BuildGradlePath

# بناء React
Write-Host "⚛️ بناء مشروع React..." -ForegroundColor Blue
npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في بناء React!" -ForegroundColor Red
    exit 1
}

# مزامنة Capacitor
Write-Host "🔄 مزامنة Capacitor..." -ForegroundColor Blue
npx cap sync android
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في مزامنة Capacitor!" -ForegroundColor Red
    exit 1
}

# بناء APK
Write-Host "📱 بناء APK..." -ForegroundColor Blue
Set-Location android
.\gradlew clean assembleDebug
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في بناء APK!" -ForegroundColor Red
    Set-Location ..
    exit 1
}
Set-Location ..

# نسخ APK مع اسم مخصص
$SourceAPK = "android\app\build\outputs\apk\debug\app-debug.apk"
$TargetDir = "releases"
if (!(Test-Path $TargetDir)) {
    New-Item -ItemType Directory -Path $TargetDir
}

$APKName = "savings-fund-v$NewVersionName-$DateTime"
if ($Description -ne "") {
    $APKName += "-$Description"
}
$APKName += ".apk"

$TargetAPK = "$TargetDir\$APKName"

Copy-Item $SourceAPK $TargetAPK

# إنشاء ملف معلومات الإصدار
$ReleaseInfo = @"
إصدار تطبيق صندوق التوفير
==========================

📱 اسم الملف: $APKName
📊 رقم الإصدار: $NewVersionName (Code: $NewVersionCode)
📅 تاريخ البناء: $DateTime
📝 الوصف: $Description
💾 الحجم: $([math]::Round((Get-Item $TargetAPK).Length / 1MB, 2)) MB

🔧 التحديثات المضمنة:
- جميع التحسينات الأخيرة في الكود
- تحسينات واجهة المستخدم
- إصلاحات الأخطاء

📍 المسار: $TargetAPK
"@

$ReleaseInfo | Out-File "$TargetDir\release-info-v$NewVersionName.txt" -Encoding UTF8

Write-Host "✅ تم بناء APK بنجاح!" -ForegroundColor Green
Write-Host "📱 اسم الملف: $APKName" -ForegroundColor Yellow
Write-Host "📍 المسار: $TargetAPK" -ForegroundColor Yellow
Write-Host "📊 الحجم: $([math]::Round((Get-Item $TargetAPK).Length / 1MB, 2)) MB" -ForegroundColor Yellow

# عرض قائمة الإصدارات
Write-Host "`n📋 الإصدارات المتوفرة:" -ForegroundColor Cyan
Get-ChildItem $TargetDir -Filter "*.apk" | Sort-Object LastWriteTime -Descending | ForEach-Object {
    Write-Host "   📱 $($_.Name) - $($_.LastWriteTime)" -ForegroundColor White
}
