@import url(https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap);html{direction:rtl}*{box-sizing:border-box;padding:0}*,body{margin:0}body{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;direction:rtl;font-family:<PERSON><PERSON><PERSON>,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;text-align:right}code{font-family:source-code-pro,Menlo,Monaco,Consolas,Courier New,monospace}.MuiFormControl-root,.MuiInputBase-input,.MuiTableCell-root{text-align:right}.arabic-number{font-family:Tajawal,sans-serif}::-webkit-scrollbar{height:8px;width:8px}::-webkit-scrollbar-track{background:#f1f1f1}::-webkit-scrollbar-thumb{background:#888;border-radius:4px}::-webkit-scrollbar-thumb:hover{background:#555}.fade-in{animation:fadeIn .5s ease-in-out}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}@media (max-width:768px){.MuiTypography-h4{font-size:1.5rem!important}.MuiTypography-h5{font-size:1.25rem!important}.MuiTypography-h6{font-size:1.1rem!important}.MuiButton-root{font-size:.8rem!important;min-height:36px!important;padding:6px 12px!important}.MuiCard-root{margin:4px!important}.MuiCardContent-root{padding:12px!important}.MuiTableCell-root{font-size:.8rem!important;padding:8px!important}.MuiTextField-root{margin:4px 0!important}.MuiInputBase-root{font-size:.9rem!important}}@media (max-width:480px){.MuiTypography-h4{font-size:1.3rem!important}.MuiTypography-h5{font-size:1.1rem!important}.MuiButton-root{font-size:.75rem!important;min-height:32px!important;padding:4px 8px!important}.MuiCardContent-root{padding:8px!important}.MuiTableCell-root{font-size:.75rem!important;padding:4px!important}.MuiTableContainer-root{max-width:100%!important;overflow-x:auto!important}.MuiTable-root{min-width:300px!important}.MuiButton-root{position:relative!important;z-index:1!important}.MuiBox-root{box-sizing:border-box!important}}@media print{@page{size:A4;margin:.5in}body{font-size:12px!important;line-height:1.3!important}.no-print{display:none!important}.MuiPaper-root{border:1px solid #ddd!important;box-shadow:none!important}.MuiGrid-container{margin:0!important}.MuiGrid-item{padding:2px!important}.MuiTable-root{font-size:10px!important}.MuiTableCell-root{border:1px solid #ccc!important;padding:2px 4px!important}.MuiTableContainer-root,.MuiTableRow-root{page-break-inside:avoid}.MuiTableRow-root{page-break-after:auto}}.App{direction:rtl;text-align:right}.App-logo{height:40vmin;pointer-events:none}@media (prefers-reduced-motion:no-preference){.App-logo{animation:App-logo-spin 20s linear infinite}}.App-header{align-items:center;background-color:#282c34;color:#fff;display:flex;flex-direction:column;font-size:calc(10px + 2vmin);justify-content:center;min-height:100vh}.App-link{color:#61dafb}@keyframes App-logo-spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}.app-container{direction:rtl;display:flex;height:100vh;text-align:right}.content-area{flex-grow:1;margin-right:240px;overflow-y:auto;padding:16px}@media (max-width:768px){.app-container{flex-direction:column;height:100vh}.content-area{flex:1 1;margin-right:0;overflow-y:auto;padding:8px}}@media (max-width:480px){.content-area{padding:4px}}.card{transition:all .3s ease}.card:hover{box-shadow:0 8px 16px #0000001a;transform:translateY(-5px)}.icon-button{transition:transform .2s}.icon-button:hover{transform:scale(1.1)}.animate-card{animation:fadeInUp .5s ease-out}@keyframes fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}input[type=number]{font-family:Tajawal,sans-serif}.MuiFormControl-root,.MuiTableCell-body,.MuiTableCell-head{text-align:right!important}button.MuiButton-root{direction:rtl}.MuiInputBase-root{text-align:right}.MuiSelect-select{padding-left:32px!important;padding-right:14px!important;text-align:right!important}.MuiMenuItem-root{justify-content:flex-start;text-align:right}
/*# sourceMappingURL=main.0d2f4fed.css.map*/