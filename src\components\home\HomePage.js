import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActionArea,
  Divider,
  Paper,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  Alert,
  Avatar,
  Chip,
  Container,
  Fade,
  Zoom
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Add as AddIcon,
  AccountBalance as AccountBalanceIcon,
  Delete as DeleteIcon,
  Dashboard as DashboardIcon,
  Star as StarIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useFund } from '../../contexts/FundContext';
import Copyright from '../common/Copyright';

const StyledCard = styled(Card)(({ theme, isActive }) => ({
  height: '100%',
  borderRadius: '20px',
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  background: isActive
    ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    : 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
  border: isActive ? '2px solid #667eea' : '1px solid rgba(0,0,0,0.1)',
  boxShadow: isActive
    ? '0 20px 40px rgba(102, 126, 234, 0.3)'
    : '0 8px 25px rgba(0,0,0,0.1)',
  position: 'relative',
  overflow: 'hidden',
  '&:hover': {
    transform: 'translateY(-10px) scale(1.02)',
    boxShadow: isActive
      ? '0 25px 50px rgba(102, 126, 234, 0.4)'
      : '0 15px 35px rgba(0,0,0,0.2)',
  },
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: isActive
      ? 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)'
      : 'linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.4) 100%)',
    pointerEvents: 'none',
  }
}));

const GradientBackground = styled(Box)(({ theme }) => ({
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  minHeight: '100vh',
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
    pointerEvents: 'none',
  }
}));

const FloatingCard = styled(Paper)(({ theme }) => ({
  borderRadius: '25px',
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  boxShadow: '0 25px 50px rgba(0, 0, 0, 0.1)',
  padding: theme.spacing(4),
  margin: theme.spacing(2, 0),
}));

const HomePage = () => {
  const navigate = useNavigate();
  const { funds, currentFundId, switchFund, deleteFund } = useFund();
  const [confirmDeleteDialog, setConfirmDeleteDialog] = useState({ open: false, fundId: null });
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  const handleSelectFund = (fundId) => {
    switchFund(fundId);
    setSnackbar({
      open: true,
      message: 'تم تبديل الصندوق بنجاح',
      severity: 'success'
    });
  };

  const handleNavigateToDashboard = (fundId) => {
    if (fundId !== currentFundId) {
      switchFund(fundId);
    }
    navigate('/dashboard');
  };

  const handleDeleteFund = (e, fundId) => {
    e.stopPropagation();
    setConfirmDeleteDialog({ open: true, fundId });
  };

  const confirmDeleteFund = () => {
    const fundId = confirmDeleteDialog.fundId;
    if (!fundId || fundId === 'default') {
      setSnackbar({
        open: true,
        message: 'لا يمكن حذف الصندوق الافتراضي',
        severity: 'error'
      });
      setConfirmDeleteDialog({ open: false, fundId: null });
      return;
    }

    try {
      deleteFund(fundId);
      setSnackbar({
        open: true,
        message: 'تم حذف الصندوق بنجاح',
        severity: 'success'
      });
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'حدث خطأ أثناء حذف الصندوق',
        severity: 'error'
      });
    }

    setConfirmDeleteDialog({ open: false, fundId: null });
  };

  const handleCreateNewFund = () => {
    navigate('/funds');
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <GradientBackground>
      <Container maxWidth="xl" sx={{ py: 4, position: 'relative', zIndex: 1 }}>
        <Fade in timeout={800}>
          <FloatingCard>
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Typography
                variant="h3"
                component="h1"
                fontWeight="bold"
                sx={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 2
                }}
              >
                إدارة الصناديق
              </Typography>
              <Typography variant="h6" color="text.secondary" sx={{ mb: 3 }}>
                اختر الصندوق الذي تريد العمل عليه أو قم بإنشاء صندوق جديد
              </Typography>
              <Divider sx={{
                width: '100px',
                height: '4px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                mx: 'auto',
                borderRadius: '2px'
              }} />
            </Box>

            <Grid container spacing={4} sx={{ mb: 6 }}>
              {funds.map((fund, index) => (
                <Grid item xs={12} sm={6} lg={4} key={fund.id}>
                  <Zoom in timeout={600 + index * 200}>
                    <StyledCard
                      elevation={0}
                      isActive={fund.id === currentFundId}
                    >
                      <CardActionArea onClick={() => handleSelectFund(fund.id)}>
                        <CardContent sx={{
                          textAlign: 'center',
                          py: 4,
                          position: 'relative',
                          zIndex: 2
                        }}>
                          {fund.id !== 'default' && (
                            <IconButton
                              size="small"
                              sx={{
                                position: 'absolute',
                                top: 12,
                                left: 12,
                                backgroundColor: 'rgba(244, 67, 54, 0.1)',
                                color: '#f44336',
                                '&:hover': {
                                  backgroundColor: 'rgba(244, 67, 54, 0.2)',
                                }
                              }}
                              onClick={(e) => handleDeleteFund(e, fund.id)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          )}

                          {fund.id === currentFundId && (
                            <Chip
                              icon={<StarIcon />}
                              label="الصندوق الحالي"
                              size="small"
                              sx={{
                                position: 'absolute',
                                top: 12,
                                right: 12,
                                backgroundColor: '#4caf50',
                                color: 'white',
                                fontWeight: 'bold'
                              }}
                            />
                          )}

                          <Avatar sx={{
                            width: 80,
                            height: 80,
                            mx: 'auto',
                            mb: 3,
                            background: fund.id === currentFundId
                              ? 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)'
                              : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            boxShadow: fund.id === currentFundId
                              ? '0 8px 25px rgba(76, 175, 80, 0.4)'
                              : '0 8px 25px rgba(102, 126, 234, 0.4)',
                          }}>
                            <AccountBalanceIcon sx={{ fontSize: 40, color: 'white' }} />
                          </Avatar>

                          <Typography
                            variant="h5"
                            fontWeight="bold"
                            mb={1}
                            sx={{
                              color: fund.id === currentFundId ? 'white' : '#333'
                            }}
                          >
                            {fund.fundName}
                          </Typography>

                          {fund.fundFamily && (
                            <Chip
                              label={fund.fundFamily}
                              size="small"
                              sx={{
                                mb: 3,
                                backgroundColor: fund.id === currentFundId
                                  ? 'rgba(255,255,255,0.2)'
                                  : 'rgba(102, 126, 234, 0.1)',
                                color: fund.id === currentFundId ? 'white' : '#667eea'
                              }}
                            />
                          )}

                          <Box sx={{
                            display: 'flex',
                            gap: 1,
                            justifyContent: 'center',
                            flexWrap: 'wrap',
                            mt: 3
                          }}>
                            <Button
                              variant={fund.id === currentFundId ? "contained" : "outlined"}
                              color={fund.id === currentFundId ? "success" : "primary"}
                              size="small"
                              startIcon={fund.id === currentFundId ? <StarIcon /> : <VisibilityIcon />}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleSelectFund(fund.id);
                              }}
                              sx={{
                                borderRadius: '20px',
                                px: 2,
                                fontWeight: 'bold'
                              }}
                            >
                              {fund.id === currentFundId ? "مختار" : "اختيار"}
                            </Button>
                            <Button
                              variant="outlined"
                              size="small"
                              startIcon={<DashboardIcon />}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleNavigateToDashboard(fund.id);
                              }}
                              sx={{
                                borderRadius: '20px',
                                px: 2,
                                color: fund.id === currentFundId ? 'white' : '#667eea',
                                borderColor: fund.id === currentFundId ? 'rgba(255,255,255,0.5)' : '#667eea',
                                '&:hover': {
                                  borderColor: fund.id === currentFundId ? 'white' : '#667eea',
                                  backgroundColor: fund.id === currentFundId ? 'rgba(255,255,255,0.1)' : 'rgba(102, 126, 234, 0.1)'
                                }
                              }}
                            >
                              لوحة المعلومات
                            </Button>
                          </Box>
                        </CardContent>
                      </CardActionArea>
                    </StyledCard>
                  </Zoom>
                </Grid>
              ))}
            </Grid>

            <Fade in timeout={1200}>
              <Box sx={{ textAlign: 'center' }}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 4,
                    background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.05) 100%)',
                    borderRadius: '20px',
                    border: '2px dashed rgba(76, 175, 80, 0.3)'
                  }}
                >
                  <Avatar sx={{
                    width: 60,
                    height: 60,
                    mx: 'auto',
                    mb: 2,
                    background: 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)',
                    boxShadow: '0 8px 25px rgba(76, 175, 80, 0.3)'
                  }}>
                    <AddIcon sx={{ fontSize: 30, color: 'white' }} />
                  </Avatar>
                  <Typography variant="h5" fontWeight="bold" mb={2} color="#4caf50">
                    إنشاء صندوق جديد
                  </Typography>
                  <Typography variant="body1" color="text.secondary" mb={3}>
                    هل تريد إنشاء صندوق توفير جديد لمجموعة أخرى من الأعضاء؟
                  </Typography>
                  <Button
                    variant="contained"
                    size="large"
                    startIcon={<AddIcon />}
                    onClick={handleCreateNewFund}
                    sx={{
                      px: 4,
                      py: 1.5,
                      borderRadius: '25px',
                      background: 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)',
                      boxShadow: '0 8px 25px rgba(76, 175, 80, 0.3)',
                      fontWeight: 'bold',
                      fontSize: '1.1rem',
                      '&:hover': {
                        background: 'linear-gradient(135deg, #45a049 0%, #4caf50 100%)',
                        boxShadow: '0 12px 35px rgba(76, 175, 80, 0.4)',
                        transform: 'translateY(-2px)'
                      }
                    }}
                  >
                    إنشاء صندوق جديد
                  </Button>
                </Paper>
              </Box>
            </Fade>

            {/* Copyright */}
            <Copyright variant="footer" showDivider={false} />
          </FloatingCard>
        </Fade>
      </Container>

      {/* Confirmation dialog for deleting a fund */}
      <Dialog
        open={confirmDeleteDialog.open}
        onClose={() => setConfirmDeleteDialog({ open: false, fundId: null })}
        maxWidth="sm"
        fullWidth
        dir="rtl"
        PaperProps={{
          sx: {
            borderRadius: '20px',
            background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
          }
        }}
      >
        <DialogTitle sx={{ textAlign: 'center', pb: 1 }}>
          <Avatar sx={{
            width: 60,
            height: 60,
            mx: 'auto',
            mb: 2,
            background: 'linear-gradient(135deg, #f44336 0%, #d32f2f 100%)',
            boxShadow: '0 8px 25px rgba(244, 67, 54, 0.3)'
          }}>
            <DeleteIcon sx={{ fontSize: 30, color: 'white' }} />
          </Avatar>
          <Typography variant="h5" fontWeight="bold" color="#f44336">
            تأكيد حذف الصندوق
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ textAlign: 'center', px: 4 }}>
          <Typography variant="body1" sx={{ fontSize: '1.1rem', lineHeight: 1.6 }}>
            هل أنت متأكد من رغبتك في حذف هذا الصندوق؟
            <br />
            <strong>سيتم حذف جميع البيانات المرتبطة به بشكل نهائي.</strong>
          </Typography>
        </DialogContent>
        <DialogActions sx={{ px: 4, py: 3, justifyContent: 'center', gap: 2 }}>
          <Button
            variant="outlined"
            onClick={() => setConfirmDeleteDialog({ open: false, fundId: null })}
            sx={{
              borderRadius: '25px',
              px: 3,
              py: 1,
              borderColor: '#666',
              color: '#666',
              '&:hover': {
                borderColor: '#333',
                backgroundColor: 'rgba(0,0,0,0.05)'
              }
            }}
          >
            إلغاء
          </Button>
          <Button
            variant="contained"
            onClick={confirmDeleteFund}
            sx={{
              borderRadius: '25px',
              px: 3,
              py: 1,
              background: 'linear-gradient(135deg, #f44336 0%, #d32f2f 100%)',
              boxShadow: '0 4px 15px rgba(244, 67, 54, 0.3)',
              '&:hover': {
                background: 'linear-gradient(135deg, #d32f2f 0%, #f44336 100%)',
                boxShadow: '0 6px 20px rgba(244, 67, 54, 0.4)',
              }
            }}
          >
            حذف نهائي
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{
            width: '100%',
            borderRadius: '15px',
            boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
            '& .MuiAlert-icon': {
              fontSize: '1.5rem'
            }
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </GradientBackground>
  );
};

export default HomePage;