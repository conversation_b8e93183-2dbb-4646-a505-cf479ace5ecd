import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Alert,
  Snackbar,
  Badge
} from '@mui/material';
import {
  SwapHoriz as SwapIcon,
  Notes as NotesIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PriorityHigh as HighPriorityIcon,
  Warning as MediumPriorityIcon,
  Info as LowPriorityIcon,
  Schedule as ScheduleIcon,
  Notifications as NotificationIcon,
  Extension as ExtendIcon,
  VolumeUp as SoundIcon
} from '@mui/icons-material';
import moment from 'moment';
import 'moment/locale/ar';
import momentHijri from 'moment-hijri';
import Copyright from '../common/Copyright';



const DashboardPage = ({
  members = [],
  income = [],
  expenses = [],
  families = [],
  totalIncomeAmount = 0,
  totalExpensesAmount = 0,
  netBalance = 0,
  dateType = 'gregorian'
}) => {
  // State for date converter
  const [gregorianDate, setGregorianDate] = useState('');
  const [hijriDate, setHijriDate] = useState('');
  const [dateCalculationResult, setDateCalculationResult] = useState(null);
  const [isHijriInput, setIsHijriInput] = useState(false);

  // State for notes
  const [notes, setNotes] = useState(() => {
    try {
      const savedNotes = localStorage.getItem('dashboard_notes');
      return savedNotes ? JSON.parse(savedNotes) : [];
    } catch (error) {
      return [];
    }
  });
  const [noteDialog, setNoteDialog] = useState(false);
  const [currentNote, setCurrentNote] = useState({
    id: null,
    title: '',
    content: '',
    priority: 'medium',
    dueDate: '',
    reminderEnabled: false,
    reminderTime: '',
    category: 'general'
  });
  const [showNotification, setShowNotification] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState('');

  // Create notification sound using Web Audio API
  const playNotificationSound = () => {
    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
      oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
      oscillator.frequency.setValueAtTime(800, audioContext.currentTime + 0.2);

      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.3);
    } catch (error) {
      console.log('Audio not available:', error);
    }
  };

  // Priority colors and icons
  const priorityConfig = {
    high: {
      color: '#f44336',
      bgColor: 'rgba(244, 67, 54, 0.1)',
      icon: HighPriorityIcon,
      label: 'عالية'
    },
    medium: {
      color: '#ff9800',
      bgColor: 'rgba(255, 152, 0, 0.1)',
      icon: MediumPriorityIcon,
      label: 'متوسطة'
    },
    low: {
      color: '#4caf50',
      bgColor: 'rgba(76, 175, 80, 0.1)',
      icon: LowPriorityIcon,
      label: 'منخفضة'
    }
  };

  // Categories
  const categories = {
    general: 'عام',
    work: 'عمل',
    personal: 'شخصي',
    urgent: 'عاجل',
    meeting: 'اجتماع',
    reminder: 'تذكير'
  };

  // Check for due notes and send notifications
  useEffect(() => {
    const checkNotifications = () => {
      const now = new Date();
      const upcomingNotes = notes.filter(note => {
        if (!note.reminderEnabled || !note.dueDate || !note.reminderTime) return false;

        const dueDateTime = new Date(`${note.dueDate}T${note.reminderTime}`);
        const timeDiff = dueDateTime.getTime() - now.getTime();

        // Show notification if due time is within 1 minute
        return timeDiff > 0 && timeDiff <= 60000;
      });

      upcomingNotes.forEach(note => {
        const message = `تذكير: ${note.title}`;
        setNotificationMessage(message);
        setShowNotification(true);

        // Play notification sound
        playNotificationSound();
      });
    };

    const interval = setInterval(checkNotifications, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, [notes]);



  // Calculate remaining time for notes
  const calculateRemainingTime = (dueDate, dueTime) => {
    if (!dueDate) return null;

    const now = new Date();
    const due = dueTime ? new Date(`${dueDate}T${dueTime}`) : new Date(dueDate);
    const diff = due.getTime() - now.getTime();

    if (diff <= 0) return 'منتهية الصلاحية';

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) return `${days} يوم`;
    if (hours > 0) return `${hours} ساعة`;
    return `${minutes} دقيقة`;
  };



  // Helper function to convert Arabic numerals to English
  const convertArabicToEnglish = (str) => {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

    let result = str;
    for (let i = 0; i < arabicNumbers.length; i++) {
      result = result.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i]);
    }
    return result;
  };

  // Helper function to convert English numerals to Arabic
  const convertEnglishToArabic = (str) => {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

    let result = str;
    for (let i = 0; i < englishNumbers.length; i++) {
      result = result.replace(new RegExp(englishNumbers[i], 'g'), arabicNumbers[i]);
    }
    return result;
  };

  // Date conversion function
  const handleDateConversion = () => {
    try {
      let convertedDate = null;

      if (isHijriInput && hijriDate) {
        // Convert Arabic numerals to English for processing
        const processedHijriDate = convertArabicToEnglish(hijriDate);

        // Convert Hijri to Gregorian
        const formats = ['iYYYY/iMM/iDD', 'iYYYY-iMM-iDD', 'iDD/iMM/iYYYY'];
        let hijriMoment = null;

        for (const format of formats) {
          hijriMoment = momentHijri(processedHijriDate, format);
          if (hijriMoment.isValid()) break;
        }

        if (hijriMoment && hijriMoment.isValid()) {
          convertedDate = hijriMoment.format('YYYY-MM-DD');
          setGregorianDate(convertedDate);
        } else {
          alert('تنسيق التاريخ الهجري غير صحيح. استخدم التنسيق: ۱۴۴۵/۰۳/۱۵');
          return;
        }
      } else if (!isHijriInput && gregorianDate) {
        // Convert Gregorian to Hijri
        const gregorianMoment = moment(gregorianDate);
        if (gregorianMoment.isValid()) {
          const hijriConverted = momentHijri(gregorianMoment);
          const hijriFormatted = hijriConverted.format('iYYYY/iMM/iDD');
          setHijriDate(convertEnglishToArabic(hijriFormatted));
          convertedDate = gregorianDate;
        } else {
          alert('تاريخ ميلادي غير صحيح');
          return;
        }
      }

      // Calculate age or remaining time automatically after conversion
      if (convertedDate) {
        calculateDateDifference(convertedDate);
      }
    } catch (error) {
      console.error('Date conversion error:', error);
      alert('حدث خطأ في تحويل التاريخ');
    }
  };

  // Date difference calculation function (age for past dates, remaining time for future dates)
  const calculateDateDifference = (dateString) => {
    if (!dateString) return;

    try {
      const targetDate = moment(dateString);
      const now = moment();

      if (targetDate.isValid() && targetDate.isBefore(now)) {
        // Past date - calculate age
        const years = now.diff(targetDate, 'years');
        const months = now.diff(targetDate.clone().add(years, 'years'), 'months');
        const days = now.diff(targetDate.clone().add(years, 'years').add(months, 'months'), 'days');
        const totalDays = now.diff(targetDate, 'days');

        setDateCalculationResult({
          type: 'age',
          years,
          months,
          days,
          totalDays,
          title: '🎂 العمر المحسوب من هذا التاريخ:',
          description: `📅 إجمالي الأيام: ${totalDays.toLocaleString('ar-SA')} يوم`
        });
      } else if (targetDate.isAfter(now)) {
        // Future date - calculate remaining time
        const years = targetDate.diff(now, 'years');
        const months = targetDate.diff(now.clone().add(years, 'years'), 'months');
        const days = targetDate.diff(now.clone().add(years, 'years').add(months, 'months'), 'days');
        const totalDays = targetDate.diff(now, 'days');

        setDateCalculationResult({
          type: 'remaining',
          years,
          months,
          days,
          totalDays,
          title: '⏰ الوقت المتبقي حتى هذا التاريخ:',
          description: `📅 إجمالي الأيام المتبقية: ${totalDays.toLocaleString('ar-SA')} يوم`
        });
      } else {
        // Same date as today
        setDateCalculationResult({
          type: 'today',
          years: 0,
          months: 0,
          days: 0,
          totalDays: 0,
          title: '📅 هذا التاريخ هو اليوم!',
          description: 'التاريخ المدخل يطابق تاريخ اليوم'
        });
      }
    } catch (error) {
      console.error('Date calculation error:', error);
      setDateCalculationResult(null);
    }
  };

  // Notes management
  const saveNote = () => {
    if (!currentNote.title.trim() || !currentNote.content.trim()) return;

    try {
      const noteData = {
        id: currentNote.id || Date.now(),
        title: currentNote.title.trim(),
        content: currentNote.content.trim(),
        priority: currentNote.priority,
        dueDate: currentNote.dueDate,
        reminderEnabled: currentNote.reminderEnabled,
        reminderTime: currentNote.reminderTime,
        category: currentNote.category,
        date: currentNote.id ? currentNote.date : new Date().toISOString(),
        lastModified: new Date().toISOString()
      };

      let updatedNotes;
      if (currentNote.id) {
        updatedNotes = notes.map(note => note.id === currentNote.id ? noteData : note);
      } else {
        updatedNotes = [...notes, noteData];
      }

      setNotes(updatedNotes);
      localStorage.setItem('dashboard_notes', JSON.stringify(updatedNotes));
      setNoteDialog(false);
      setCurrentNote({
        id: null,
        title: '',
        content: '',
        priority: 'medium',
        dueDate: '',
        reminderEnabled: false,
        reminderTime: '',
        category: 'general'
      });
    } catch (error) {
      console.error('Error saving note:', error);
    }
  };

  const deleteNote = (noteId) => {
    try {
      const updatedNotes = notes.filter(note => note.id !== noteId);
      setNotes(updatedNotes);
      localStorage.setItem('dashboard_notes', JSON.stringify(updatedNotes));
    } catch (error) {
      console.error('Error deleting note:', error);
    }
  };

  const editNote = (note) => {
    setCurrentNote(note);
    setNoteDialog(true);
  };

  const extendNote = (noteId, days = 1) => {
    try {
      const updatedNotes = notes.map(note => {
        if (note.id === noteId && note.dueDate) {
          const currentDue = new Date(note.dueDate);
          currentDue.setDate(currentDue.getDate() + days);
          return {
            ...note,
            dueDate: currentDue.toISOString().split('T')[0],
            lastModified: new Date().toISOString()
          };
        }
        return note;
      });

      setNotes(updatedNotes);
      localStorage.setItem('dashboard_notes', JSON.stringify(updatedNotes));

      setNotificationMessage(`تم تمديد الملاحظة لـ ${days} يوم إضافي`);
      setShowNotification(true);
    } catch (error) {
      console.error('Error extending note:', error);
    }
  };

  return (
    <Box sx={{ direction: 'rtl', p: { xs: 2, sm: 3 } }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold" sx={{
          color: 'primary.main',
          fontSize: { xs: '1.75rem', sm: '2.125rem' }
        }}>
          قسم الخدمات
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          أدوات مفيدة ومحول التاريخ وحساب العمر والوقت المتبقي
        </Typography>
      </Box>

      {/* Date Converter with Age Calculator */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Combined Date Converter and Age Calculator */}
        <Grid item xs={12}>
          <Paper elevation={8} sx={{
            p: { xs: 2, sm: 3, md: 4 },
            borderRadius: { xs: '16px', sm: '20px', md: '24px' },
            background: 'linear-gradient(135deg, #2196F3 0%, #1976D2 100%)',
            color: 'white',
            border: { xs: '2px solid rgba(255,255,255,0.3)', md: '4px solid rgba(255,255,255,0.3)' },
            boxShadow: '0 12px 40px rgba(33, 150, 243, 0.4)',
            transform: { xs: 'none', md: 'scale(1.02)' },
            transition: 'all 0.3s ease',
            maxWidth: '100%',
            width: '100%',
            aspectRatio: { xs: 'auto', sm: 'auto' },
            minHeight: { xs: 'auto', sm: 'auto' },
            '&:hover': {
              transform: { xs: 'translateY(-2px)', md: 'scale(1.04)' },
              boxShadow: '0 16px 48px rgba(33, 150, 243, 0.5)',
            }
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <SwapIcon sx={{ mr: 1, fontSize: 28 }} />
              <Typography variant="h6" fontWeight="bold">
                محول التاريخ وحاسبة العمر والوقت
              </Typography>
            </Box>

            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={isHijriInput}
                      onChange={(e) => setIsHijriInput(e.target.checked)}
                      sx={{
                        '& .MuiSwitch-switchBase.Mui-checked': {
                          color: 'white',
                        },
                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                          backgroundColor: 'rgba(255,255,255,0.3)',
                        },
                      }}
                    />
                  }
                  label={isHijriInput ? "إدخال هجري" : "إدخال ميلادي"}
                  sx={{ color: 'white', fontWeight: 'bold' }}
                />
              </Grid>
            </Grid>

            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 'bold', opacity: 0.9 }}>
                    📅 تاريخ للتحويل وحساب العمر أو الوقت المتبقي
                  </Typography>
                  {isHijriInput ? (
                    <Box>
                      <Grid container spacing={1} sx={{ mb: 1 }}>
                        <Grid item xs={4}>
                          <TextField
                            label="اليوم"
                            value={hijriDate.split('/')[2] || ''}
                            onChange={(e) => {
                              const parts = hijriDate.split('/');
                              parts[2] = e.target.value;
                              setHijriDate(parts.join('/'));
                            }}
                            placeholder="۱۸"
                            fullWidth
                            inputProps={{
                              dir: 'rtl',
                              style: {
                                textAlign: 'center',
                                fontFamily: 'Arial, sans-serif',
                                fontSize: '1.4rem',
                                fontWeight: 'bold'
                              },
                              maxLength: 2
                            }}
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                backgroundColor: 'rgba(255,255,255,0.2)',
                                color: 'white',
                                fontSize: '1.4rem',
                                fontWeight: 'bold',
                                '& fieldset': { borderColor: 'rgba(255,255,255,0.5)', borderWidth: '2px' },
                                '&:hover fieldset': { borderColor: 'rgba(255,255,255,0.8)' },
                                '&.Mui-focused fieldset': { borderColor: 'white', borderWidth: '3px' },
                              },
                              '& .MuiInputLabel-root': { color: 'rgba(255,255,255,0.9)', fontWeight: 'bold' },
                              '& .MuiInputLabel-root.Mui-focused': { color: 'white' },
                            }}
                          />
                        </Grid>
                        <Grid item xs={4}>
                          <TextField
                            label="الشهر"
                            value={hijriDate.split('/')[1] || ''}
                            onChange={(e) => {
                              const parts = hijriDate.split('/');
                              parts[1] = e.target.value;
                              setHijriDate(parts.join('/'));
                            }}
                            placeholder="۱۲"
                            fullWidth
                            inputProps={{
                              dir: 'rtl',
                              style: {
                                textAlign: 'center',
                                fontFamily: 'Arial, sans-serif',
                                fontSize: '1.4rem',
                                fontWeight: 'bold'
                              },
                              maxLength: 2
                            }}
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                backgroundColor: 'rgba(255,255,255,0.2)',
                                color: 'white',
                                fontSize: '1.4rem',
                                fontWeight: 'bold',
                                '& fieldset': { borderColor: 'rgba(255,255,255,0.5)', borderWidth: '2px' },
                                '&:hover fieldset': { borderColor: 'rgba(255,255,255,0.8)' },
                                '&.Mui-focused fieldset': { borderColor: 'white', borderWidth: '3px' },
                              },
                              '& .MuiInputLabel-root': { color: 'rgba(255,255,255,0.9)', fontWeight: 'bold' },
                              '& .MuiInputLabel-root.Mui-focused': { color: 'white' },
                            }}
                          />
                        </Grid>
                        <Grid item xs={4}>
                          <TextField
                            label="السنة"
                            value={hijriDate.split('/')[0] || ''}
                            onChange={(e) => {
                              const parts = hijriDate.split('/');
                              parts[0] = e.target.value;
                              setHijriDate(parts.join('/'));
                            }}
                            placeholder="۱۴۰۷"
                            fullWidth
                            inputProps={{
                              dir: 'rtl',
                              style: {
                                textAlign: 'center',
                                fontFamily: 'Arial, sans-serif',
                                fontSize: '1.4rem',
                                fontWeight: 'bold'
                              },
                              maxLength: 4
                            }}
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                backgroundColor: 'rgba(255,255,255,0.2)',
                                color: 'white',
                                fontSize: '1.4rem',
                                fontWeight: 'bold',
                                '& fieldset': { borderColor: 'rgba(255,255,255,0.5)', borderWidth: '2px' },
                                '&:hover fieldset': { borderColor: 'rgba(255,255,255,0.8)' },
                                '&.Mui-focused fieldset': { borderColor: 'white', borderWidth: '3px' },
                              },
                              '& .MuiInputLabel-root': { color: 'rgba(255,255,255,0.9)', fontWeight: 'bold' },
                              '& .MuiInputLabel-root.Mui-focused': { color: 'white' },
                            }}
                          />
                        </Grid>
                      </Grid>
                      <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.8)', display: 'block', textAlign: 'center' }}>
                        مثال: اليوم ۱۸، الشهر ۱۲، السنة ۱۴۰۷
                      </Typography>
                    </Box>
                  ) : (
                    <TextField
                      type="date"
                      label="التاريخ الميلادي"
                      value={gregorianDate}
                      onChange={(e) => setGregorianDate(e.target.value)}
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      inputProps={{
                        dir: 'rtl',
                        style: { textAlign: 'right' }
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'rgba(255,255,255,0.15)',
                          color: 'white',
                          fontSize: '1.2rem',
                          direction: 'rtl',
                          '& fieldset': { borderColor: 'rgba(255,255,255,0.4)', borderWidth: '2px' },
                          '&:hover fieldset': { borderColor: 'rgba(255,255,255,0.7)' },
                          '&.Mui-focused fieldset': { borderColor: 'white', borderWidth: '2px' },
                        },
                        '& .MuiInputLabel-root': { color: 'rgba(255,255,255,0.9)', fontWeight: 'bold' },
                        '& .MuiInputLabel-root.Mui-focused': { color: 'white' },
                      }}
                    />
                  )}
                </Box>
              </Grid>
            </Grid>

            <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
              <Button
                variant="contained"
                onClick={handleDateConversion}
                startIcon={<SwapIcon />}
                size="large"
                sx={{
                  bgcolor: 'rgba(255,255,255,0.25)',
                  color: 'white',
                  fontWeight: 'bold',
                  fontSize: '1.2rem',
                  py: 2,
                  px: 4,
                  border: '2px solid rgba(255,255,255,0.3)',
                  borderRadius: '12px',
                  '&:hover': {
                    bgcolor: 'rgba(255,255,255,0.35)',
                    border: '2px solid rgba(255,255,255,0.5)',
                    transform: 'translateY(-2px)'
                  }
                }}
              >
                تحويل التاريخ وحساب العمر/الوقت
              </Button>
            </Box>

            {/* Results Display */}
            {((isHijriInput && gregorianDate) || (!isHijriInput && hijriDate)) && (
              <Box sx={{
                p: 4,
                bgcolor: 'rgba(255,255,255,0.2)',
                borderRadius: '16px',
                border: '2px solid rgba(255,255,255,0.3)',
                textAlign: 'center'
              }}>
                {/* Date Conversion Result */}
                <Typography variant="body1" sx={{ mb: 2, opacity: 0.9, fontWeight: 'bold' }}>
                  ✅ نتيجة التحويل:
                </Typography>
                <Typography variant="h4" fontWeight="bold" sx={{
                  mb: 2,
                  color: 'white',
                  textShadow: '1px 1px 2px rgba(0,0,0,0.3)',
                  direction: 'rtl'
                }}>
                  {isHijriInput ?
                    `الميلادي: ${moment(gregorianDate).format('DD/MM/YYYY')}` :
                    `الهجري: ${hijriDate.split('/').reverse().join('/')}`
                  }
                </Typography>
                <Typography variant="h6" sx={{ mb: 3, opacity: 0.8 }}>
                  {isHijriInput ?
                    `${moment(gregorianDate).format('dddd')} - ${moment(gregorianDate).format('MMMM YYYY')}` :
                    `${momentHijri(hijriDate, 'iYYYY/iMM/iDD').format('dddd')} - ${momentHijri(hijriDate, 'iYYYY/iMM/iDD').format('iMMMM iYYYY')}`
                  }
                </Typography>

                {/* Date Calculation Result (Age for past dates, Remaining time for future dates) */}
                {dateCalculationResult && (
                  <Box sx={{
                    mt: 3,
                    pt: 3,
                    borderTop: '2px solid rgba(255,255,255,0.3)'
                  }}>
                    <Typography variant="body1" sx={{ mb: 2, opacity: 0.9, fontWeight: 'bold' }}>
                      {dateCalculationResult.title}
                    </Typography>
                    {dateCalculationResult.type !== 'today' && (
                      <Typography variant="h4" fontWeight="bold" sx={{
                        mb: 2,
                        color: 'white',
                        textShadow: '1px 1px 2px rgba(0,0,0,0.3)',
                        direction: 'rtl'
                      }}>
                        {dateCalculationResult.years} سنة، {dateCalculationResult.months} شهر، {dateCalculationResult.days} يوم
                      </Typography>
                    )}
                    <Typography variant="h6" sx={{
                      opacity: 0.9,
                      fontWeight: 'medium',
                      color: dateCalculationResult.type === 'remaining' ? '#4caf50' :
                             dateCalculationResult.type === 'today' ? '#ff9800' : 'white'
                    }}>
                      {dateCalculationResult.description}
                    </Typography>
                  </Box>
                )}
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>



      {/* Notes Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <Paper elevation={3} sx={{
            p: { xs: 2, sm: 3 },
            borderRadius: { xs: '12px', sm: '16px' },
            background: 'linear-gradient(135deg, #9C27B0 0%, #673AB7 100%)',
            color: 'white',
            border: '2px solid rgba(255,255,255,0.2)',
            maxWidth: '100%',
            width: '100%'
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <NotesIcon sx={{ mr: 1, fontSize: 28 }} />
                <Typography variant="h6" fontWeight="bold">
                  الملاحظات المهنية
                </Typography>
              </Box>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => {
                  setCurrentNote({
                    id: null,
                    title: '',
                    content: '',
                    priority: 'medium',
                    dueDate: '',
                    reminderEnabled: false,
                    reminderTime: '',
                    category: 'general'
                  });
                  setNoteDialog(true);
                }}
                sx={{
                  bgcolor: 'rgba(255,255,255,0.2)',
                  color: 'white',
                  '&:hover': { bgcolor: 'rgba(255,255,255,0.3)' }
                }}
              >
                إضافة ملاحظة
              </Button>
            </Box>

            {notes.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <NotesIcon sx={{ fontSize: 48, opacity: 0.5, mb: 2 }} />
                <Typography variant="h6" sx={{ opacity: 0.7 }}>
                  لا توجد ملاحظات حتى الآن
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.5 }}>
                  اضغط على "إضافة ملاحظة" لإنشاء ملاحظة جديدة
                </Typography>
              </Box>
            ) : (
              <Grid container spacing={2}>
                {notes
                  .sort((a, b) => {
                    // Sort by priority first, then by due date
                    const priorityOrder = { high: 3, medium: 2, low: 1 };
                    if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
                      return priorityOrder[b.priority] - priorityOrder[a.priority];
                    }
                    if (a.dueDate && b.dueDate) {
                      return new Date(a.dueDate) - new Date(b.dueDate);
                    }
                    return new Date(b.date) - new Date(a.date);
                  })
                  .map((note) => {
                    const priorityInfo = priorityConfig[note.priority || 'medium'];
                    const PriorityIcon = priorityInfo.icon;
                    const remainingTime = calculateRemainingTime(note.dueDate, note.reminderTime);
                    const isOverdue = remainingTime === 'منتهية الصلاحية';

                    return (
                      <Grid item xs={12} sm={6} lg={4} key={note.id}>
                        <Paper elevation={3} sx={{
                          p: { xs: 1.5, sm: 2 },
                          borderRadius: { xs: '8px', sm: '12px' },
                          background: `linear-gradient(135deg, ${priorityInfo.bgColor} 0%, rgba(255,255,255,0.05) 100%)`,
                          border: `2px solid ${priorityInfo.color}`,
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          position: 'relative',
                          transition: 'all 0.3s ease',
                          maxWidth: '100%',
                          minHeight: { xs: '200px', sm: '250px' },
                          '&:hover': {
                            transform: { xs: 'translateY(-2px)', sm: 'translateY(-4px)' },
                            boxShadow: `0 8px 25px ${priorityInfo.color}40`
                          }
                        }}>
                          {/* Priority Badge */}
                          <Box sx={{
                            position: 'absolute',
                            top: -8,
                            right: -8,
                            zIndex: 1
                          }}>
                            <Badge
                              badgeContent={
                                <PriorityIcon sx={{
                                  fontSize: 16,
                                  color: 'white'
                                }} />
                              }
                              sx={{
                                '& .MuiBadge-badge': {
                                  backgroundColor: priorityInfo.color,
                                  width: 28,
                                  height: 28,
                                  borderRadius: '50%'
                                }
                              }}
                            />
                          </Box>

                          {/* Header */}
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                            <Box sx={{ flex: 1 }}>
                              <Typography variant="subtitle1" fontWeight="bold" sx={{
                                color: 'white',
                                mb: 0.5
                              }}>
                                {note.title}
                              </Typography>
                              <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                                <Chip
                                  label={priorityInfo.label}
                                  size="small"
                                  sx={{
                                    backgroundColor: priorityInfo.color,
                                    color: 'white',
                                    fontSize: '0.7rem'
                                  }}
                                />
                                <Chip
                                  label={categories[note.category] || 'عام'}
                                  size="small"
                                  sx={{
                                    backgroundColor: 'rgba(255,255,255,0.2)',
                                    color: 'white',
                                    fontSize: '0.7rem'
                                  }}
                                />
                              </Box>
                            </Box>
                          </Box>

                          {/* Content */}
                          <Typography variant="body2" sx={{
                            flex: 1,
                            mb: 2,
                            color: 'rgba(255,255,255,0.9)',
                            lineHeight: 1.5
                          }}>
                            {note.content}
                          </Typography>

                          {/* Due Date and Remaining Time */}
                          {note.dueDate && (
                            <Box sx={{
                              mb: 2,
                              p: 1,
                              bgcolor: isOverdue ? 'rgba(244, 67, 54, 0.2)' : 'rgba(255,255,255,0.1)',
                              borderRadius: '8px',
                              border: isOverdue ? '1px solid #f44336' : '1px solid rgba(255,255,255,0.2)'
                            }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                                <ScheduleIcon sx={{ fontSize: 16, mr: 0.5, color: isOverdue ? '#f44336' : 'white' }} />
                                <Typography variant="caption" sx={{
                                  color: isOverdue ? '#f44336' : 'white',
                                  fontWeight: 'bold'
                                }}>
                                  تاريخ الاستحقاق: {moment(note.dueDate).format('DD/MM/YYYY')}
                                </Typography>
                              </Box>
                              {note.reminderTime && (
                                <Typography variant="caption" sx={{
                                  color: isOverdue ? '#f44336' : 'rgba(255,255,255,0.8)',
                                  display: 'block'
                                }}>
                                  وقت التذكير: {note.reminderTime}
                                </Typography>
                              )}
                              <Typography variant="caption" sx={{
                                color: isOverdue ? '#f44336' : '#4caf50',
                                fontWeight: 'bold',
                                display: 'block'
                              }}>
                                {remainingTime && `المتبقي: ${remainingTime}`}
                              </Typography>
                            </Box>
                          )}

                          {/* Action Buttons */}
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Box sx={{ display: 'flex', gap: 0.5 }}>
                              <IconButton
                                size="small"
                                onClick={() => editNote(note)}
                                sx={{
                                  color: 'white',
                                  bgcolor: 'rgba(255,255,255,0.1)',
                                  '&:hover': { bgcolor: 'rgba(255,255,255,0.2)' }
                                }}
                              >
                                <EditIcon fontSize="small" />
                              </IconButton>
                              {note.dueDate && (
                                <IconButton
                                  size="small"
                                  onClick={() => extendNote(note.id, 1)}
                                  sx={{
                                    color: 'white',
                                    bgcolor: 'rgba(76, 175, 80, 0.3)',
                                    '&:hover': { bgcolor: 'rgba(76, 175, 80, 0.5)' }
                                  }}
                                >
                                  <ExtendIcon fontSize="small" />
                                </IconButton>
                              )}
                              <IconButton
                                size="small"
                                onClick={() => deleteNote(note.id)}
                                sx={{
                                  color: 'white',
                                  bgcolor: 'rgba(244, 67, 54, 0.3)',
                                  '&:hover': { bgcolor: 'rgba(244, 67, 54, 0.5)' }
                                }}
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Box>

                            <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.7)' }}>
                              {moment(note.date).locale('ar').format('DD/MM/YYYY')}
                            </Typography>
                          </Box>
                        </Paper>
                      </Grid>
                    );
                  })}
              </Grid>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Note Dialog */}
      <Dialog
        open={noteDialog}
        onClose={() => setNoteDialog(false)}
        maxWidth="md"
        fullWidth
        sx={{ direction: 'rtl' }}
      >
        <DialogTitle sx={{
          bgcolor: 'primary.main',
          color: 'white',
          display: 'flex',
          alignItems: 'center'
        }}>
          <NotesIcon sx={{ mr: 1 }} />
          {currentNote.id ? 'تعديل الملاحظة' : 'إضافة ملاحظة جديدة'}
        </DialogTitle>
        <DialogContent sx={{ mt: 2 }}>
          <Grid container spacing={2}>
            {/* Title */}
            <Grid item xs={12}>
              <TextField
                autoFocus
                label="عنوان الملاحظة"
                fullWidth
                variant="outlined"
                value={currentNote.title}
                onChange={(e) => setCurrentNote({ ...currentNote, title: e.target.value })}
                required
              />
            </Grid>

            {/* Content */}
            <Grid item xs={12}>
              <TextField
                label="محتوى الملاحظة"
                fullWidth
                multiline
                rows={4}
                variant="outlined"
                value={currentNote.content}
                onChange={(e) => setCurrentNote({ ...currentNote, content: e.target.value })}
                required
              />
            </Grid>

            {/* Priority and Category */}
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>مستوى الأهمية</InputLabel>
                <Select
                  value={currentNote.priority}
                  label="مستوى الأهمية"
                  onChange={(e) => setCurrentNote({ ...currentNote, priority: e.target.value })}
                >
                  <MenuItem value="high">
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <HighPriorityIcon sx={{ mr: 1, color: '#f44336' }} />
                      عالية
                    </Box>
                  </MenuItem>
                  <MenuItem value="medium">
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <MediumPriorityIcon sx={{ mr: 1, color: '#ff9800' }} />
                      متوسطة
                    </Box>
                  </MenuItem>
                  <MenuItem value="low">
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <LowPriorityIcon sx={{ mr: 1, color: '#4caf50' }} />
                      منخفضة
                    </Box>
                  </MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>التصنيف</InputLabel>
                <Select
                  value={currentNote.category}
                  label="التصنيف"
                  onChange={(e) => setCurrentNote({ ...currentNote, category: e.target.value })}
                >
                  {Object.entries(categories).map(([key, value]) => (
                    <MenuItem key={key} value={key}>{value}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Due Date */}
            <Grid item xs={6}>
              <TextField
                label="تاريخ الاستحقاق"
                type="date"
                fullWidth
                variant="outlined"
                value={currentNote.dueDate}
                onChange={(e) => setCurrentNote({ ...currentNote, dueDate: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            {/* Reminder Time */}
            <Grid item xs={6}>
              <TextField
                label="وقت التذكير"
                type="time"
                fullWidth
                variant="outlined"
                value={currentNote.reminderTime}
                onChange={(e) => setCurrentNote({ ...currentNote, reminderTime: e.target.value })}
                InputLabelProps={{ shrink: true }}
                disabled={!currentNote.dueDate}
              />
            </Grid>

            {/* Reminder Toggle */}
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={currentNote.reminderEnabled}
                    onChange={(e) => setCurrentNote({ ...currentNote, reminderEnabled: e.target.checked })}
                    disabled={!currentNote.dueDate || !currentNote.reminderTime}
                  />
                }
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <NotificationIcon sx={{ mr: 1 }} />
                    تفعيل التنبيهات الصوتية
                  </Box>
                }
              />
            </Grid>

            {/* Preview */}
            {currentNote.dueDate && (
              <Grid item xs={12}>
                <Paper sx={{ p: 2, bgcolor: 'grey.100' }}>
                  <Typography variant="subtitle2" gutterBottom>
                    معاينة:
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                    <Chip
                      label={priorityConfig[currentNote.priority].label}
                      size="small"
                      sx={{ backgroundColor: priorityConfig[currentNote.priority].color, color: 'white' }}
                    />
                    <Chip
                      label={categories[currentNote.category]}
                      size="small"
                      variant="outlined"
                    />
                  </Box>
                  <Typography variant="body2">
                    تاريخ الاستحقاق: {moment(currentNote.dueDate).format('DD/MM/YYYY')}
                    {currentNote.reminderTime && ` في ${currentNote.reminderTime}`}
                  </Typography>
                  {currentNote.reminderEnabled && (
                    <Typography variant="caption" color="success.main">
                      ✓ سيتم إرسال تنبيه صوتي
                    </Typography>
                  )}
                </Paper>
              </Grid>
            )}
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={() => setNoteDialog(false)} color="inherit">
            إلغاء
          </Button>
          <Button
            onClick={saveNote}
            variant="contained"
            disabled={!currentNote.title.trim() || !currentNote.content.trim()}
            startIcon={currentNote.id ? <EditIcon /> : <AddIcon />}
          >
            {currentNote.id ? 'تحديث' : 'حفظ'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Notification Snackbar */}
      <Snackbar
        open={showNotification}
        autoHideDuration={6000}
        onClose={() => setShowNotification(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setShowNotification(false)}
          severity="info"
          sx={{
            width: '100%',
            direction: 'rtl',
            '& .MuiAlert-icon': {
              fontSize: '1.5rem'
            }
          }}
          icon={<SoundIcon />}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <NotificationIcon sx={{ mr: 1 }} />
            {notificationMessage}
          </Box>
        </Alert>
      </Snackbar>

      <Copyright />
    </Box>
  );
};

export default DashboardPage;
