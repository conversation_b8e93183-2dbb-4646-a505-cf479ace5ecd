import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useFund } from '../../contexts/FundContext';
import {
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
  Box,
  Divider,
  IconButton,
  Tooltip,
  Avatar,
  Chip
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Home as HomeIcon,
  People as PeopleIcon,
  TrendingUp as IncomeIcon,
  TrendingDown as ExpenseIcon,
  Assessment as ReportIcon,
  AddBox as AddBoxIcon,
  FamilyRestroom as FamilyIcon,
  ChevronRight as ChevronRightIcon,
  ChevronLeft as ChevronLeftIcon,
  Settings as SettingsIcon,
  Dashboard as DashboardIcon,
  AccountBalance as AccountBalanceIcon,
  Savings as SavingsIcon
} from '@mui/icons-material';

const drawerWidth = 240;
const drawerCollapsedWidth = 60;
const mobileDrawerWidth = 200; // أصغر للجوال
const mobileCollapsedWidth = 45; // أصغر عند الطي

// Styled components
const StyledDrawer = styled(Drawer)(({ theme, open }) => ({
  width: open ? drawerWidth : drawerCollapsedWidth,
  flexShrink: 0,
  whiteSpace: 'nowrap',
  '& .MuiDrawer-paper': {
    width: open ? drawerWidth : drawerCollapsedWidth,
    boxSizing: 'border-box',
    background: 'linear-gradient(135deg, #1a237e 0%, #283593 25%, #3949ab 50%, #5c6bc0 75%, #7986cb 100%)',
    color: 'white',
    overflowX: 'hidden',
    transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
    boxShadow: '0 10px 30px rgba(0,0,0,0.3)',
    borderLeft: 'none',
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
      pointerEvents: 'none',
    },
    // Mobile optimizations - make it compact
    [theme.breakpoints.down('md')]: {
      position: 'fixed',
      zIndex: 1300,
      width: open ? mobileDrawerWidth : mobileCollapsedWidth,
      transform: 'translateX(0)', // Always visible
    },
    [theme.breakpoints.down('sm')]: {
      width: open ? '180px' : '40px', // أصغر بكثير للجوال
      transform: 'translateX(0)', // Always visible
    },
  },
}));

const StyledListItem = styled(ListItem)(({ theme, active, open }) => ({
  padding: open ? theme.spacing(1.2, 1.5) : theme.spacing(0.8, 0.5),
  margin: open ? theme.spacing(0.5, 1) : theme.spacing(0.3, 0.5),
  borderRadius: '12px',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  backgroundColor: active ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
  backdropFilter: active ? 'blur(10px)' : 'none',
  border: active ? '1px solid rgba(255, 255, 255, 0.3)' : '1px solid transparent',
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    transform: open ? 'translateX(-5px) scale(1.02)' : 'scale(1.05)',
    boxShadow: '0 8px 25px rgba(0, 0, 0, 0.2)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
  },
  display: 'flex',
  justifyContent: open ? 'flex-start' : 'center',
  alignItems: 'center',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    right: 0,
    width: active ? '4px' : '0px',
    height: '100%',
    background: 'linear-gradient(45deg, #fff, #f0f0f0)',
    transition: 'width 0.3s ease',
  },
  [theme.breakpoints.down('sm')]: {
    padding: open ? theme.spacing(1, 1.25) : theme.spacing(0.6, 0.3),
    margin: open ? theme.spacing(0.25, 0.75) : theme.spacing(0.2, 0.3),
  },
}));

const LogoContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  padding: theme.spacing(2),
  position: 'relative',
  textAlign: 'center',
  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
  [theme.breakpoints.down('md')]: {
    padding: theme.spacing(1.5),
  },
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(1),
  },
}));

const CollapseButton = styled(IconButton)(({ theme }) => ({
  position: 'absolute',
  bottom: '10px',
  left: '50%',
  transform: 'translateX(-50%)',
  color: 'white',
  backgroundColor: 'rgba(255, 255, 255, 0.2)',
  border: '2px solid rgba(255, 255, 255, 0.3)',
  width: '36px',
  height: '36px',
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    transform: 'translateX(-50%) scale(1.1)',
  },
  transition: 'all 0.3s ease',
  zIndex: 1200,
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
  [theme.breakpoints.down('md')]: {
    width: '28px',
    height: '28px',
    bottom: '6px',
  },
  [theme.breakpoints.down('sm')]: {
    width: '24px',
    height: '24px',
    bottom: '4px',
    '& .MuiSvgIcon-root': {
      fontSize: '1rem'
    }
  },
}));

const menuItems = [
  {
    text: 'الرئيسية',
    icon: <HomeIcon />,
    path: '/',
    color: '#4CAF50'
  },
  {
    text: 'إنشاء صندوق',
    icon: <AddBoxIcon />,
    path: '/funds',
    color: '#2196F3'
  },
  {
    text: 'قسم الخدمات',
    icon: <DashboardIcon />,
    path: '/dashboard',
    color: '#FF9800'
  },
  {
    text: 'العائلات',
    icon: <FamilyIcon />,
    path: '/families',
    color: '#9C27B0'
  },
  {
    text: 'الأعضاء',
    icon: <PeopleIcon />,
    path: '/members',
    color: '#3F51B5'
  },
  {
    text: 'الإيرادات',
    icon: <IncomeIcon />,
    path: '/income',
    color: '#4CAF50'
  },
  {
    text: 'المصروفات',
    icon: <ExpenseIcon />,
    path: '/expenses',
    color: '#F44336'
  },
  {
    text: 'التقارير',
    icon: <ReportIcon />,
    path: '/reports',
    color: '#607D8B'
  },
  {
    text: 'الإعدادات',
    icon: <SettingsIcon />,
    path: '/settings',
    color: '#795548'
  }
];

const Sidebar = ({ currentFundId, fundSettings }) => {
  const [open, setOpen] = useState(true);
  const location = useLocation();
  const navigate = useNavigate();
  const { funds } = useFund();

  // Find current fund in funds list
  const currentFund = funds.find(fund => fund.id === currentFundId);

  // Use safe fund settings with defaults
  const safeFundSettings = fundSettings || {
    fundName: 'صندوق التوفير',
    fundFamily: '',
    generalManager: '',
    treasurer: '',
    collector: ''
  };

  const handleDrawerToggle = () => {
    setOpen(!open);
    
    // Save open state to localStorage
    try {
      localStorage.setItem('sidebar_open', JSON.stringify(open));
    } catch (error) {
      console.error('Error saving sidebar state:', error);
    }
  };

  // Responsive drawer width - أصغر للجوال
  const responsiveDrawerWidth = {
    xs: open ? '180px' : '40px', // أصغر بكثير
    sm: open ? '200px' : '50px', // متوسط للتابلت
    md: open ? drawerWidth : drawerCollapsedWidth
  };
  
  // Responsive icon size
  const iconSize = {
    xs: '1.2rem',
    sm: '1.5rem'
  };

  return (
    <StyledDrawer variant="permanent" anchor="right" open={open}>
      <LogoContainer>
        <Avatar sx={{
          background: 'linear-gradient(135deg, #1a237e 0%, #3949ab 100%)',
          width: open ? { xs: 35, sm: 50, md: 60 } : { xs: 25, sm: 35, md: 40 },
          height: open ? { xs: 35, sm: 50, md: 60 } : { xs: 25, sm: 35, md: 40 },
          boxShadow: '0 8px 25px rgba(0,0,0,0.3)',
          border: '2px solid rgba(255,255,255,0.2)',
          mb: open ? { xs: 1, sm: 1.5, md: 2 } : 0.5,
          transition: 'all 0.3s ease'
        }}>
          <AccountBalanceIcon sx={{
            fontSize: open ? { xs: 18, sm: 25, md: 30 } : { xs: 14, sm: 18, md: 20 },
            color: 'white',
            transition: 'all 0.3s ease'
          }} />
        </Avatar>
        {open && (
          <Box sx={{ textAlign: 'center', mb: 1 }}>
            <Typography
              variant="h6"
              fontWeight="bold"
              sx={{
                color: 'white',
                fontSize: { xs: '0.8rem', sm: '1rem', md: '1.2rem' },
                textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                maxWidth: { xs: '140px', sm: '160px', md: '180px' },
                mb: { xs: 0.3, sm: 0.4, md: 0.5 }
              }}
            >
              {safeFundSettings.fundName}
            </Typography>
            {safeFundSettings.fundFamily && (
              <Chip
                label={safeFundSettings.fundFamily}
                size="small"
                sx={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  color: 'white',
                  fontSize: '0.75rem',
                  height: '24px',
                  '& .MuiChip-label': {
                    px: 1
                  }
                }}
              />
            )}
          </Box>
        )}
      </LogoContainer>

      <Divider sx={{ backgroundColor: 'rgba(255, 255, 255, 0.2)', mx: 1 }} />

      <List sx={{ mt: 1, px: 0 }}>
        {menuItems.map((item) => (
          <Tooltip
            title={!open ? item.text : ""}
            placement="left"
            key={item.text}
          >
            <StyledListItem
              component={Link}
              to={item.path}
              active={location.pathname === item.path ? 1 : 0}
              open={open ? 1 : 0}
              sx={{ cursor: 'pointer' }}
            >
              <ListItemIcon sx={{
                minWidth: open ? { xs: 35, sm: 45, md: 50 } : { xs: 25, sm: 35, md: 40 },
                mr: open ? { xs: 1, sm: 1.5, md: 2 } : 'auto',
                ml: open ? 0 : 'auto',
                justifyContent: 'center'
              }}>
                <Box sx={{
                  fontSize: { xs: '0.9rem', sm: '1.1rem', md: '1.3rem' },
                  background: location.pathname === item.path
                    ? `linear-gradient(135deg, ${item.color}, ${item.color}dd)`
                    : open
                      ? 'rgba(255,255,255,0.15)'
                      : 'rgba(255,255,255,0.25)',
                  borderRadius: open ? { xs: '8px', sm: '10px', md: '12px' } : { xs: '6px', sm: '8px' },
                  padding: open ? { xs: '6px', sm: '8px', md: '10px' } : { xs: '4px', sm: '6px', md: '8px' },
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: open ? { xs: '30px', sm: '35px', md: '40px' } : { xs: '22px', sm: '28px', md: '32px' },
                  height: open ? { xs: '30px', sm: '35px', md: '40px' } : { xs: '22px', sm: '28px', md: '32px' },
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  color: location.pathname === item.path
                    ? 'white'
                    : open
                      ? 'white'
                      : 'white',
                  boxShadow: location.pathname === item.path
                    ? `0 4px 15px ${item.color}40`
                    : open
                      ? '0 3px 10px rgba(0,0,0,0.15)'
                      : '0 3px 10px rgba(0,0,0,0.25)',
                  '&:hover': {
                    background: `linear-gradient(135deg, ${item.color}, ${item.color}dd)`,
                    color: 'white',
                    transform: 'scale(1.1)',
                    boxShadow: `0 6px 20px ${item.color}60`
                  }
                }}>
                  {item.icon}
                </Box>
              </ListItemIcon>
              {open && (
                <ListItemText
                  primary={item.text}
                  primaryTypographyProps={{
                    fontWeight: location.pathname === item.path ? 'bold' : '600',
                    color: 'white',
                    fontSize: { xs: '0.85rem', sm: '1rem', md: '1.1rem' },
                    textAlign: 'right',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    maxWidth: { xs: '120px', sm: '140px', md: '160px' },
                    filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.3))'
                  }}
                />
              )}
            </StyledListItem>
          </Tooltip>
        ))}
      </List>
      
      <Box sx={{ flexGrow: 1 }} />
      
      <CollapseButton onClick={handleDrawerToggle}>
        {open ? <ChevronRightIcon /> : <ChevronLeftIcon />}
      </CollapseButton>
      
      {open && (
        <Box sx={{ p: { xs: 1, sm: 2 }, textAlign: 'center', borderTop: '1px solid rgba(255, 255, 255, 0.1)' }}>
          <Typography variant="caption" sx={{ color: 'white', opacity: 0.7, fontSize: { xs: '0.65rem', sm: '0.75rem' } }}>
            {new Date().getFullYear()} © {safeFundSettings.fundName}
          </Typography>
        </Box>
      )}
    </StyledDrawer>
  );
};

export default Sidebar;
