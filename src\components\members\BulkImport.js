import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  Tabs,
  Tab,
  Alert,
  List,
  ListItem,
  ListItemText,
  Chip,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper
} from '@mui/material';
import {
  Upload as UploadIcon,
  ContentPaste as PasteIcon,
  People as PeopleIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Info as InfoIcon
} from '@mui/icons-material';

const BulkImport = ({ open, onClose, onImport, families = [] }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [textData, setTextData] = useState('');
  const [importMode, setImportMode] = useState('names'); // 'names' or 'full'
  const [defaultFamily, setDefaultFamily] = useState('');
  const [preview, setPreview] = useState([]);
  const [errors, setErrors] = useState([]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    setTextData('');
    setPreview([]);
    setErrors([]);
  };

  // معالجة النص المدخل
  const processTextData = (text) => {
    if (!text.trim()) {
      setPreview([]);
      setErrors([]);
      return;
    }

    const lines = text.split('\n').filter(line => line.trim());
    const processed = [];
    const newErrors = [];

    lines.forEach((line, index) => {
      const trimmedLine = line.trim();
      if (!trimmedLine) return;

      if (importMode === 'names') {
        // استيراد الأسماء فقط
        if (trimmedLine.length < 2) {
          newErrors.push(`السطر ${index + 1}: الاسم قصير جداً`);
          return;
        }
        
        processed.push({
          name: trimmedLine,
          phone: '',
          email: '',
          address: '',
          familyId: defaultFamily
        });
      } else {
        // استيراد البيانات الكاملة (مفصولة بفاصلة أو تاب)
        const parts = trimmedLine.split(/[,\t]/).map(part => part.trim());
        
        if (parts.length < 1) {
          newErrors.push(`السطر ${index + 1}: بيانات غير كافية`);
          return;
        }

        const member = {
          name: parts[0] || '',
          phone: parts[1] || '',
          email: parts[2] || '',
          address: parts[3] || '',
          familyId: parts[4] || defaultFamily
        };

        if (!member.name) {
          newErrors.push(`السطر ${index + 1}: الاسم مطلوب`);
          return;
        }

        processed.push(member);
      }
    });

    setPreview(processed);
    setErrors(newErrors);
  };

  // معالجة ملف CSV/TXT
  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target.result;
      setTextData(content);
      processTextData(content);
    };
    reader.readAsText(file, 'UTF-8');
  };

  // معالجة النص المدخل يدوياً
  const handleTextChange = (event) => {
    const text = event.target.value;
    setTextData(text);
    processTextData(text);
  };

  // لصق من الحافظة
  const handlePasteFromClipboard = async () => {
    try {
      const text = await navigator.clipboard.readText();
      setTextData(text);
      processTextData(text);
    } catch (err) {
      console.error('فشل في قراءة الحافظة:', err);
    }
  };

  // تأكيد الاستيراد
  const handleImport = () => {
    if (preview.length === 0) return;
    
    onImport(preview);
    handleClose();
  };

  // إغلاق النافذة
  const handleClose = () => {
    setTextData('');
    setPreview([]);
    setErrors([]);
    setActiveTab(0);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <PeopleIcon />
        استيراد أعضاء متعددين
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Tabs value={activeTab} onChange={handleTabChange}>
            <Tab label="رفع ملف" icon={<UploadIcon />} />
            <Tab label="نسخ ولصق" icon={<PasteIcon />} />
          </Tabs>
        </Box>

        {/* إعدادات الاستيراد */}
        <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>
          <Typography variant="subtitle2" gutterBottom>إعدادات الاستيراد</Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth size="small">
                <InputLabel>نوع الاستيراد</InputLabel>
                <Select
                  value={importMode}
                  onChange={(e) => setImportMode(e.target.value)}
                  label="نوع الاستيراد"
                >
                  <MenuItem value="names">الأسماء فقط</MenuItem>
                  <MenuItem value="full">البيانات الكاملة</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth size="small">
                <InputLabel>العائلة الافتراضية</InputLabel>
                <Select
                  value={defaultFamily}
                  onChange={(e) => setDefaultFamily(e.target.value)}
                  label="العائلة الافتراضية"
                >
                  <MenuItem value="">بدون عائلة</MenuItem>
                  {families.map((family) => (
                    <MenuItem key={family.id} value={family.id}>
                      {family.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Paper>

        {/* تبويب رفع الملف */}
        {activeTab === 0 && (
          <Box>
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                {importMode === 'names' 
                  ? 'ارفع ملف نصي يحتوي على اسم واحد في كل سطر'
                  : 'ارفع ملف CSV أو نصي بالتنسيق: الاسم، الهاتف، البريد، العنوان، العائلة'
                }
              </Typography>
            </Alert>
            
            <input
              accept=".txt,.csv"
              style={{ display: 'none' }}
              id="bulk-import-file"
              type="file"
              onChange={handleFileUpload}
            />
            <label htmlFor="bulk-import-file">
              <Button
                variant="outlined"
                component="span"
                startIcon={<UploadIcon />}
                fullWidth
                sx={{ py: 2 }}
              >
                اختيار ملف (.txt أو .csv)
              </Button>
            </label>
          </Box>
        )}

        {/* تبويب النسخ واللصق */}
        {activeTab === 1 && (
          <Box>
            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
              <Button
                variant="outlined"
                startIcon={<PasteIcon />}
                onClick={handlePasteFromClipboard}
                size="small"
              >
                لصق من الحافظة
              </Button>
            </Box>
            
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                {importMode === 'names' 
                  ? 'اكتب أو الصق اسم واحد في كل سطر'
                  : 'اكتب أو الصق البيانات بالتنسيق: الاسم، الهاتف، البريد، العنوان، العائلة (مفصولة بفاصلة أو تاب)'
                }
              </Typography>
            </Alert>
            
            <TextField
              fullWidth
              multiline
              rows={8}
              placeholder={importMode === 'names' 
                ? 'أحمد محمد\nفاطمة علي\nخالد سعد\n...'
                : 'أحمد محمد، 0501234567، <EMAIL>، الرياض\nفاطمة علي، 0509876543، <EMAIL>، جدة\n...'
              }
              value={textData}
              onChange={handleTextChange}
              variant="outlined"
            />
          </Box>
        )}

        {/* معاينة البيانات */}
        {preview.length > 0 && (
          <Box sx={{ mt: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <CheckIcon color="success" />
              معاينة البيانات ({preview.length} عضو)
            </Typography>
            
            <Paper sx={{ maxHeight: 200, overflow: 'auto', p: 1 }}>
              <List dense>
                {preview.slice(0, 10).map((member, index) => (
                  <ListItem key={index}>
                    <ListItemText
                      primary={member.name}
                      secondary={
                        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mt: 0.5 }}>
                          {member.phone && <Chip label={member.phone} size="small" />}
                          {member.email && <Chip label={member.email} size="small" />}
                          {member.familyId && (
                            <Chip 
                              label={families.find(f => f.id === member.familyId)?.name || 'عائلة غير معروفة'} 
                              size="small" 
                              color="primary" 
                            />
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
                {preview.length > 10 && (
                  <ListItem>
                    <ListItemText
                      primary={`... و ${preview.length - 10} عضو آخر`}
                      sx={{ textAlign: 'center', fontStyle: 'italic' }}
                    />
                  </ListItem>
                )}
              </List>
            </Paper>
          </Box>
        )}

        {/* الأخطاء */}
        {errors.length > 0 && (
          <Box sx={{ mt: 2 }}>
            <Alert severity="warning">
              <Typography variant="subtitle2" gutterBottom>
                تم العثور على {errors.length} خطأ:
              </Typography>
              <List dense>
                {errors.slice(0, 5).map((error, index) => (
                  <ListItem key={index} sx={{ py: 0 }}>
                    <ListItemText primary={error} />
                  </ListItem>
                ))}
                {errors.length > 5 && (
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    ... و {errors.length - 5} خطأ آخر
                  </Typography>
                )}
              </List>
            </Alert>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose}>إلغاء</Button>
        <Button
          onClick={handleImport}
          variant="contained"
          disabled={preview.length === 0 || errors.length > 0}
          startIcon={<PeopleIcon />}
        >
          استيراد {preview.length} عضو
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BulkImport;
