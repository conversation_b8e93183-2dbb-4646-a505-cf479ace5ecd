import React, { useState, useEffect } from 'react';
import { 
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  Box,
  Typography,
  Button,
  Divider,
  Grid,
  Paper,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemAvatar,
  Avatar,
  IconButton,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText
} from '@mui/material';
import {
  FamilyRestroom as FamilyIcon,
  Person as PersonIcon,
  PersonAdd as PersonAddIcon,
  Phone as PhoneIcon,
  Home as HomeIcon,
  Notes as NotesIcon,
  TrendingUp as IncomeIcon,
  TrendingDown as ExpenseIcon,
  CalendarToday as DateIcon,
  AccountBalance as BalanceIcon,
  Close as CloseIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Add as AddIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import moment from 'moment';
import momentHijri from 'moment-hijri';
import { useNavigate } from 'react-router-dom';

// TabPanel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`family-tabpanel-${index}`}
      aria-labelledby={`family-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const FamilyDetails = ({ family, open, onClose, dateType, updateFamily, deleteFamily, addMember, updateMember, deleteMember }) => {
  const navigate = useNavigate();
  const [value, setValue] = useState(0);
  const [members, setMembers] = useState([]);
  const [transactions, setTransactions] = useState([]);
  const [expenses, setExpenses] = useState([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editedFamily, setEditedFamily] = useState({});
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [errors, setErrors] = useState({});
  const [addMemberDialogOpen, setAddMemberDialogOpen] = useState(false);
  const [editMemberDialogOpen, setEditMemberDialogOpen] = useState(false);
  const [confirmDeleteMemberOpen, setConfirmDeleteMemberOpen] = useState(false);
  const [selectedMember, setSelectedMember] = useState(null);
  const [newMember, setNewMember] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    familyId: ''
  });
  const [editedMember, setEditedMember] = useState({
    id: '',
    name: '',
    phone: '',
    email: '',
    address: '',
    familyId: ''
  });

  // Format date based on type (Gregorian or Hijri)
  const formatDate = (dateString) => {
    if (dateType === 'gregorian') {
      return moment(dateString).locale('ar').format('YYYY/MM/DD');
    } else {
      return momentHijri(dateString).locale('ar').format('iYYYY/iMM/iDD');
    }
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  // تحديث إحصائيات العائلة في localStorage وتحديث الواجهة
  const updateFamilyStatsInLocalStorage = (familyId, familyMembers) => {
    try {
      // الحصول على معرف الصندوق الحالي
      const currentFundId = localStorage.getItem('current_fund_id') || 'default';
      
      // استخدام مفاتيح تخزين مرتبطة بالصندوق الحالي
      const familiesKey = `savings_fund_families_${currentFundId}`;
      
      // الحصول على قائمة العائلات من localStorage باستخدام المفتاح المرتبط بالصندوق الحالي
      const storedFamilies = JSON.parse(localStorage.getItem(familiesKey) || '[]');
      
      // حساب عدد الأعضاء والرصيد الإجمالي
      const membersCount = familyMembers.length;
      const totalBalance = familyMembers.reduce((sum, member) => sum + (parseFloat(member.totalBalance) || 0), 0);
      
      // تحديث إحصائيات العائلة
      const updatedFamilies = storedFamilies.map(f => {
        if (f.id === familyId) {
          return {
            ...f,
            membersCount,
            totalBalance
          };
        }
        return f;
      });
      
      // حفظ البيانات المحدثة في localStorage باستخدام المفتاح المرتبط بالصندوق الحالي
      localStorage.setItem(familiesKey, JSON.stringify(updatedFamilies));
      
      // تحديث الحالة المحلية للعائلة الحالية لضمان تحديث الواجهة
      if (family && family.id === familyId) {
        // استخدام تحديث مباشر للكائن لضمان تحديث الواجهة
        family.membersCount = membersCount;
        family.totalBalance = totalBalance;
      }
    } catch (error) {
      console.error("Error updating family stats:", error);
    }
  };

  // Load family members, income transactions, and expenses
  useEffect(() => {
    if (!family || !open) return;

    // These would normally fetch from App.js via props, but for now we'll use localStorage
    try {
      // استخدام المفاتيح المرتبطة بالصندوق الحالي
      const currentFundId = localStorage.getItem('current_fund_id') || 'default';

      // تعريف مفاتيح التخزين المرتبطة بالصندوق الحالي
      const membersKey = `savings_fund_members_${currentFundId}`;
      const incomeKey = `savings_fund_income_${currentFundId}`;
      const expensesKey = `savings_fund_expenses_${currentFundId}`;

      // الحصول على البيانات من localStorage
      const storedMembers = JSON.parse(localStorage.getItem(membersKey) || '[]');
      const storedIncome = JSON.parse(localStorage.getItem(incomeKey) || '[]');
      const storedExpenses = JSON.parse(localStorage.getItem(expensesKey) || '[]');

      // Filter members by family ID - تضمين جميع الأعضاء بما فيهم رب الأسرة
      const familyMembers = storedMembers.filter(member => member.familyId === family.id);
      setMembers(familyMembers);

      // Filter income transactions for family members
      const memberIds = familyMembers.map(member => member.id);
      const familyIncome = storedIncome.filter(item => memberIds.includes(item.memberId));
      setTransactions(familyIncome);

      // Filter expenses for family members
      const familyExpenses = storedExpenses.filter(item => memberIds.includes(item.memberId));
      setExpenses(familyExpenses);

      // تحديث إحصائيات العائلة في localStorage
      updateFamilyStatsInLocalStorage(family.id, familyMembers);

      // تحديث الإحصائيات في كائن العائلة مباشرة لتحديث الواجهة
      if (family) {
        family.membersCount = familyMembers.length;
        family.totalBalance = familyMembers.reduce((sum, member) => sum + (parseFloat(member.totalBalance) || 0), 0);
      }

    } catch (error) {
      console.error("Error loading family data:", error);
    }
  }, [family, open]); // أضفنا open للتأكد من تحديث البيانات عند فتح النافذة

  // Refresh data when family details change
  useEffect(() => {
    if (!family || !open) return;

    try {
      // استخدام المفتاح المرتبط بالصندوق الحالي
      const currentFundId = localStorage.getItem('current_fund_id') || 'default';

      // تعريف مفتاح التخزين المرتبط بالصندوق الحالي
      const membersKey = `savings_fund_members_${currentFundId}`;

      // الحصول على البيانات من localStorage
      const storedMembers = JSON.parse(localStorage.getItem(membersKey) || '[]');

      // فلترة الأعضاء حسب العائلة
      const familyMembers = storedMembers.filter(member => member.familyId === family.id);
      setMembers(familyMembers);
    } catch (error) {
      console.error("Error refreshing family members:", error);
    }
  }, [family, open]);

  // Initialize edited family data when family changes
  useEffect(() => {
    if (!family) return;

    setEditedFamily({
      name: family.name || '',
      headName: family.headName || '',
      phone: family.phone || '',
      address: family.address || '',
      notes: family.notes || ''
    });
  }, [family]);

  // Return early if family is null or undefined
  if (!family) {
    return null;
  }

  // Handle tab change
  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  // Toggle edit mode
  const handleEditToggle = () => {
    setIsEditing(!isEditing);
    
    // Reset to original values if cancelling edit
    if (isEditing) {
      setEditedFamily({
        name: family.name,
        headName: family.headName || '',
        phone: family.phone || '',
        address: family.address || '',
        notes: family.notes || ''
      });
      setErrors({});
    }
  };

  // Handle view member details
  const handleViewMember = (memberId) => {
    try {
      console.log('=== Family Member View Debug ===');
      console.log('Member ID from family:', memberId);

      // التأكد من وجود العضو
      const memberExists = members.find(m => m.id === memberId);
      console.log('Member exists in family:', !!memberExists);

      if (memberExists) {
        console.log('Navigating to member details from family...');
        navigate(`/member/${memberId}`);

        setTimeout(() => {
          console.log('Current URL after family navigate:', window.location.pathname);
        }, 100);
      } else {
        console.error('Member not found in members list from family');
        alert('العضو غير موجود في القائمة');
      }
    } catch (error) {
      console.error('Error navigating to member details from family:', error);
      alert('حدث خطأ أثناء فتح تفاصيل العضو من العائلة');
    }
  };

  // Handle edit member
  const handleEditMember = (member) => {
    setEditedMember({
      id: member.id,
      name: member.name,
      phone: member.phone || '',
      email: member.email || '',
      address: member.address || '',
      familyId: member.familyId,
      totalBalance: member.totalBalance || 0
    });
    setSelectedMember(member);
    setEditMemberDialogOpen(true);
  };

  // Handle delete member confirmation
  const handleDeleteMemberConfirm = (member) => {
    setSelectedMember(member);
    setConfirmDeleteMemberOpen(true);
  };

  // Handle delete member
  const handleDeleteMember = () => {
    if (selectedMember && deleteMember) {
      // حذف العضو
      deleteMember(selectedMember.id);
      
      // تحديث قائمة الأعضاء المحلية مباشرة بإزالة العضو المحذوف
      const updatedMembers = members.filter(member => member.id !== selectedMember.id);
      setMembers(updatedMembers);
      
      // تحديث إحصائيات العائلة بعد حذف العضو
      updateFamilyStatsInLocalStorage(family.id, updatedMembers);
      
      // تحديث الإحصائيات في كائن العائلة مباشرة لتحديث الواجهة
      family.membersCount = updatedMembers.length;
      family.totalBalance = updatedMembers.reduce((sum, member) => sum + (parseFloat(member.totalBalance) || 0), 0);
      
      // إغلاق نافذة التأكيد وإعادة تعيين العضو المحدد
      setConfirmDeleteMemberOpen(false);
      setSelectedMember(null);
    }
  };

  // Handle input change in edit mode
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setEditedFamily({
      ...editedFamily,
      [name]: value
    });
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};
    if (!editedFamily.name) {
      newErrors.name = 'الرجاء إدخال اسم العائلة';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Save edited family
  const handleSaveFamily = () => {
    if (validateForm()) {
      updateFamily(family.id, editedFamily);
      setIsEditing(false);
    }
  };

  // Open delete confirmation dialog
  const handleDeleteConfirmOpen = () => {
    setConfirmDeleteOpen(true);
  };

  // Close delete confirmation dialog
  const handleDeleteConfirmClose = () => {
    setConfirmDeleteOpen(false);
  };

  // Delete the family
  const handleDeleteFamily = () => {
    if (deleteFamily && family) {
      // حذف العائلة
      deleteFamily(family.id);
      
      // إغلاق نوافذ التأكيد والتفاصيل
      handleDeleteConfirmClose();
      onClose();
      
      // تحديث واجهة المستخدم مباشرة
      // سيتم تنفيذ ذلك من خلال useEffect في FamiliesManagement
    }
  };

  return (
    <>
      <Dialog 
        open={open} 
        onClose={onClose} 
        maxWidth="md" 
        fullWidth
        scroll="paper"
        dir="rtl"
      >
        <DialogTitle sx={{ 
          bgcolor: 'primary.main', 
          color: 'white', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          borderBottom: '1px solid',
          borderColor: 'divider' 
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton 
              aria-label="close" 
              onClick={onClose} 
              sx={{ color: 'white' }}
            >
              <CloseIcon />
            </IconButton>
            
            {updateFamily && (
              <IconButton 
                onClick={isEditing ? handleSaveFamily : handleEditToggle} 
                sx={{ color: 'white', ml: 1 }}
              >
                {isEditing ? <SaveIcon /> : <EditIcon />}
              </IconButton>
            )}
            
            {deleteFamily && (
              <IconButton 
                onClick={handleDeleteConfirmOpen} 
                sx={{ color: 'white', ml: 1 }}
              >
                <DeleteIcon />
              </IconButton>
            )}
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <FamilyIcon sx={{ mr: 1 }} />
            <Typography variant="h6" fontWeight="bold">
              تفاصيل العائلة: {family.name}
            </Typography>
          </Box>
        </DialogTitle>
        
        <Divider />
        
        <DialogContent sx={{ pb: 1 }}>
          {/* Family Information Card */}
          <Paper elevation={2} sx={{ p: 2, mb: 3, borderRadius: '12px' }}>
            <Grid container spacing={2}>
              {isEditing ? (
                // Editing Mode
                <>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      name="name"
                      label="اسم العائلة"
                      variant="outlined"
                      value={editedFamily.name}
                      onChange={handleInputChange}
                      error={!!errors.name}
                      helperText={errors.name}
                      size="small"
                      margin="dense"
                      inputProps={{ style: { textAlign: 'right' } }}
                      InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      name="headName"
                      label="رب الأسرة"
                      variant="outlined"
                      value={editedFamily.headName}
                      onChange={handleInputChange}
                      size="small"
                      margin="dense"
                      inputProps={{ style: { textAlign: 'right' } }}
                      InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      name="phone"
                      label="رقم الهاتف"
                      variant="outlined"
                      value={editedFamily.phone}
                      onChange={handleInputChange}
                      size="small"
                      margin="dense"
                      inputProps={{ style: { textAlign: 'right' } }}
                      InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      name="address"
                      label="العنوان"
                      variant="outlined"
                      value={editedFamily.address}
                      onChange={handleInputChange}
                      size="small"
                      margin="dense"
                      inputProps={{ style: { textAlign: 'right' } }}
                      InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      name="notes"
                      label="ملاحظات"
                      variant="outlined"
                      multiline
                      rows={2}
                      value={editedFamily.notes}
                      onChange={handleInputChange}
                      size="small"
                      margin="dense"
                      inputProps={{ style: { textAlign: 'right' } }}
                      InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
                    />
                  </Grid>
                  <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-start', mt: 1 }}>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={handleSaveFamily}
                      startIcon={<SaveIcon />}
                      size="small"
                    >
                      حفظ التغييرات
                    </Button>
                    <Button
                      variant="outlined"
                      color="inherit"
                      onClick={handleEditToggle}
                      size="small"
                      sx={{ mr: 1 }}
                    >
                      إلغاء
                    </Button>
                  </Grid>
                </>
              ) : (
                // View Mode
                <>
                  <Grid item xs={12} md={4}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <PersonIcon sx={{ color: 'primary.main', mr: 1 }} />
                      <Typography variant="body2" color="text.secondary">
                        رب الأسرة:
                      </Typography>
                      <Typography variant="body1" sx={{ mr: 1 }} fontWeight="medium">
                        {family.headName || 'غير محدد'}
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} md={4}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <PhoneIcon sx={{ color: 'primary.main', mr: 1 }} />
                      <Typography variant="body2" color="text.secondary">
                        رقم الهاتف:
                      </Typography>
                      <Typography variant="body1" sx={{ mr: 1 }}>
                        {family.phone || 'غير محدد'}
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} md={4}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <DateIcon sx={{ color: 'primary.main', mr: 1 }} />
                      <Typography variant="body2" color="text.secondary">
                        تاريخ الإضافة:
                      </Typography>
                      <Typography variant="body1" sx={{ mr: 1 }}>
                        {formatDate(family.createdAt)}
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} md={8}>
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                      <HomeIcon sx={{ color: 'primary.main', mr: 1, mt: 0.3 }} />
                      <Typography variant="body2" color="text.secondary">
                        العنوان:
                      </Typography>
                      <Typography variant="body1" sx={{ mr: 1 }}>
                        {family.address || 'غير محدد'}
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} md={4}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <BalanceIcon sx={{ color: 'primary.main', mr: 1 }} />
                      <Typography variant="body2" color="text.secondary">
                        الرصيد:
                      </Typography>
                      <Chip
                        label={formatCurrency(family.totalBalance)}
                        color={family.totalBalance >= 0 ? 'success' : 'error'}
                        size="small"
                        sx={{ mr: 1 }}
                      />
                    </Box>
                  </Grid>
                  
                  {family.notes && (
                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                        <NotesIcon sx={{ color: 'primary.main', mr: 1, mt: 0.3 }} />
                        <Typography variant="body2" color="text.secondary">
                          ملاحظات:
                        </Typography>
                        <Typography variant="body1" sx={{ mr: 1 }}>
                          {family.notes}
                        </Typography>
                      </Box>
                    </Grid>
                  )}
                </>
              )}
            </Grid>
          </Paper>
          
          {/* Tabs */}
          <Box sx={{ width: '100%' }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs 
                value={value} 
                onChange={handleChange} 
                aria-label="family tabs" 
                variant="fullWidth"
              >
                <Tab label="أفراد العائلة" />
                <Tab label="الإيرادات" />
                <Tab label="المصروفات" />
              </Tabs>
            </Box>
            
            {/* Tab 1: Family Members */}
            <TabPanel value={value} index={0}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<PersonAddIcon />}
                  onClick={() => {
                    setNewMember({
                      name: '',
                      phone: '',
                      email: '',
                      address: '',
                      familyId: family.id
                    });
                    setAddMemberDialogOpen(true);
                  }}
                  sx={{ borderRadius: '8px' }}
                >
                  إضافة عضو
                </Button>
              </Box>
              {members.length > 0 ? (
                <TableContainer component={Paper} sx={{ borderRadius: '12px' }}>
                  <Table>
                    <TableHead sx={{ bgcolor: '#f5f5f5' }}>
                      <TableRow>
                        <TableCell align="center" sx={{ fontWeight: 'bold', width: '50px' }}>#</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>الاسم</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>رقم الهاتف</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>تاريخ الإضافة</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>الرصيد</TableCell>
                        <TableCell align="center" sx={{ fontWeight: 'bold' }}>الإجراءات</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {members.map((member, index) => (
                        <TableRow key={member.id}>
                          <TableCell align="center" sx={{
                            fontWeight: 'bold',
                            color: 'primary.main',
                            width: '50px'
                          }}>
                            {index + 1}
                          </TableCell>
                          <TableCell align="right">{member.name}</TableCell>
                          <TableCell align="right">{member.phone || 'غير متوفر'}</TableCell>
                          <TableCell align="right">{formatDate(member.createdAt)}</TableCell>
                          <TableCell align="right">
                            <Typography
                              variant="body2"
                              fontWeight="bold"
                              color={
                                member.totalBalance > 0 ? 'success.main' :
                                member.totalBalance < 0 ? 'error.main' :
                                'text.primary'
                              }
                              sx={{
                                fontSize: '1rem',
                                textShadow: '0 1px 2px rgba(0,0,0,0.1)'
                              }}
                            >
                              {formatCurrency(member.totalBalance)}
                              {member.totalBalance < 0 && (
                                <Typography variant="caption" color="error.main" sx={{ display: 'block' }}>
                                  (مديونية)
                                </Typography>
                              )}
                            </Typography>
                          </TableCell>
                          <TableCell align="center">
                            <IconButton
                              size="small"
                              color="info"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                console.log('Family member view button clicked:', member.id);
                                handleViewMember(member.id);
                              }}
                              sx={{ mx: 0.5 }}
                              title="عرض تفاصيل العضو"
                            >
                              <ViewIcon fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => handleEditMember(member)}
                              sx={{ mx: 0.5 }}
                              title="تعديل العضو"
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleDeleteMemberConfirm(member)}
                              sx={{ mx: 0.5 }}
                              title="حذف العضو"
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Box sx={{ py: 4, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    لا يوجد أعضاء في هذه العائلة
                  </Typography>
                </Box>
              )}
            </TabPanel>
            
            {/* Tab 2: Income Transactions */}
            <TabPanel value={value} index={1}>
              {transactions.length > 0 ? (
                <TableContainer component={Paper} sx={{ borderRadius: '12px' }}>
                  <Table>
                    <TableHead sx={{ bgcolor: '#f5f5f5' }}>
                      <TableRow>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>العضو</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>المبلغ</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>التاريخ</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>البيان</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {transactions.map((transaction) => {
                        const member = members.find(m => m.id === transaction.memberId);
                        return (
                          <TableRow key={transaction.id}>
                            <TableCell align="right">{member ? member.name : 'غير معروف'}</TableCell>
                            <TableCell align="right">
                              <Typography 
                                variant="body2" 
                                fontWeight="medium" 
                                color={parseFloat(transaction.amount) >= 0 ? 'success.main' : 'error.main'}
                              >
                                {formatCurrency(transaction.amount)}
                              </Typography>
                            </TableCell>
                            <TableCell align="right">{formatDate(transaction.createdAt)}</TableCell>
                            <TableCell align="right">{transaction.description || 'بدون بيان'}</TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Box sx={{ py: 4, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    لا توجد إيرادات لهذه العائلة
                  </Typography>
                </Box>
              )}
            </TabPanel>
            
            {/* Tab 3: Expenses */}
            <TabPanel value={value} index={2}>
              {expenses.length > 0 ? (
                <TableContainer component={Paper} sx={{ borderRadius: '12px' }}>
                  <Table>
                    <TableHead sx={{ bgcolor: '#f5f5f5' }}>
                      <TableRow>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>العضو</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>المبلغ</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>التاريخ</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>السبب</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {expenses.map((expense) => {
                        const member = members.find(m => m.id === expense.memberId);
                        return (
                          <TableRow key={expense.id}>
                            <TableCell align="right">{member ? member.name : 'غير معروف'}</TableCell>
                            <TableCell align="right">
                              <Typography 
                                variant="body2" 
                                fontWeight="medium" 
                                color={parseFloat(expense.amount) >= 0 ? 'error.main' : 'success.main'}
                              >
                                {formatCurrency(expense.amount)}
                              </Typography>
                            </TableCell>
                            <TableCell align="right">{formatDate(expense.createdAt)}</TableCell>
                            <TableCell align="right">{expense.reason || 'بدون سبب'}</TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Box sx={{ py: 4, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    لا توجد مصروفات لهذه العائلة
                  </Typography>
                </Box>
              )}
            </TabPanel>
          </Box>
        </DialogContent>
        
        <DialogActions sx={{ p: 2, justifyContent: 'center' }}>
          <Button 
            onClick={onClose} 
            variant="outlined" 
            color="primary" 
            sx={{ minWidth: 120 }}
          >
            إغلاق
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog
        open={confirmDeleteOpen}
        onClose={handleDeleteConfirmClose}
        dir="rtl"
      >
        <DialogTitle sx={{ bgcolor: 'error.main', color: 'white' }}>
          تأكيد حذف العائلة
        </DialogTitle>
        <DialogContent sx={{ mt: 2 }}>
          <DialogContentText>
            هل أنت متأكد من رغبتك في حذف عائلة "{family.name}"؟ في حال وجود أعضاء في هذه العائلة، سيتم إلغاء ارتباطهم بها.
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ p: 2, justifyContent: 'flex-start' }}>
          <Button onClick={handleDeleteConfirmClose} color="inherit">
            إلغاء
          </Button>
          <Button 
            onClick={handleDeleteFamily} 
            variant="contained" 
            color="error"
            startIcon={<DeleteIcon />}
          >
            حذف
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add Member Dialog */}
      <Dialog 
        open={addMemberDialogOpen} 
        onClose={() => setAddMemberDialogOpen(false)} 
        fullWidth 
        maxWidth="md" 
        dir="rtl"
      >
        <DialogTitle sx={{ 
          bgcolor: 'primary.main', 
          color: 'white', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          borderBottom: '1px solid',
          borderColor: 'divider' 
        }}>
          <IconButton 
            aria-label="close" 
            onClick={() => setAddMemberDialogOpen(false)} 
            sx={{ color: 'white' }}
          >
            <CloseIcon />
          </IconButton>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <PersonAddIcon sx={{ mr: 1 }} />
            <Typography variant="h6" fontWeight="bold">
              إضافة عضو جديد للعائلة
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ mt: 2, pt: 2 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                autoFocus
                name="name"
                label="اسم العضو"
                variant="outlined"
                value={newMember.name}
                onChange={(e) => setNewMember({ ...newMember, name: e.target.value })}
                error={!!errors.name}
                helperText={errors.name}
                inputProps={{ style: { textAlign: 'right' } }}
                InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                name="phone"
                label="رقم الهاتف"
                variant="outlined"
                value={newMember.phone}
                onChange={(e) => setNewMember({ ...newMember, phone: e.target.value })}
                error={!!errors.phone}
                helperText={errors.phone}
                inputProps={{ style: { textAlign: 'right' } }}
                InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                name="email"
                label="البريد الإلكتروني"
                variant="outlined"
                value={newMember.email}
                onChange={(e) => setNewMember({ ...newMember, email: e.target.value })}
                error={!!errors.email}
                helperText={errors.email}
                inputProps={{ style: { textAlign: 'right' } }}
                InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                name="address"
                label="العنوان"
                variant="outlined"
                multiline
                rows={3}
                value={newMember.address}
                onChange={(e) => setNewMember({ ...newMember, address: e.target.value })}
                inputProps={{ style: { textAlign: 'right' } }}
                InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 2, justifyContent: 'flex-start' }}>
          <Button 
            onClick={() => setAddMemberDialogOpen(false)} 
            color="inherit" 
            sx={{ ml: 2, mr: 0 }}
          >
            إلغاء
          </Button>
          <Button 
            onClick={() => {
              // Validate form
              const errors = {};
              if (!newMember.name.trim()) {
                errors.name = 'اسم العضو مطلوب';
              }
              
              if (newMember.phone && !/^\d{10}$/.test(newMember.phone.trim())) {
                errors.phone = 'رقم الهاتف يجب أن يكون 10 أرقام';
              }
              
              if (newMember.email && !/\S+@\S+\.\S+/.test(newMember.email.trim())) {
                errors.email = 'صيغة البريد الإلكتروني غير صحيحة';
              }
              
              setErrors(errors);
              
              if (Object.keys(errors).length === 0) {
                // إضافة العضو الجديد
                const newMemberWithId = {
                  id: Date.now().toString(),
                  ...newMember,
                  totalBalance: 0,
                  createdAt: new Date().toISOString()
                };
                
                // استدعاء دالة إضافة العضو من الأب
                addMember(newMember);
                
                // تحديث قائمة الأعضاء المحلية مباشرة
                setMembers(prevMembers => [...prevMembers, newMemberWithId]);
                
                // تحديث إحصائيات العائلة بعد إضافة العضو الجديد
                const updatedMembers = [...members, newMemberWithId];
                updateFamilyStatsInLocalStorage(family.id, updatedMembers);
                
                // تحديث الإحصائيات في كائن العائلة مباشرة لتحديث الواجهة
                if (family) {
                  family.membersCount = updatedMembers.length;
                  family.totalBalance = updatedMembers.reduce((sum, member) => sum + (parseFloat(member.totalBalance) || 0), 0);
                }
                
                // إغلاق نافذة الإضافة
                setAddMemberDialogOpen(false);
                
                // إعادة تعيين نموذج العضو الجديد
                setNewMember({
                  name: '',
                  phone: '',
                  email: '',
                  address: '',
                  familyId: family.id
                });
              }
            }} 
            variant="contained" 
            color="primary"
          >
            إضافة
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Member Dialog */}
      <Dialog 
        open={editMemberDialogOpen} 
        onClose={() => setEditMemberDialogOpen(false)} 
        fullWidth 
        maxWidth="md" 
        dir="rtl"
      >
        <DialogTitle sx={{ 
          bgcolor: 'primary.main', 
          color: 'white', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          borderBottom: '1px solid',
          borderColor: 'divider' 
        }}>
          <IconButton 
            aria-label="close" 
            onClick={() => setEditMemberDialogOpen(false)} 
            sx={{ color: 'white' }}
          >
            <CloseIcon />
          </IconButton>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <EditIcon sx={{ mr: 1 }} />
            <Typography variant="h6" fontWeight="bold">
              تعديل بيانات العضو
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ mt: 2, pt: 2 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                autoFocus
                name="name"
                label="اسم العضو"
                variant="outlined"
                value={editedMember.name}
                onChange={(e) => setEditedMember({ ...editedMember, name: e.target.value })}
                error={!!errors.editName}
                helperText={errors.editName}
                inputProps={{ style: { textAlign: 'right' } }}
                InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                name="phone"
                label="رقم الهاتف"
                variant="outlined"
                value={editedMember.phone}
                onChange={(e) => setEditedMember({ ...editedMember, phone: e.target.value })}
                error={!!errors.editPhone}
                helperText={errors.editPhone}
                inputProps={{ style: { textAlign: 'right' } }}
                InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                name="email"
                label="البريد الإلكتروني"
                variant="outlined"
                value={editedMember.email}
                onChange={(e) => setEditedMember({ ...editedMember, email: e.target.value })}
                error={!!errors.editEmail}
                helperText={errors.editEmail}
                inputProps={{ style: { textAlign: 'right' } }}
                InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                name="address"
                label="العنوان"
                variant="outlined"
                multiline
                rows={3}
                value={editedMember.address}
                onChange={(e) => setEditedMember({ ...editedMember, address: e.target.value })}
                inputProps={{ style: { textAlign: 'right' } }}
                InputLabelProps={{ style: { right: 15, left: 'auto', transformOrigin: 'right' } }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 2, justifyContent: 'flex-start' }}>
          <Button 
            onClick={() => setEditMemberDialogOpen(false)} 
            color="inherit" 
            sx={{ ml: 2, mr: 0 }}
          >
            إلغاء
          </Button>
          <Button 
            onClick={() => {
              // Validate form
              const errors = {};
              if (!editedMember.name.trim()) {
                errors.editName = 'اسم العضو مطلوب';
              }
              
              if (editedMember.phone && !/^\d{10}$/.test(editedMember.phone.trim())) {
                errors.editPhone = 'رقم الهاتف يجب أن يكون 10 أرقام';
              }
              
              if (editedMember.email && !/\S+@\S+\.\S+/.test(editedMember.email.trim())) {
                errors.editEmail = 'صيغة البريد الإلكتروني غير صحيحة';
              }
              
              setErrors(errors);
              
              if (Object.keys(errors).length === 0 && updateMember) {
                // تحديث بيانات العضو
                updateMember(editedMember.id, editedMember);
                
                // تحديث قائمة الأعضاء المحلية مباشرة
                const updatedMembers = members.map(member => 
                  member.id === editedMember.id ? { ...member, ...editedMember } : member
                );
                setMembers(updatedMembers);
                
                // تحديث إحصائيات العائلة بعد تعديل العضو
                updateFamilyStatsInLocalStorage(family.id, updatedMembers);
                
                // تحديث الإحصائيات في كائن العائلة مباشرة لتحديث الواجهة
                family.membersCount = updatedMembers.length;
                family.totalBalance = updatedMembers.reduce((sum, member) => sum + (parseFloat(member.totalBalance) || 0), 0);
                
                // إغلاق نافذة التعديل
                setEditMemberDialogOpen(false);
              }
            }} 
            variant="contained" 
            color="primary"
          >
            حفظ التغييرات
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Member Confirmation Dialog */}
      <Dialog
        open={confirmDeleteMemberOpen}
        onClose={() => setConfirmDeleteMemberOpen(false)}
        dir="rtl"
      >
        <DialogTitle sx={{ bgcolor: 'error.main', color: 'white' }}>
          تأكيد حذف العضو
        </DialogTitle>
        <DialogContent sx={{ mt: 2 }}>
          <DialogContentText>
            هل أنت متأكد من رغبتك في حذف العضو "{selectedMember?.name}"؟
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ p: 2, justifyContent: 'flex-start' }}>
          <Button onClick={() => setConfirmDeleteMemberOpen(false)} color="inherit">
            إلغاء
          </Button>
          <Button 
            onClick={handleDeleteMember} 
            variant="contained" 
            color="error"
            startIcon={<DeleteIcon />}
          >
            حذف
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default FamilyDetails;
