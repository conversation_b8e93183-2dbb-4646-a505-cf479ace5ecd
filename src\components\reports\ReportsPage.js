import React, { useRef, useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  ButtonGroup
} from '@mui/material';
import {
  Print as PrintIcon,
  Assessment as ReportIcon,
  PersonOutline as PersonIcon,
  TrendingUp as IncomeIcon,
  TrendingDown as ExpenseIcon,
  AccountBalance as BalanceIcon,
  FilterList as FilterIcon,
  DateRange as DateRangeIcon,
  PictureAsPdf as PdfIcon,
  TableChart as TableIcon,
  ExpandMore as ExpandMoreIcon,
  MoneyOff as WithdrawIcon,
  CreditCard as LoanIcon,
  FamilyRestroom as FamilyIcon,
  Group as GroupIcon
} from '@mui/icons-material';
import { useReactToPrint } from 'react-to-print';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import moment from 'moment';
import momentHijri from 'moment-hijri';
import Copyright from '../common/Copyright';

const ReportsPage = ({
  members,
  income,
  expenses,
  withdrawals,
  loans,
  dateType,
  families
}) => {
  const reportRef = useRef();

  // Set default values for missing data
  const safeMembers = members || [];
  const safeIncome = income || [];
  const safeExpenses = expenses || [];
  const safeWithdrawals = withdrawals || [];
  const safeLoans = loans || [];
  const safeFamilies = families || [];

  // Calculate totals
  const totalIncomeAmount = safeIncome.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0);
  const totalExpensesAmount = safeExpenses.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0);
  const netBalance = totalIncomeAmount - totalExpensesAmount;

  // Update families with current member data
  useEffect(() => {
    if (safeFamilies.length > 0 && safeMembers.length > 0) {
      // Update family statistics based on current member data
      const updatedFamilies = safeFamilies.map(family => {
        const familyMembers = safeMembers.filter(m => m.familyId === family.id);
        const membersCount = familyMembers.length;
        const totalBalance = familyMembers.reduce((sum, m) => sum + (m.totalBalance || 0), 0);

        return {
          ...family,
          membersCount,
          totalBalance
        };
      });

      console.log('Family statistics updated:', {
        totalFamilies: safeFamilies.length,
        totalMembers: safeMembers.length,
        linkedMembers: safeMembers.filter(m => m.familyId).length,
        updatedFamilies: updatedFamilies.map(f => ({ name: f.name, members: f.membersCount, balance: f.totalBalance }))
      });
    }
  }, [safeFamilies, safeMembers]);

  // State for advanced filtering and report types
  const [reportType, setReportType] = useState('summary');
  const [filterType, setFilterType] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [selectedMember, setSelectedMember] = useState('');
  const [selectedFamily, setSelectedFamily] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [tabValue, setTabValue] = useState(0);


  
  // Format date based on type (Gregorian or Hijri)
  const formatDate = (dateString) => {
    if (dateType === 'gregorian') {
      return moment(dateString).locale('ar').format('YYYY/MM/DD');
    } else {
      return momentHijri(dateString).locale('ar').format('iYYYY/iMM/iDD');
    }
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', { 
      style: 'currency', 
      currency: 'SAR' 
    }).format(amount);
  };

  // Get dynamic report title for file naming
  const getReportTitle = () => {
    let title = '';

    switch (reportType) {
      case 'income':
        title = 'تقرير الإيرادات';
        break;
      case 'expenses':
        title = 'تقرير المصروفات';
        break;
      case 'withdrawals':
        title = 'تقرير السحوبات';
        break;
      case 'loans':
        title = 'تقرير السلف';
        break;
      case 'member':
        if (selectedMember) {
          const member = safeMembers.find(m => m.id === selectedMember);
          title = `تقرير العضو ${member?.name || 'غير معروف'}`;
        } else {
          title = 'تقرير العضو';
        }
        break;
      case 'family':
        title = 'تقرير العائلات';
        break;
      case 'unlinked':
        title = 'تقرير الأعضاء غير المرتبطين';
        break;
      case 'balance':
        title = 'تقرير ترتيب الأعضاء حسب الرصيد';
        break;
      case 'summary':
      default:
        title = 'تقرير صندوق التوفير الشامل';
        break;
    }

    // Add date filter to title if applicable
    if (dateFilter === 'today') {
      title += ' - اليوم';
    } else if (dateFilter === 'week') {
      title += ' - هذا الأسبوع';
    } else if (dateFilter === 'month') {
      title += ' - هذا الشهر';
    } else if (dateFilter === 'year') {
      title += ' - هذا العام';
    } else if (dateFilter === 'custom' && startDate && endDate) {
      title += ` - من ${startDate} إلى ${endDate}`;
    }

    return title;
  };

  // Handle print
  const handlePrint = useReactToPrint({
    content: () => reportRef.current,
    documentTitle: () => {
      const title = getReportTitle();
      return title.replace(/\s+/g, '_').replace(/[^\w\u0600-\u06FF_-]/g, '');
    },
  });

  // Export to PDF using html2canvas to preserve Arabic text
  const exportToPDF = async () => {
    try {
      const element = reportRef.current;
      if (!element) return;

      // Create canvas from the report element
      const canvas = await html2canvas(element, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: element.scrollWidth,
        height: element.scrollHeight
      });

      const imgData = canvas.toDataURL('image/png');

      // Calculate PDF dimensions
      const imgWidth = 210; // A4 width in mm
      const pageHeight = 295; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;

      const doc = new jsPDF('p', 'mm', 'a4');
      let position = 0;

      // Add first page
      doc.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      // Add additional pages if needed
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        doc.addPage();
        doc.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      // Generate dynamic filename
      const title = getReportTitle();
      const filename = title.replace(/\s+/g, '_').replace(/[^\w\u0600-\u06FF_-]/g, '') + '.pdf';

      // Save PDF
      doc.save(filename);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('حدث خطأ أثناء تصدير PDF');
    }
  };

  // Get current date for report
  const getCurrentDate = () => {
    if (dateType === 'gregorian') {
      return moment().locale('ar').format('dddd، D MMMM YYYY');
    } else {
      return momentHijri().locale('ar').format('iYYYY/iMM/iDD');
    }
  };

  // Filter data based on selected criteria
  const getFilteredData = (data, type) => {
    let filtered = [...data];

    // Date filtering
    if (dateFilter !== 'all' && startDate && endDate) {
      filtered = filtered.filter(item => {
        const itemDate = moment(item.date || item.createdAt);
        return itemDate.isBetween(startDate, endDate, 'day', '[]');
      });
    } else if (dateFilter === 'today') {
      filtered = filtered.filter(item => {
        const itemDate = moment(item.date || item.createdAt);
        return itemDate.isSame(moment(), 'day');
      });
    } else if (dateFilter === 'week') {
      filtered = filtered.filter(item => {
        const itemDate = moment(item.date || item.createdAt);
        return itemDate.isSame(moment(), 'week');
      });
    } else if (dateFilter === 'month') {
      filtered = filtered.filter(item => {
        const itemDate = moment(item.date || item.createdAt);
        return itemDate.isSame(moment(), 'month');
      });
    } else if (dateFilter === 'year') {
      filtered = filtered.filter(item => {
        const itemDate = moment(item.date || item.createdAt);
        return itemDate.isSame(moment(), 'year');
      });
    }

    // Member filtering
    if (selectedMember) {
      filtered = filtered.filter(item => item.memberId === selectedMember);
    }

    // Family filtering
    if (selectedFamily) {
      const familyMemberIds = safeMembers
        .filter(m => m.familyId === selectedFamily)
        .map(m => m.id);
      filtered = filtered.filter(item => familyMemberIds.includes(item.memberId));
    }

    return filtered;
  };

  // Get members without families
  const getUnlinkedMembers = () => {
    return safeMembers.filter(member => !member.familyId);
  };

  // Get members sorted by balance
  const getMembersByBalance = () => {
    return [...safeMembers].sort((a, b) => (b.totalBalance || 0) - (a.totalBalance || 0));
  };

  // Print specific report type
  const printSpecificReport = (type) => {
    setReportType(type);
    setTimeout(() => {
      handlePrint();
    }, 100);
  };



  return (
    <Box>
      {/* Header with Advanced Controls */}
      <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 3, '@media print': { display: 'none' } }} className="no-print">
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1" fontWeight="bold" sx={{
            fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' }
          }}>
            التقارير والإحصائيات المتقدمة
          </Typography>
          <Button
            variant="outlined"
            startIcon={<FilterIcon />}
            onClick={() => setShowFilters(!showFilters)}
            sx={{ borderRadius: 2 }}
          >
            {showFilters ? 'إخفاء المرشحات' : 'إظهار المرشحات'}
          </Button>
        </Box>

        {/* Advanced Filters */}
        {showFilters && (
          <Paper elevation={1} sx={{ p: 3, mb: 3, bgcolor: 'grey.50', borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom>مرشحات التقارير</Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>نوع التقرير</InputLabel>
                  <Select
                    value={reportType}
                    onChange={(e) => setReportType(e.target.value)}
                    label="نوع التقرير"
                  >
                    <MenuItem value="summary">ملخص عام</MenuItem>
                    <MenuItem value="income">الإيرادات فقط</MenuItem>
                    <MenuItem value="expenses">المصروفات فقط</MenuItem>
                    <MenuItem value="withdrawals">السحوبات فقط</MenuItem>
                    <MenuItem value="loans">السلف فقط</MenuItem>
                    <MenuItem value="member">ملخص عضو</MenuItem>
                    <MenuItem value="family">تقرير العائلات</MenuItem>
                    <MenuItem value="unlinked">الأعضاء غير المرتبطين</MenuItem>
                    <MenuItem value="balance">ترتيب حسب الرصيد</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>فترة التقرير</InputLabel>
                  <Select
                    value={dateFilter}
                    onChange={(e) => setDateFilter(e.target.value)}
                    label="فترة التقرير"
                  >
                    <MenuItem value="all">جميع الفترات</MenuItem>
                    <MenuItem value="today">اليوم</MenuItem>
                    <MenuItem value="week">هذا الأسبوع</MenuItem>
                    <MenuItem value="month">هذا الشهر</MenuItem>
                    <MenuItem value="year">هذا العام</MenuItem>
                    <MenuItem value="custom">فترة مخصصة</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              {dateFilter === 'custom' && (
                <>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      size="small"
                      type="date"
                      label="من تاريخ"
                      value={startDate}
                      onChange={(e) => setStartDate(e.target.value)}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      size="small"
                      type="date"
                      label="إلى تاريخ"
                      value={endDate}
                      onChange={(e) => setEndDate(e.target.value)}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                </>
              )}


            </Grid>
          </Paper>
        )}

        {/* Quick Report Buttons */}
        <Box sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          flexWrap: 'wrap',
          gap: { xs: 1, sm: 2 },
          mb: 3
        }}>
          <ButtonGroup
            variant="outlined"
            size="small"
            orientation="horizontal"
            sx={{
              flexDirection: { xs: 'column', sm: 'row' },
              '& .MuiButton-root': {
                fontSize: { xs: '0.75rem', sm: '0.875rem' },
                padding: { xs: '4px 8px', sm: '6px 16px' },
                width: { xs: '100%', sm: 'auto' }
              }
            }}
          >
            <Button startIcon={<IncomeIcon />} onClick={() => printSpecificReport('income')}>
              طباعة الإيرادات
            </Button>
            <Button startIcon={<ExpenseIcon />} onClick={() => printSpecificReport('expenses')}>
              طباعة المصروفات
            </Button>
            <Button startIcon={<WithdrawIcon />} onClick={() => printSpecificReport('withdrawals')}>
              طباعة السحوبات
            </Button>
            <Button startIcon={<LoanIcon />} onClick={() => printSpecificReport('loans')}>
              طباعة السلف
            </Button>
          </ButtonGroup>
        </Box>

        <Box sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          flexWrap: 'wrap',
          gap: { xs: 1, sm: 2 },
          mb: 3
        }}>
          <ButtonGroup
            variant="outlined"
            size="small"
            orientation="horizontal"
            sx={{
              flexDirection: { xs: 'column', sm: 'row' },
              '& .MuiButton-root': {
                fontSize: { xs: '0.75rem', sm: '0.875rem' },
                padding: { xs: '4px 8px', sm: '6px 16px' },
                width: { xs: '100%', sm: 'auto' }
              }
            }}
          >
            <Button startIcon={<PersonIcon />} onClick={() => printSpecificReport('unlinked')}>
              الأعضاء غير المرتبطين
            </Button>
            <Button startIcon={<FamilyIcon />} onClick={() => printSpecificReport('family')}>
              تقرير العائلات
            </Button>
            <Button startIcon={<BalanceIcon />} onClick={() => printSpecificReport('balance')}>
              ترتيب حسب الرصيد
            </Button>
          </ButtonGroup>
        </Box>

        {/* Member Selection for Individual Report */}
        {reportType === 'member' && (
          <Paper sx={{ p: 2, mb: 3, bgcolor: 'primary.50', border: '1px solid', borderColor: 'primary.main' }}>
            <Typography variant="h6" fontWeight="bold" mb={2} color="primary.main">
              اختيار العضو للتقرير الشخصي
            </Typography>
            <FormControl fullWidth>
              <InputLabel>اختر العضو</InputLabel>
              <Select
                value={selectedMember}
                onChange={(e) => setSelectedMember(e.target.value)}
                label="اختر العضو"
              >
                <MenuItem value="">-- اختر عضو --</MenuItem>
                {safeMembers.map(member => (
                  <MenuItem key={member.id} value={member.id}>
                    {member.name} - {formatCurrency(member.totalBalance || 0)}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Paper>
        )}

        {/* Main Action Buttons */}
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<PrintIcon />}
            onClick={handlePrint}
            sx={{ borderRadius: 2 }}
          >
            طباعة التقرير
          </Button>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<PdfIcon />}
            onClick={exportToPDF}
            sx={{ borderRadius: 2 }}
          >
            تصدير PDF
          </Button>
        </Box>
      </Paper>
      <Divider sx={{ mb: 4 }} />

      <div ref={reportRef}>
        {/* Report Header */}
        <Paper
          elevation={3}
          sx={{
            p: { xs: 1.5, sm: 2 },
            mb: { xs: 2, sm: 3 },
            borderRadius: 2,
            textAlign: 'center',
            background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)',
            color: 'white',
            '@media print': {
              p: 1,
              mb: 1,
              borderRadius: 0,
              boxShadow: 'none',
              background: '#1976d2 !important'
            }
          }}
        >
          <Typography variant="h5" fontWeight="bold" gutterBottom sx={{
            fontSize: { xs: '1.25rem', sm: '1.5rem' },
            '@media print': {
              fontSize: '1.1rem',
              mb: 0.5
            }
          }}>
            {reportType === 'income' && 'تقرير الإيرادات'}
            {reportType === 'expenses' && 'تقرير المصروفات'}
            {reportType === 'withdrawals' && 'تقرير السحوبات'}
            {reportType === 'loans' && 'تقرير السلف'}
            {reportType === 'member' && selectedMember && (() => {
              const member = safeMembers.find(m => m.id === selectedMember);
              return `تقرير العضو ${member?.name || 'غير معروف'}`;
            })()}
            {reportType === 'member' && !selectedMember && 'تقرير العضو'}
            {reportType === 'family' && 'تقرير العائلات'}
            {reportType === 'unlinked' && 'تقرير الأعضاء غير المرتبطين'}
            {reportType === 'balance' && 'تقرير ترتيب الأعضاء حسب الرصيد'}
            {reportType === 'summary' && 'تقرير صندوق التوفير الشامل'}
          </Typography>
          <Typography variant="body1" sx={{
            fontSize: { xs: '0.9rem', sm: '1rem' },
            '@media print': {
              fontSize: '0.8rem'
            }
          }}>
            {getCurrentDate()}
          </Typography>
          {(dateFilter !== 'all') && (
            <Typography variant="body2" sx={{ mt: 1, opacity: 0.9 }}>
              {dateFilter === 'today' && 'تقرير اليوم'}
              {dateFilter === 'week' && 'تقرير هذا الأسبوع'}
              {dateFilter === 'month' && 'تقرير هذا الشهر'}
              {dateFilter === 'year' && 'تقرير هذا العام'}
              {dateFilter === 'custom' && startDate && endDate && `من ${startDate} إلى ${endDate}`}
            </Typography>
          )}
        </Paper>

        {/* Summary Cards - Compact for Print */}
        <Grid container spacing={{ xs: 1, sm: 2 }} mb={{ xs: 2, sm: 3 }} sx={{
          '@media print': {
            mb: 1,
            '& .MuiGrid-item': {
              paddingTop: '4px !important',
              paddingLeft: '4px !important'
            }
          }
        }}>
          <Grid item xs={6} sm={3}>
            <Card sx={{
              height: '100%',
              borderRadius: 2,
              '@media print': {
                borderRadius: 0,
                boxShadow: '0 1px 3px rgba(0,0,0,0.2)',
                minHeight: 'auto'
              }
            }}>
              <CardContent sx={{
                textAlign: 'center',
                py: { xs: 1.5, sm: 2 },
                px: { xs: 1, sm: 2 },
                '@media print': {
                  py: 1,
                  px: 0.5,
                  '&:last-child': { pb: 1 }
                }
              }}>
                <Box
                  sx={{
                    width: { xs: 35, sm: 50 },
                    height: { xs: 35, sm: 50 },
                    bgcolor: '#bbdefb',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto',
                    mb: { xs: 1, sm: 1.5 },
                    '@media print': {
                      width: 30,
                      height: 30,
                      mb: 0.5
                    }
                  }}
                >
                  <PersonIcon sx={{
                    fontSize: { xs: 20, sm: 25 },
                    color: '#1976d2',
                    '@media print': {
                      fontSize: 16
                    }
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary" gutterBottom sx={{
                  fontSize: { xs: '0.75rem', sm: '0.875rem' },
                  '@media print': {
                    fontSize: '0.7rem',
                    mb: 0.25
                  }
                }}>
                  عدد الأعضاء
                </Typography>
                <Typography variant="h6" fontWeight="bold" sx={{
                  fontSize: { xs: '1.1rem', sm: '1.25rem' },
                  '@media print': {
                    fontSize: '0.9rem'
                  }
                }}>
                  {members.length}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={6} sm={3}>
            <Card sx={{
              height: '100%',
              borderRadius: 2,
              '@media print': {
                borderRadius: 0,
                boxShadow: '0 1px 3px rgba(0,0,0,0.2)',
                minHeight: 'auto'
              }
            }}>
              <CardContent sx={{
                textAlign: 'center',
                py: { xs: 1.5, sm: 2 },
                px: { xs: 1, sm: 2 },
                '@media print': {
                  py: 1,
                  px: 0.5,
                  '&:last-child': { pb: 1 }
                }
              }}>
                <Box
                  sx={{
                    width: { xs: 35, sm: 50 },
                    height: { xs: 35, sm: 50 },
                    bgcolor: '#c8e6c9',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto',
                    mb: { xs: 1, sm: 1.5 },
                    '@media print': {
                      width: 30,
                      height: 30,
                      mb: 0.5
                    }
                  }}
                >
                  <IncomeIcon sx={{
                    fontSize: { xs: 20, sm: 25 },
                    color: '#4caf50',
                    '@media print': {
                      fontSize: 16
                    }
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary" gutterBottom sx={{
                  fontSize: { xs: '0.75rem', sm: '0.875rem' },
                  '@media print': {
                    fontSize: '0.7rem',
                    mb: 0.25
                  }
                }}>
                  إجمالي الإيرادات
                </Typography>
                <Typography variant="h6" fontWeight="bold" color="success.main" sx={{
                  fontSize: { xs: '1.1rem', sm: '1.25rem' },
                  '@media print': {
                    fontSize: '0.9rem'
                  }
                }}>
                  {formatCurrency(totalIncomeAmount)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={6} sm={3}>
            <Card sx={{
              height: '100%',
              borderRadius: 2,
              '@media print': {
                borderRadius: 0,
                boxShadow: '0 1px 3px rgba(0,0,0,0.2)',
                minHeight: 'auto'
              }
            }}>
              <CardContent sx={{
                textAlign: 'center',
                py: { xs: 1.5, sm: 2 },
                px: { xs: 1, sm: 2 },
                '@media print': {
                  py: 1,
                  px: 0.5,
                  '&:last-child': { pb: 1 }
                }
              }}>
                <Box
                  sx={{
                    width: { xs: 35, sm: 50 },
                    height: { xs: 35, sm: 50 },
                    bgcolor: '#ffcdd2',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto',
                    mb: { xs: 1, sm: 1.5 },
                    '@media print': {
                      width: 30,
                      height: 30,
                      mb: 0.5
                    }
                  }}
                >
                  <ExpenseIcon sx={{
                    fontSize: { xs: 20, sm: 25 },
                    color: '#f44336',
                    '@media print': {
                      fontSize: 16
                    }
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary" gutterBottom sx={{
                  fontSize: { xs: '0.75rem', sm: '0.875rem' },
                  '@media print': {
                    fontSize: '0.7rem',
                    mb: 0.25
                  }
                }}>
                  إجمالي المصروفات
                </Typography>
                <Typography variant="h6" fontWeight="bold" color="error.main" sx={{
                  fontSize: { xs: '1.1rem', sm: '1.25rem' },
                  '@media print': {
                    fontSize: '0.9rem'
                  }
                }}>
                  {formatCurrency(totalExpensesAmount)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={6} sm={3}>
            <Card sx={{
              height: '100%',
              borderRadius: 2,
              '@media print': {
                borderRadius: 0,
                boxShadow: '0 1px 3px rgba(0,0,0,0.2)',
                minHeight: 'auto'
              }
            }}>
              <CardContent sx={{
                textAlign: 'center',
                py: { xs: 1.5, sm: 2 },
                px: { xs: 1, sm: 2 },
                '@media print': {
                  py: 1,
                  px: 0.5,
                  '&:last-child': { pb: 1 }
                }
              }}>
                <Box
                  sx={{
                    width: { xs: 35, sm: 50 },
                    height: { xs: 35, sm: 50 },
                    bgcolor: netBalance >= 0 ? '#e8eaf6' : '#fff3e0',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto',
                    mb: { xs: 1, sm: 1.5 },
                    '@media print': {
                      width: 30,
                      height: 30,
                      mb: 0.5
                    }
                  }}
                >
                  <BalanceIcon sx={{
                    fontSize: { xs: 20, sm: 25 },
                    color: netBalance >= 0 ? '#3f51b5' : '#ff9800',
                    '@media print': {
                      fontSize: 16
                    }
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary" gutterBottom sx={{
                  fontSize: { xs: '0.75rem', sm: '0.875rem' },
                  '@media print': {
                    fontSize: '0.7rem',
                    mb: 0.25
                  }
                }}>
                  صافي الرصيد
                </Typography>
                <Typography
                  variant="h6"
                  fontWeight="bold"
                  color={netBalance >= 0 ? 'primary.main' : 'warning.main'}
                  sx={{
                    fontSize: { xs: '1.1rem', sm: '1.25rem' },
                    '@media print': {
                      fontSize: '0.9rem'
                    }
                  }}
                >
                  {formatCurrency(netBalance)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>



        {/* Dynamic Report Content */}
        {reportType === 'income' && (
          <>
            <Typography variant="h5" fontWeight="bold" mb={2}>
              تقرير الإيرادات التفصيلي
            </Typography>

            {/* Income Summary */}
            <Paper sx={{
              p: { xs: 1.5, sm: 2 },
              mb: 3,
              bgcolor: 'success.50',
              borderRadius: 2
            }}>
              <Grid container spacing={{ xs: 1, sm: 2 }}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" sx={{
                    fontSize: { xs: '0.8rem', sm: '0.875rem' },
                    textAlign: { xs: 'center', sm: 'right' }
                  }}>
                    إجمالي الإيرادات: <strong>{formatCurrency(getFilteredData(safeIncome, 'income').reduce((sum, i) => sum + (parseFloat(i.amount) || 0), 0))}</strong>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" sx={{
                    fontSize: { xs: '0.8rem', sm: '0.875rem' },
                    textAlign: { xs: 'center', sm: 'right' }
                  }}>
                    عدد المعاملات: <strong>{getFilteredData(safeIncome, 'income').length}</strong>
                  </Typography>
                </Grid>
              </Grid>
            </Paper>

            <TableContainer component={Paper} sx={{
              borderRadius: 2,
              mb: 4,
              overflow: 'auto',
              maxWidth: '100%',
              '@media print': {
                overflow: 'visible'
              }
            }}>
              <Table sx={{
                minWidth: { xs: '100%', sm: 650 },
                tableLayout: { xs: 'auto', sm: 'fixed' }
              }}>
                <TableHead sx={{ bgcolor: '#f5f5f5' }}>
                  <TableRow>
                    <TableCell align="center" sx={{
                      fontWeight: 'bold',
                      width: { xs: '30px', sm: '50px' },
                      fontSize: { xs: '0.75rem', sm: '0.875rem' },
                      padding: { xs: '4px', sm: '8px' }
                    }}>#</TableCell>
                    <TableCell align="right" sx={{
                      fontWeight: 'bold',
                      fontSize: { xs: '0.75rem', sm: '0.875rem' },
                      padding: { xs: '4px', sm: '8px' },
                      minWidth: { xs: '80px', sm: '120px' }
                    }}>العضو</TableCell>
                    <TableCell align="right" sx={{
                      fontWeight: 'bold',
                      fontSize: { xs: '0.75rem', sm: '0.875rem' },
                      padding: { xs: '4px', sm: '8px' },
                      minWidth: { xs: '70px', sm: '100px' }
                    }}>المبلغ</TableCell>
                    <TableCell align="right" sx={{
                      fontWeight: 'bold',
                      display: { xs: 'none', md: 'table-cell' },
                      fontSize: { xs: '0.75rem', sm: '0.875rem' },
                      padding: { xs: '4px', sm: '8px' }
                    }}>البنك</TableCell>
                    <TableCell align="right" sx={{
                      fontWeight: 'bold',
                      display: { xs: 'none', sm: 'table-cell' },
                      fontSize: { xs: '0.75rem', sm: '0.875rem' },
                      padding: { xs: '4px', sm: '8px' }
                    }}>التاريخ</TableCell>
                    <TableCell align="right" sx={{
                      fontWeight: 'bold',
                      display: { xs: 'none', lg: 'table-cell' },
                      fontSize: { xs: '0.75rem', sm: '0.875rem' },
                      padding: { xs: '4px', sm: '8px' }
                    }}>ملاحظات</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {getFilteredData(safeIncome, 'income').length > 0 ? (
                    getFilteredData(safeIncome, 'income').map((transaction, index) => {
                      const member = safeMembers.find(m => m.id === transaction.memberId);
                      return (
                        <TableRow key={transaction.id} sx={{ '&:hover': { bgcolor: '#f9f9f9' } }}>
                          <TableCell align="center" sx={{
                            fontWeight: 'bold',
                            color: 'primary.main',
                            fontSize: { xs: '0.75rem', sm: '0.875rem' },
                            padding: { xs: '4px', sm: '8px' }
                          }}>
                            {index + 1}
                          </TableCell>
                          <TableCell align="right" sx={{
                            fontSize: { xs: '0.75rem', sm: '0.875rem' },
                            padding: { xs: '4px', sm: '8px' },
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            maxWidth: { xs: '80px', sm: 'none' }
                          }}>
                            {member ? member.name : 'غير معروف'}
                          </TableCell>
                          <TableCell
                            align="right"
                            sx={{
                              color: 'success.main',
                              fontWeight: 'bold',
                              fontSize: { xs: '0.75rem', sm: '0.875rem' },
                              padding: { xs: '4px', sm: '8px' }
                            }}
                          >
                            {formatCurrency(transaction.amount)}
                          </TableCell>
                          <TableCell align="right" sx={{
                            display: { xs: 'none', md: 'table-cell' },
                            fontSize: { xs: '0.75rem', sm: '0.875rem' },
                            padding: { xs: '4px', sm: '8px' }
                          }}>
                            {transaction.bank || 'غير محدد'}
                          </TableCell>
                          <TableCell align="right" sx={{
                            display: { xs: 'none', sm: 'table-cell' },
                            fontSize: { xs: '0.75rem', sm: '0.875rem' },
                            padding: { xs: '4px', sm: '8px' }
                          }}>
                            {formatDate(transaction.createdAt)}
                          </TableCell>
                          <TableCell align="right" sx={{
                            display: { xs: 'none', lg: 'table-cell' },
                            fontSize: { xs: '0.75rem', sm: '0.875rem' },
                            padding: { xs: '4px', sm: '8px' },
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            maxWidth: '100px'
                          }}>
                            {transaction.notes || '-'}
                          </TableCell>
                        </TableRow>
                      );
                    })
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                        <Typography variant="body1" color="text.secondary">
                          لا توجد إيرادات في الفترة المحددة
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </>
        )}

        {reportType === 'summary' && (
          <>
            <Typography variant="h5" fontWeight="bold" mb={2}>
              ملخص الإيرادات
            </Typography>
            <TableContainer component={Paper} sx={{ borderRadius: 2, mb: 4, overflow: 'hidden' }}>
              <Table>
                <TableHead sx={{ bgcolor: '#f5f5f5' }}>
                  <TableRow>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>العضو</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>المبلغ</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>البنك</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>التاريخ</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {getFilteredData(safeIncome, 'income').length > 0 ? (
                    getFilteredData(safeIncome, 'income').map((transaction) => {
                      const member = safeMembers.find(m => m.id === transaction.memberId);
                      return (
                        <TableRow key={transaction.id} sx={{ '&:hover': { bgcolor: '#f9f9f9' } }}>
                          <TableCell align="right">{member ? member.name : 'غير معروف'}</TableCell>
                          <TableCell
                            align="right"
                            sx={{ color: 'success.main', fontWeight: 'bold' }}
                          >
                            {formatCurrency(transaction.amount)}
                          </TableCell>
                          <TableCell align="right">{transaction.bank || 'غير محدد'}</TableCell>
                          <TableCell align="right">{formatDate(transaction.createdAt)}</TableCell>
                        </TableRow>
                      );
                    })
                  ) : (
                    <TableRow>
                      <TableCell colSpan={4} align="center" sx={{ py: 3 }}>
                        <Typography variant="body1" color="text.secondary">
                          لا توجد إيرادات حتى الآن
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </>
        )}

        {reportType === 'expenses' && (
          <>
            <Typography variant="h5" fontWeight="bold" mb={2}>
              تقرير المصروفات التفصيلي
            </Typography>

            {/* Expenses Summary */}
            <Paper sx={{ p: 2, mb: 3, bgcolor: 'error.50' }}>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2">إجمالي المصروفات: <strong>{formatCurrency(getFilteredData(safeExpenses, 'expenses').reduce((sum, e) => sum + (parseFloat(e.amount) || 0), 0))}</strong></Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2">عدد المعاملات: <strong>{getFilteredData(safeExpenses, 'expenses').length}</strong></Typography>
                </Grid>
              </Grid>
            </Paper>

            <TableContainer component={Paper} sx={{ borderRadius: 2, mb: 4, overflow: 'hidden' }}>
              <Table>
                <TableHead sx={{ bgcolor: '#f5f5f5' }}>
                  <TableRow>
                    <TableCell align="center" sx={{ fontWeight: 'bold', width: '50px' }}>#</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>المناسبة/الغرض</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>المبلغ</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>العضو المستفيد</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>التاريخ</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>ملاحظات</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {getFilteredData(safeExpenses, 'expenses').length > 0 ? (
                    getFilteredData(safeExpenses, 'expenses').map((expense, index) => {
                      const member = expense.memberId ? safeMembers.find(m => m.id === expense.memberId) : null;
                      return (
                        <TableRow key={expense.id} sx={{ '&:hover': { bgcolor: '#f9f9f9' } }}>
                          <TableCell align="center" sx={{
                            fontWeight: 'bold',
                            color: 'primary.main',
                            width: '50px'
                          }}>
                            {index + 1}
                          </TableCell>
                          <TableCell align="right">{expense.reason || 'غير محدد'}</TableCell>
                          <TableCell
                            align="right"
                            sx={{ color: 'error.main', fontWeight: 'bold' }}
                          >
                            {formatCurrency(expense.amount)}
                          </TableCell>
                          <TableCell align="right">{member ? member.name : 'مصروف عام'}</TableCell>
                          <TableCell align="right">{formatDate(expense.createdAt)}</TableCell>
                          <TableCell align="right">{expense.notes || '-'}</TableCell>
                        </TableRow>
                      );
                    })
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                        <Typography variant="body1" color="text.secondary">
                          لا توجد مصروفات في الفترة المحددة
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </>
        )}

        {reportType === 'summary' && (
          <>
            <Typography variant="h5" fontWeight="bold" mb={2}>
              ملخص المصروفات
            </Typography>
            <TableContainer component={Paper} sx={{ borderRadius: 2, mb: 4, overflow: 'hidden' }}>
              <Table>
                <TableHead sx={{ bgcolor: '#f5f5f5' }}>
                  <TableRow>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>المناسبة/الغرض</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>المبلغ</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>العضو</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>التاريخ</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {getFilteredData(safeExpenses, 'expenses').length > 0 ? (
                    getFilteredData(safeExpenses, 'expenses').map((expense) => {
                      const member = expense.memberId ? safeMembers.find(m => m.id === expense.memberId) : null;
                      return (
                        <TableRow key={expense.id} sx={{ '&:hover': { bgcolor: '#f9f9f9' } }}>
                          <TableCell align="right">{expense.reason || 'غير محدد'}</TableCell>
                          <TableCell
                            align="right"
                            sx={{ color: 'error.main', fontWeight: 'bold' }}
                          >
                            {formatCurrency(expense.amount)}
                          </TableCell>
                          <TableCell align="right">{member ? member.name : '-'}</TableCell>
                          <TableCell align="right">{formatDate(expense.createdAt)}</TableCell>
                        </TableRow>
                      );
                    })
                  ) : (
                    <TableRow>
                      <TableCell colSpan={4} align="center" sx={{ py: 3 }}>
                        <Typography variant="body1" color="text.secondary">
                          لا توجد مصروفات حتى الآن
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </>
        )}

        {(reportType === 'summary' || reportType === 'withdrawals') && (
          <>
            <Typography variant="h5" fontWeight="bold" mb={2}>
              {reportType === 'withdrawals' ? 'تقرير السحوبات' : 'ملخص السحوبات'}
            </Typography>
            <TableContainer component={Paper} sx={{ borderRadius: 2, mb: 4, overflow: 'hidden' }}>
              <Table>
                <TableHead sx={{ bgcolor: '#f5f5f5' }}>
                  <TableRow>
                    <TableCell align="center" sx={{ fontWeight: 'bold', width: '50px' }}>#</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>العضو</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>المبلغ</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>ملاحظات</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>التاريخ</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {getFilteredData(withdrawals, 'withdrawals').length > 0 ? (
                    getFilteredData(withdrawals, 'withdrawals').map((withdrawal, index) => {
                      const member = members.find(m => m.id === withdrawal.memberId);
                      return (
                        <TableRow key={withdrawal.id} sx={{ '&:hover': { bgcolor: '#f9f9f9' } }}>
                          <TableCell align="center" sx={{
                            fontWeight: 'bold',
                            color: 'primary.main',
                            width: '50px'
                          }}>
                            {index + 1}
                          </TableCell>
                          <TableCell align="right">{member ? member.name : 'غير معروف'}</TableCell>
                          <TableCell
                            align="right"
                            sx={{ color: 'warning.main', fontWeight: 'bold' }}
                          >
                            {formatCurrency(withdrawal.amount)}
                          </TableCell>
                          <TableCell align="right">{withdrawal.notes || '-'}</TableCell>
                          <TableCell align="right">{formatDate(withdrawal.date)}</TableCell>
                        </TableRow>
                      );
                    })
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                        <Typography variant="body1" color="text.secondary">
                          لا توجد سحوبات حتى الآن
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </>
        )}

        {(reportType === 'summary' || reportType === 'loans') && (
          <>
            <Typography variant="h5" fontWeight="bold" mb={2}>
              {reportType === 'loans' ? 'تقرير السلف' : 'ملخص السلف'}
            </Typography>
            <TableContainer component={Paper} sx={{ borderRadius: 2, mb: 4, overflow: 'hidden' }}>
              <Table>
                <TableHead sx={{ bgcolor: '#f5f5f5' }}>
                  <TableRow>
                    <TableCell align="center" sx={{ fontWeight: 'bold', width: '50px' }}>#</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>العضو</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>المبلغ</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>تاريخ الاستحقاق</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>ملاحظات</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>التاريخ</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {getFilteredData(loans, 'loans').length > 0 ? (
                    getFilteredData(loans, 'loans').map((loan, index) => {
                      const member = members.find(m => m.id === loan.memberId);
                      return (
                        <TableRow key={loan.id} sx={{ '&:hover': { bgcolor: '#f9f9f9' } }}>
                          <TableCell align="center" sx={{
                            fontWeight: 'bold',
                            color: 'primary.main',
                            width: '50px'
                          }}>
                            {index + 1}
                          </TableCell>
                          <TableCell align="right">{member ? member.name : 'غير معروف'}</TableCell>
                          <TableCell
                            align="right"
                            sx={{ color: 'info.main', fontWeight: 'bold' }}
                          >
                            {formatCurrency(loan.amount)}
                          </TableCell>
                          <TableCell align="right">{formatDate(loan.dueDate)}</TableCell>
                          <TableCell align="right">{loan.notes || '-'}</TableCell>
                          <TableCell align="right">{formatDate(loan.date)}</TableCell>
                        </TableRow>
                      );
                    })
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                        <Typography variant="body1" color="text.secondary">
                          لا توجد سلف حتى الآن
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </>
        )}

        {reportType === 'unlinked' && (
          <>
            <Typography variant="h5" fontWeight="bold" mb={2}>
              الأعضاء غير المرتبطين بعائلات
            </Typography>
            <TableContainer component={Paper} sx={{ borderRadius: 2, mb: 4, overflow: 'hidden' }}>
              <Table>
                <TableHead sx={{ bgcolor: '#f5f5f5' }}>
                  <TableRow>
                    <TableCell align="center" sx={{ fontWeight: 'bold', width: '50px' }}>#</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>الاسم</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>الهاتف</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>تاريخ الانضمام</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>الرصيد</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {getUnlinkedMembers().length > 0 ? (
                    getUnlinkedMembers().map((member, index) => (
                      <TableRow key={member.id} sx={{ '&:hover': { bgcolor: '#f9f9f9' } }}>
                        <TableCell align="center" sx={{
                          fontWeight: 'bold',
                          color: 'primary.main',
                          width: '50px'
                        }}>
                          {index + 1}
                        </TableCell>
                        <TableCell align="right">{member.name}</TableCell>
                        <TableCell align="right">{member.phone || 'غير متوفر'}</TableCell>
                        <TableCell align="right">{formatDate(member.createdAt)}</TableCell>
                        <TableCell
                          align="right"
                          sx={{
                            color: member.totalBalance >= 0 ? 'success.main' : 'error.main',
                            fontWeight: 'bold'
                          }}
                        >
                          {formatCurrency(member.totalBalance)}
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                        <Typography variant="body1" color="text.secondary">
                          جميع الأعضاء مرتبطون بعائلات
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </>
        )}

        {reportType === 'balance' && (
          <>
            <Typography variant="h5" fontWeight="bold" mb={2}>
              ترتيب الأعضاء حسب الرصيد
            </Typography>
            <TableContainer component={Paper} sx={{ borderRadius: 2, mb: 4, overflow: 'hidden' }}>
              <Table>
                <TableHead sx={{ bgcolor: '#f5f5f5' }}>
                  <TableRow>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>الترتيب</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>الاسم</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>العائلة</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>الرصيد</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {getMembersByBalance().map((member, index) => {
                    const family = families?.find(f => f.id === member.familyId);
                    return (
                      <TableRow key={member.id} sx={{ '&:hover': { bgcolor: '#f9f9f9' } }}>
                        <TableCell align="right">
                          <Chip
                            label={index + 1}
                            size="small"
                            color={index < 3 ? 'primary' : 'default'}
                          />
                        </TableCell>
                        <TableCell align="right">{member.name}</TableCell>
                        <TableCell align="right">{family?.name || 'غير مرتبط'}</TableCell>
                        <TableCell
                          align="right"
                          sx={{
                            color: member.totalBalance > 0 ? 'success.main' :
                                   member.totalBalance < 0 ? 'error.main' :
                                   'text.primary',
                            fontWeight: 'bold',
                            fontSize: '1rem',
                            textShadow: '0 1px 2px rgba(0,0,0,0.1)'
                          }}
                        >
                          {formatCurrency(member.totalBalance)}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </>
        )}

        {reportType === 'family' && (
          <>
            <Typography variant="h5" fontWeight="bold" mb={2}>
              تقرير العائلات التفصيلي
            </Typography>

            {/* Debug Info */}
            <Paper sx={{ p: 2, mb: 2, bgcolor: 'info.50', border: '1px solid', borderColor: 'info.main' }}>
              <Typography variant="body2">
                <strong>إحصائيات عامة:</strong><br/>
                عدد العائلات المسجلة: {safeFamilies.length}<br/>
                عدد الأعضاء الكلي: {safeMembers.length}<br/>
                عدد الأعضاء المرتبطين بعائلات: {safeMembers.filter(m => m.familyId).length}<br/>
                عدد الأعضاء غير المرتبطين: {safeMembers.filter(m => !m.familyId).length}
              </Typography>
            </Paper>

            {safeFamilies.length > 0 ? (
              <>
                {/* Overall Family Statistics */}
                <Paper sx={{ p: 2, mb: 3, bgcolor: 'info.50' }}>
                  <Grid container spacing={2}>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2">عدد العائلات: <strong>{safeFamilies.length}</strong></Typography>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2">إجمالي الأفراد: <strong>{safeMembers.filter(m => m.familyId).length}</strong></Typography>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2">الأعضاء غير المرتبطين: <strong>{safeMembers.filter(m => !m.familyId).length}</strong></Typography>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2">إجمالي الأرصدة: <strong>{formatCurrency(safeMembers.reduce((sum, m) => sum + (m.totalBalance || 0), 0))}</strong></Typography>
                    </Grid>
                  </Grid>
                </Paper>
              </>
            ) : (
              <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'grey.50' }}>
                <Typography variant="h6" color="text.secondary" mb={2}>
                  لا توجد عائلات مسجلة حتى الآن
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  يرجى إضافة عائلات من قسم إدارة العائلات أولاً
                </Typography>
              </Paper>
            )}

            {safeFamilies.length > 0 && safeFamilies.map((family) => {
              const familyMembers = safeMembers.filter(m => m.familyId === family.id);
              const familyBalance = familyMembers.reduce((sum, m) => sum + (m.totalBalance || 0), 0);
              const familyIncome = familyMembers.reduce((sum, m) => {
                const memberIncome = safeIncome.filter(i => i.memberId === m.id).reduce((s, i) => s + (parseFloat(i.amount) || 0), 0);
                return sum + memberIncome;
              }, 0);
              const familyExpenses = familyMembers.reduce((sum, m) => {
                const memberExpenses = safeExpenses.filter(e => e.memberId === m.id).reduce((s, e) => s + (parseFloat(e.amount) || 0), 0);
                return sum + memberExpenses;
              }, 0);

              return (
                <Paper key={family.id} sx={{ mb: 3, p: 2, borderRadius: 2, border: '1px solid #e0e0e0' }}>
                  <Typography variant="h6" fontWeight="bold" mb={2} color="primary" sx={{ borderBottom: '2px solid', borderColor: 'primary.main', pb: 1 }}>
                    عائلة {family.name}
                  </Typography>

                  {/* Family Info */}
                  <Grid container spacing={2} mb={2}>
                    <Grid item xs={12} sm={6} md={3}>
                      <Typography variant="body2"><strong>رب الأسرة:</strong> {family.headName || 'غير محدد'}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <Typography variant="body2"><strong>عدد الأفراد:</strong> {familyMembers.length}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <Typography variant="body2"><strong>الهاتف:</strong> {family.phone || 'غير متوفر'}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <Typography variant="body2"><strong>تاريخ التسجيل:</strong> {formatDate(family.createdAt)}</Typography>
                    </Grid>
                  </Grid>

                  {/* Family Financial Summary */}
                  <Grid container spacing={2} mb={2}>
                    <Grid item xs={6} sm={3}>
                      <Box sx={{ textAlign: 'center', p: 1, bgcolor: 'success.50', borderRadius: 1 }}>
                        <Typography variant="caption" color="success.main">إجمالي الإيرادات</Typography>
                        <Typography variant="body2" fontWeight="bold" color="success.main">
                          {formatCurrency(familyIncome)}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Box sx={{ textAlign: 'center', p: 1, bgcolor: 'error.50', borderRadius: 1 }}>
                        <Typography variant="caption" color="error.main">إجمالي المصروفات</Typography>
                        <Typography variant="body2" fontWeight="bold" color="error.main">
                          {formatCurrency(familyExpenses)}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Box sx={{ textAlign: 'center', p: 1, bgcolor: familyBalance >= 0 ? 'primary.50' : 'warning.50', borderRadius: 1 }}>
                        <Typography variant="caption" color={familyBalance >= 0 ? 'primary.main' : 'warning.main'}>إجمالي رصيد العائلة</Typography>
                        <Typography
                          variant="h6"
                          fontWeight="bold"
                          color={familyBalance >= 0 ? 'primary.main' : 'warning.main'}
                        >
                          {formatCurrency(familyBalance)}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>

                  {/* Family Members Details */}
                  {familyMembers.length > 0 && (
                    <>
                      <Typography variant="subtitle1" fontWeight="bold" mb={1} color="text.primary">
                        أفراد العائلة:
                      </Typography>
                      <TableContainer>
                        <Table size="small">
                          <TableHead sx={{ bgcolor: 'grey.100' }}>
                            <TableRow>
                              <TableCell align="center" sx={{ fontWeight: 'bold', width: '50px' }}>#</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold' }}>الاسم</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold' }}>الهاتف</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold' }}>تاريخ الانضمام</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold' }}>الرصيد الشخصي</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {familyMembers.map((member, index) => (
                              <TableRow key={member.id} sx={{ '&:hover': { bgcolor: 'grey.50' } }}>
                                <TableCell align="center" sx={{
                                  fontWeight: 'bold',
                                  color: 'primary.main',
                                  width: '50px'
                                }}>
                                  {index + 1}
                                </TableCell>
                                <TableCell align="right">{member.name}</TableCell>
                                <TableCell align="right">{member.phone || 'غير متوفر'}</TableCell>
                                <TableCell align="right">{formatDate(member.createdAt)}</TableCell>
                                <TableCell
                                  align="right"
                                  sx={{
                                    color: member.totalBalance > 0 ? 'success.main' :
                                           member.totalBalance < 0 ? 'error.main' :
                                           'text.primary',
                                    fontWeight: 'bold',
                                    fontSize: '1rem',
                                    textShadow: '0 1px 2px rgba(0,0,0,0.1)'
                                  }}
                                >
                                  {formatCurrency(member.totalBalance)}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </>
                  )}
                </Paper>
              );
            })}
          </>
        )}

        {reportType === 'member' && selectedMember && (
          <>
            {(() => {
              const member = safeMembers.find(m => m.id === selectedMember);
              if (!member) return (
                <Paper sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant="h6" color="text.secondary">
                    العضو المحدد غير موجود
                  </Typography>
                </Paper>
              );

              const memberIncome = getFilteredData(safeIncome.filter(i => i.memberId === selectedMember), 'income');
              const memberExpenses = getFilteredData(safeExpenses.filter(e => e.memberId === selectedMember), 'expenses');
              const memberWithdrawals = getFilteredData(safeWithdrawals.filter(w => w.memberId === selectedMember), 'withdrawals');
              const memberLoans = getFilteredData(safeLoans.filter(l => l.memberId === selectedMember), 'loans');

              const totalIncome = memberIncome.reduce((sum, i) => sum + (parseFloat(i.amount) || 0), 0);
              const totalExpenses = memberExpenses.reduce((sum, e) => sum + (parseFloat(e.amount) || 0), 0);
              const totalWithdrawals = memberWithdrawals.reduce((sum, w) => sum + (parseFloat(w.amount) || 0), 0);
              const totalLoans = memberLoans.reduce((sum, l) => sum + (parseFloat(l.amount) || 0), 0);

              return (
                <>
                  <Typography variant="h5" fontWeight="bold" mb={2}>
                    تقرير العضو الشامل: {member.name}
                  </Typography>

                  {/* Member Basic Info */}
                  <Paper sx={{ p: 2, mb: 3, bgcolor: 'primary.50', border: '1px solid', borderColor: 'primary.main' }}>
                    <Typography variant="h6" fontWeight="bold" mb={2} color="primary.main">
                      المعلومات الأساسية
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6} md={3}>
                        <Typography variant="body2"><strong>الاسم:</strong> {member.name}</Typography>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Typography variant="body2"><strong>الهاتف:</strong> {member.phone || 'غير متوفر'}</Typography>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Typography variant="body2"><strong>تاريخ الانضمام:</strong> {formatDate(member.createdAt)}</Typography>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Typography variant="body2"><strong>العائلة:</strong> {safeFamilies?.find(f => f.id === member.familyId)?.name || 'غير مرتبط'}</Typography>
                      </Grid>
                    </Grid>
                  </Paper>

                  {/* Financial Summary Cards */}
                  <Grid container spacing={2} mb={3}>
                    <Grid item xs={6} sm={3}>
                      <Card sx={{ textAlign: 'center', p: 2, bgcolor: 'success.50', border: '1px solid', borderColor: 'success.main' }}>
                        <Typography variant="h5" color="success.main" fontWeight="bold">
                          {formatCurrency(totalIncome)}
                        </Typography>
                        <Typography variant="body2" color="success.main">إجمالي الإيرادات</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {memberIncome.length} معاملة
                        </Typography>
                      </Card>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Card sx={{ textAlign: 'center', p: 2, bgcolor: 'error.50', border: '1px solid', borderColor: 'error.main' }}>
                        <Typography variant="h5" color="error.main" fontWeight="bold">
                          {formatCurrency(totalExpenses)}
                        </Typography>
                        <Typography variant="body2" color="error.main">إجمالي المصروفات</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {memberExpenses.length} معاملة
                        </Typography>
                      </Card>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Card sx={{ textAlign: 'center', p: 2, bgcolor: 'warning.50', border: '1px solid', borderColor: 'warning.main' }}>
                        <Typography variant="h5" color="warning.main" fontWeight="bold">
                          {formatCurrency(totalWithdrawals)}
                        </Typography>
                        <Typography variant="body2" color="warning.main">إجمالي السحوبات</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {memberWithdrawals.length} معاملة
                        </Typography>
                      </Card>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Card sx={{ textAlign: 'center', p: 2, bgcolor: 'info.50', border: '1px solid', borderColor: 'info.main' }}>
                        <Typography variant="h5" color="info.main" fontWeight="bold">
                          {formatCurrency(totalLoans)}
                        </Typography>
                        <Typography variant="body2" color="info.main">إجمالي السلف</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {memberLoans.length} معاملة
                        </Typography>
                      </Card>
                    </Grid>
                  </Grid>

                  {/* Final Balance */}
                  <Paper sx={{
                    p: 2,
                    mb: 3,
                    textAlign: 'center',
                    bgcolor: member.totalBalance >= 0 ? 'success.50' : 'error.50',
                    border: '2px solid',
                    borderColor: member.totalBalance >= 0 ? 'success.main' : 'error.main'
                  }}>
                    <Typography variant="h6" fontWeight="bold" mb={1}>
                      الرصيد النهائي للعضو
                    </Typography>
                    <Typography
                      variant="h4"
                      fontWeight="bold"
                      color={member.totalBalance >= 0 ? 'success.main' : 'error.main'}
                    >
                      {formatCurrency(member.totalBalance)}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      (الإيرادات - المصروفات - السحوبات - السلف)
                    </Typography>
                  </Paper>

                  {/* Detailed Transactions */}
                  {memberIncome.length > 0 && (
                    <>
                      <Typography variant="h6" fontWeight="bold" mb={2} color="success.main">
                        تفاصيل الإيرادات ({memberIncome.length})
                      </Typography>
                      <TableContainer component={Paper} sx={{ mb: 3 }}>
                        <Table size="small">
                          <TableHead sx={{ bgcolor: 'success.50' }}>
                            <TableRow>
                              <TableCell align="right" sx={{ fontWeight: 'bold' }}>المبلغ</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold' }}>البنك</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold' }}>التاريخ</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold' }}>ملاحظات</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {memberIncome.map((transaction) => (
                              <TableRow key={transaction.id}>
                                <TableCell align="right" sx={{ color: 'success.main', fontWeight: 'bold' }}>
                                  {formatCurrency(transaction.amount)}
                                </TableCell>
                                <TableCell align="right">{transaction.bank || 'غير محدد'}</TableCell>
                                <TableCell align="right">{formatDate(transaction.createdAt)}</TableCell>
                                <TableCell align="right">{transaction.notes || '-'}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </>
                  )}

                  {memberExpenses.length > 0 && (
                    <>
                      <Typography variant="h6" fontWeight="bold" mb={2} color="error.main">
                        تفاصيل المصروفات ({memberExpenses.length})
                      </Typography>
                      <TableContainer component={Paper} sx={{ mb: 3 }}>
                        <Table size="small">
                          <TableHead sx={{ bgcolor: 'error.50' }}>
                            <TableRow>
                              <TableCell align="right" sx={{ fontWeight: 'bold' }}>المبلغ</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold' }}>السبب</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold' }}>التاريخ</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold' }}>ملاحظات</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {memberExpenses.map((expense) => (
                              <TableRow key={expense.id}>
                                <TableCell align="right" sx={{ color: 'error.main', fontWeight: 'bold' }}>
                                  {formatCurrency(expense.amount)}
                                </TableCell>
                                <TableCell align="right">{expense.reason || 'غير محدد'}</TableCell>
                                <TableCell align="right">{formatDate(expense.createdAt)}</TableCell>
                                <TableCell align="right">{expense.notes || '-'}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </>
                  )}

                  {memberWithdrawals.length > 0 && (
                    <>
                      <Typography variant="h6" fontWeight="bold" mb={2} color="warning.main">
                        تفاصيل السحوبات ({memberWithdrawals.length})
                      </Typography>
                      <TableContainer component={Paper} sx={{ mb: 3 }}>
                        <Table size="small">
                          <TableHead sx={{ bgcolor: 'warning.50' }}>
                            <TableRow>
                              <TableCell align="right" sx={{ fontWeight: 'bold' }}>المبلغ</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold' }}>التاريخ</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold' }}>ملاحظات</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {memberWithdrawals.map((withdrawal) => (
                              <TableRow key={withdrawal.id}>
                                <TableCell align="right" sx={{ color: 'warning.main', fontWeight: 'bold' }}>
                                  {formatCurrency(withdrawal.amount)}
                                </TableCell>
                                <TableCell align="right">{formatDate(withdrawal.date)}</TableCell>
                                <TableCell align="right">{withdrawal.notes || '-'}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </>
                  )}

                  {memberLoans.length > 0 && (
                    <>
                      <Typography variant="h6" fontWeight="bold" mb={2} color="info.main">
                        تفاصيل السلف ({memberLoans.length})
                      </Typography>
                      <TableContainer component={Paper} sx={{ mb: 3 }}>
                        <Table size="small">
                          <TableHead sx={{ bgcolor: 'info.50' }}>
                            <TableRow>
                              <TableCell align="right" sx={{ fontWeight: 'bold' }}>المبلغ</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold' }}>تاريخ الاستحقاق</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold' }}>التاريخ</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold' }}>ملاحظات</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {memberLoans.map((loan) => (
                              <TableRow key={loan.id}>
                                <TableCell align="right" sx={{ color: 'info.main', fontWeight: 'bold' }}>
                                  {formatCurrency(loan.amount)}
                                </TableCell>
                                <TableCell align="right">{formatDate(loan.dueDate)}</TableCell>
                                <TableCell align="right">{formatDate(loan.date)}</TableCell>
                                <TableCell align="right">{loan.notes || '-'}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </>
                  )}
                </>
              );
            })()}
          </>
        )}

        {reportType === 'member' && !selectedMember && (
          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'grey.50' }}>
            <Typography variant="h6" color="text.secondary" mb={2}>
              يرجى اختيار عضو من المرشحات أعلاه لعرض تقريره
            </Typography>
            <Typography variant="body2" color="text.secondary">
              استخدم قائمة "اختر العضو" في المرشحات لتحديد العضو المطلوب
            </Typography>
          </Paper>
        )}

        {/* Copyright */}
        <Copyright variant="compact" />
      </div>
    </Box>
  );
};

export default ReportsPage;
