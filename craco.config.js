module.exports = {
  webpack: {
    configure: (webpackConfig, { env, paths }) => {
      // منع التخزين المؤقت في التطوير
      if (env === 'development') {
        webpackConfig.output.filename = 'static/js/[name].js';
        webpackConfig.output.chunkFilename = 'static/js/[name].chunk.js';
        
        // إضافة headers لمنع التخزين المؤقت
        webpackConfig.devServer = {
          ...webpackConfig.devServer,
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        };
      }
      
      return webpackConfig;
    }
  }
};
